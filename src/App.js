import React, { Suspense } from 'react';
import { Toaster } from 'sonner';
import Router from './router/Router';
import Spinner from './@core/components/spinner/Fallback-spinner';
import { useLoading } from './contexts/LoadingContext';

const App = () => {
    const { loading } = useLoading();
    return (
        <Suspense fallback={<Spinner />}>
            {loading && <Spinner />}
            <Toaster
                visibleToasts={5}
            />
            <Router />
        </Suspense>
    )
};

export default App;