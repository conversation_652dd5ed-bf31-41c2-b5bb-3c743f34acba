import { lazy } from 'react';

const BIDVDashboardPage = lazy(() => import('../../views/security/bidv/dashboard'));
const BIDVHistoryDetailPage = lazy(() => import('../../views/security/bidv/history-detail'));
const BIDVMeetingPage = lazy(() => import('../../views/security/bidv/meeting'));
const BIDVMeetingDetailPage = lazy(() => import('../../views/security/bidv/meeting-detail'));
const BIDVLiveViewPage = lazy(() => import('../../views/security/bidv/live-view'));

const BIDVRoutes = [
    {
        path: '/bidv/dashboard',
        meta: {
            action: 'read',
            resource: 'DASHBOARD',
        },
        element: <BIDVDashboardPage />,
    },
    {
        path: '/bidv/history-detail',
        meta: {
            action: 'read',
            resource: 'DASHBOARD',
        },
        element: <BIDVHistoryDetailPage />,
    },
    {
        path: '/bidv/meeting',
        element: <BIDVMeetingPage />,
    },
    {
        path: '/bidv/meeting-detail',
        element: <BIDVMeetingDetailPage />,
    },
    {
        path: '/bidv/live-view',
        element: <BIDVLiveViewPage />,
    },
];

export default BIDVRoutes;