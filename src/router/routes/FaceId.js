import { lazy } from 'react'
import FakeResultPage from '../../views/faceid/fake-result'

const FaceIdReportPage = lazy(() => import('../../views/faceid/report'))
const TimekeepingPage = lazy(() => import('../../views/faceid/check-in-out-data'))
const AttendantPage = lazy(() => import('../../views/faceid/attendant'))
const FaceIdUnknownPage = lazy(() => import('../../views/faceid/timekeeping-unknown'))
const ReIDPage = lazy(() => import('../../views/faceid/reid'))
const FaceResultPage = lazy(() => import('../../views/faceid/face-result'))
const FaceIntergrationPage = lazy(() => import('../../views/faceid/face-intergration'))
const AgeAndEmotionPage = lazy(() => import('../../views/faceid/age-emotion'))

const FaceIdRoutes = [
    {
        path: '/faceid/check-in-out-data',
        element: <TimekeepingPage />,
        meta: {
            action: 'read',
            resource: 'USERS',
        }
    },
    {
        path: '/faceid/timekeeping_unknown',
        meta: {
            action: 'read',
            resource: 'ORPHAN_RESULTS',
        },
        element: <FaceIdUnknownPage />
    },
    {
        path: '/faceid/attendant',
        meta: {
            action: 'manage',
            resource: 'USERS',
        },
        element: <AttendantPage />
    },
    {
        path: '/faceid/face-result',
        meta: {
            action: 'read',
            resource: 'USERS',
        },
        element: <FaceResultPage />
    },
    {
        path: '/faceid/report',
        meta: {
            action: 'manage',
            resource: 'USERS',
        },
        element: <FaceIdReportPage />
    },
    {
        path: '/faceid/fake-result',
        element: <FakeResultPage />
    },
    {
        path: '/faceid/face-intergration',
        element: <FaceIntergrationPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/faceid/age-emotion',
        meta: {
            action: 'read',
            resource: 'ORPHAN_RESULTS',
        },
        element: <AgeAndEmotionPage />
    },
    {
        path: '/faceid/reid',
        meta: {
            action: 'read',
            resource: 'ORPHAN_RESULTS',
        },
        element: <ReIDPage />
    },
]

export default FaceIdRoutes