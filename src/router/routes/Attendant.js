import { lazy } from 'react'

const AttendantAbsentPage = lazy(() => import('../../views/attendant/by-day'))
const AttPage = lazy(() => import('../../views/attendant/att'))

const AttendantRoutes = [
    {
        path: '/attendant/att',
        meta: {
            action: 'manage',
            resource: 'ATT',
        },
        element: <AttPage />,
    },
    // {
    //     path: '/attendant/history',
    //     meta: {
    //         action: 'manage',
    //         resource: 'ATT',
    //     },
    //     element: <AttendantAbsentPage />,
    // },
    {
        path: '/attendant/by-day',
        meta: {
            action: 'manage',
            resource: 'ATT',
        },
        element: <AttendantAbsentPage />,
    },
]

export default AttendantRoutes