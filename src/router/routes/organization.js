import { lazy } from 'react'

const OrganizationTreeViewPage = lazy(() => import('../../views/organization/tree-view'))
const OrganizationAdminPage = lazy(() => import('../../views/organization/organization-admin'))
const AllOrganizationPage = lazy(() => import('../../views/organization/all-org'))
const CompanyViewPage = lazy(() => import('../../views/organization/company-view'))
const CompanyManagementPage = lazy(() => import('../../views/organization/company-management'))
const LocationPage = lazy(() => import('../../views/organization/location'))

const OrganizationRoutes = [
    // {
    //     path: '/organization/tree-view',
    //     meta: {
    //         action: 'read',
    //         resource: 'DASHBOARD',
    //     },
    //     element: <DashboardReport />
    // },
    {
        path: '/organization/all-org',
        meta: {
            action: 'read',
            resource: 'ORGANIZATION',
        },
        element: <AllOrganizationPage />
    },
    {
        path: '/organization/tree-view',
        meta: {
            action: 'read',
            resource: 'ORGANIZATION',
        },
        element: <OrganizationTreeViewPage />
    },
    {
        path: '/organization/company-view',
        meta: {
            action: 'read',
            resource: 'ORGANIZATION',
        },
        element: <CompanyViewPage />
    },
    {
        path: '/organization/company-management',
        meta: {
            action: 'manage',
            resource: 'ORGANIZATION',
        },
        element: <CompanyManagementPage />
    },
    {
        path: '/organization/organization-admin',
        meta: {
            action: 'manage',
            resource: 'ORGANIZATION',
        },
        element: <OrganizationAdminPage />
    },
    {
        path: '/organization/location',
        meta: {
            action: 'manage',
            resource: 'ORGANIZATION',
        },
        element: <LocationPage />
    },
]

export default OrganizationRoutes