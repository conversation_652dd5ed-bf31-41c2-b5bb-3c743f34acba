import { lazy } from 'react';

const EventVideoPage = lazy(() => import('../../views/videos/event-video'));
const VideoAnalyticPage = lazy(() => import('../../views/videos/video-analytic'));
const VideoAnalyticGroupPage = lazy(() => import('../../views/videos/video-analytic-group'));


const VideosRoutes = [
    {
        path: '/videos/event-video',
        element: <EventVideoPage />,
    },
    {
        path: '/videos/video-analytic',
        element: <VideoAnalyticPage />,
    },
    {
        path: '/videos/video-analytic-group',
        element: <VideoAnalyticGroupPage />,
    },
];

export default VideosRoutes;