import { lazy } from 'react'
import FakeResultPage from '../../views/faceid/fake-result'
// import WorktimeReportPage from '../../views/shift-management/worktime-report'

const SchoolPage = lazy(() => import('../../views/school/all-students'))
const FaceIdReportPage = lazy(() => import('../../views/faceid/report'))
const TimekeepingPage = lazy(() => import('../../views/faceid/check-in-out-data'))
const FaceIdUnknownPage = lazy(() => import('../../views/faceid/timekeeping-unknown'))
const FaceResultPage = lazy(() => import('../../views/faceid/face-result'))
const ShiftTypePage = lazy(() => import('../../views/shift-management/all-shift-type'))
const ShiftAssignmentPage = lazy(() => import('../../views/shift-management/shift-assignment'))
const PatientPage = lazy(() => import('../../views/medical/patients'))
const PatientAccessPage = lazy(() => import('../../views/medical/access'))
const HistoryPage = lazy(() => import('../../views/attendant/history'))
const AttendantTimePage = lazy(() => import('../../views/attendant/att-time'))
const UserConfigPage = lazy(() => import('../../views/attendant/user-config'))
const ShiftReportPage = lazy(() => import('../../views/shift-management/shift-report'))
const GuestPage = lazy(() => import('../../views/guest/guests'))
const GuestAccessPage = lazy(() => import('../../views/guest/access'))
const FaceResultGuestPage = lazy(() => import('../../views/guest/history'))
const AdminShiftExplanationPage = lazy(() => import('../../views/shift-management/admin-shift-explanation'))

const ShiftManagementRoutes = [
    {
        path: '/shift-management/all-shift-type',
        element: <ShiftTypePage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/shift-management/shift-assignment',
        element: <ShiftAssignmentPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/shift-management/admin-shift-explanation',
        element: <AdminShiftExplanationPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/shift-management/shift-report',
        element: <ShiftReportPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    // {
    //     path: '/shift-management/worktime-report',
    //     element: <WorktimeReportPage />,
    //     meta: {
    //         action: 'manage',
    //         resource: 'USERS',
    //     }
    // },
    {
        path: '/attendant/history',
        element: <HistoryPage />
    },
    {
        path: '/attendant/att-time',
        element: <AttendantTimePage />
    },
    {
        path: '/attendant/user-config',
        element: <UserConfigPage />
    },
    {
        path: '/guest/guests',
        element: <GuestPage />,
        meta: {
            action: 'manage',
            resource: 'USER_GUESTS',
        }
    },
    {
        path: '/guest/access',
        element: <GuestAccessPage />,
        meta: {
            action: 'manage',
            resource: 'USER_GUESTS',
        }
    },
    {
        path: '/guest/history',
        element: <FaceResultGuestPage />,
        meta: {
            action: 'manage',
            resource: 'USER_GUESTS',
        }
    },
    {
        path: '/school/all-students',
        element: <SchoolPage />
    },
    {
        path: '/medical/access',
        element: <PatientAccessPage />
    },
    {
        path: '/medical/patients',
        element: <PatientPage />
    },
]

export default ShiftManagementRoutes