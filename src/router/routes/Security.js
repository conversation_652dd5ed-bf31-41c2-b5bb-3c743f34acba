import { lazy } from 'react';

const AccesControlPage = lazy(() => import('../../views/security/access-control'))
const ACAlertPage = lazy(() => import('../../views/security/access-control-alert'))
const ACBetaPage = lazy(() => import('../../views/security/access-control-beta'))
const ACMbPage = lazy(() => import('../../views/security/access-control-mb'))
const ACLogPage = lazy(() => import('../../views/security/door-log'))
const VideoManagementTable = lazy(() => import('../../views/security/event-video'))
const LiveViewPage = lazy(() => import('../../views/security/live-view-video'))
const SocialSeachPage = lazy(() => import('../../views/security/human-search/social-search'))
const SmartSearchPage = lazy(() => import('../../views/security/human-search/search-feature-image'))
const AlertRulePage = lazy(() => import('../../views/security/alert-rule'))
const HumanActionPage = lazy(() => import('../../views/security/human-search/human-action'))
const HumanTrackingPage = lazy(() => import('../../views/security/human-search/mct'))
const HumanUniformPage = lazy(() => import('../../views/security/human-search/human-uniform'))
const AlarmEventsPagePage = lazy(() => import('../../views/security/security-events'))
const TrafficPage = lazy(() => import('../../views/security/vehicle/traffic'))
const VehicleSearchPage = lazy(() => import('../../views/security/vehicle/vehicle-search'))
const IParkingPage = lazy(() => import('../../views/security/parking/parking-config'))
const IParkingResultPage = lazy(() => import('../../views/security/parking/parking-result'))
const IParkingLicensePage = lazy(() => import('../../views/security/parking/parking-license'))
const IParkingHistoryPage = lazy(() => import('../../views/security/parking/parking-history'))
const IParkingDashboardPage = lazy(() => import('../../views/security/parking/parking-dashboard'))
const IParkingGeneralPage = lazy(() => import('../../views/security/iparking-integration/iparking-general'))
const IParkingVehiclePage = lazy(() => import('../../views/security/iparking-integration/iparking-vehicle'))
const IParkingConfigPage = lazy(() => import('../../views/security/iparking-integration/iparking-config'))
const IParkingSyncDataPage = lazy(() => import('../../views/security/iparking-integration/iparking-sync-data'))
const IParkingDashboardsPage = lazy(() => import('../../views/security/iparking-integration/iparking-dashboard'))

const SecurityRoutes = [
    {
        path: '/security/security-events',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <AlarmEventsPagePage />
    },
    {
        path: '/security/access-control-alert',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <ACAlertPage />
    },
    {
        path: '/security/access-control',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <AccesControlPage />
    },
    {
        path: '/security/access-control-beta',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <ACBetaPage />
    },
    {
        path: '/security/access-control-mb',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <ACMbPage />
    },
    {
        path: '/security/door-log',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <ACLogPage />
    },
    {
        path: '/security/event-video',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <VideoManagementTable />
    },
    {
        path: '/security/live-view-video',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <LiveViewPage />
    },
    {
        path: '/security/human-search/mct',
        meta: {
            action: 'manage',
            resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        },
        element: <HumanTrackingPage />
    },
    {
        path: '/security/human-search/human-uniform',
        meta: {
            action: 'manage',
            resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        },
        element: <HumanUniformPage />
    },
    {
        path: '/security/human-search/search-feature-image',
        meta: {
            action: 'manage',
            resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        },
        element: <SmartSearchPage />
    },
    {
        path: '/security/human-search/human-action',
        meta: {
            action: 'manage',
            resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        },
        element: <HumanActionPage />
    },
    {
        path: '/security/human-search/social-search',
        meta: {
            action: 'manage',
            resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        },
        element: <SocialSeachPage />
    },
    {
        path: '/security/vehicle/traffic',
        meta: {
            action: 'manage',
            resource: 'VEHICLE_SEARCH',
        },
        element: <TrafficPage />
    },
    {
        path: '/security/vehicle/vehicle-search',
        meta: {
            action: 'manage',
            resource: 'VEHICLE_SEARCH',
        },
        element: <VehicleSearchPage />
    },
    {
        path: '/security/alert-rule',
        meta: {
            action: 'manage',
            resource: 'SECURITY',
        },
        element: <AlertRulePage />
    },
    {
        path: '/security/parking/parking-dashboard',
        meta: {
            action: 'manage',
            resource: 'I_PARKING_ADMIN',
        },
        element: <IParkingDashboardPage />
    },
    {
        path: '/security/parking/parking-license',
        meta: {
            action: 'manage',
            resource: 'I_PARKING_ADMIN',
        },
        element: <IParkingLicensePage />
    },
    {
        path: '/security/parking/parking-config',
        meta: {
            action: 'manage',
            resource: 'I_PARKING_ADMIN',
        },
        element: <IParkingPage />
    },
    {
        path: '/security/parking/parking-result',
        meta: {
            action: 'manage',
            resource: 'I_PARKING_VIEWER',
        },
        element: <IParkingResultPage />
    },
    {
        path: '/security/parking/parking-history',
        meta: {
            action: 'manage',
            resource: 'I_PARKING_VIEWER',
        },
        element: <IParkingHistoryPage />
    },
    {
        path: '/security/iparking-integration/iparking-general',
        meta: {
            action: 'manage',
            resource: 'IPARKING_INTEGRATION',
        },
        element: <IParkingGeneralPage />
    },
    {
        path: '/security/iparking-integration/iparking-vehicle',
        meta: {
            action: 'manage',
            resource: 'IPARKING_INTEGRATION',
        },
        element: <IParkingVehiclePage />
    },
    {
        path: '/security/iparking-integration/iparking-config',
        meta: {
            action: 'manage',
            resource: 'IPARKING_INTEGRATION',
        },
        element: <IParkingConfigPage />
    },
    {
        path: '/security/iparking-integration/iparking-sync-data',
        meta: {
            action: 'manage',
            resource: 'IPARKING_INTEGRATION',
        },
        element: <IParkingSyncDataPage />
    },
    {
        path: '/security/iparking-integration/iparking-dashboard',
        meta: {
            action: 'manage',
            resource: 'IPARKING_INTEGRATION',
        },
        element: <IParkingDashboardsPage />
    },
]

export default SecurityRoutes