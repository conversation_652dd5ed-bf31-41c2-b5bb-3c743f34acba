import { lazy } from 'react'

const SystemLogPage = lazy(() => import('../../views/settings/system-log'))
const DataBackupPage = lazy(() => import('../../views/settings/data-backup'))
const ServerSettingsPage = lazy(() => import('../../views/settings/server-settings'))
const UserGuidePage = lazy(() => import('../../views/user-guide'))
const ManualImportPage = lazy(() => import('../../views/settings/manual-import'))
const OttSettingPage = lazy(() => import('../../views/settings/ott'))
const MqttAddInPage = lazy(() => import('../../views/settings/mqtt-add-in'))
const CronJobPage = lazy(() => import('../../views/settings/cron-job'))

const SettingsRoutes = [
    {
        path: '/settings/ott',
        element: <OttSettingPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    },
    {
        path: '/settings/mqtt-add-in',
        element: <MqttAddInPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    },
    {
        path: '/settings/system-log',
        element: <SystemLogPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    },
    {
        path: '/settings/data-backup',
        element: <DataBackupPage />
    },
    {
        path: '/settings/server-settings',
        element: <ServerSettingsPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    },
    {
        path: '/settings/manual-import',
        element: <ManualImportPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    },
    {
        path: '/user-guide',
        element: <UserGuidePage />,
        meta: {
            action: 'read',
            resource: 'TIMEKEEPING',
        }
    },
    {
        path: '/settings/cron-job',
        element: <CronJobPage />,
        meta: {
            action: 'manage',
            resource: 'SERVER_CONFIG',
        }
    }
]

export default SettingsRoutes