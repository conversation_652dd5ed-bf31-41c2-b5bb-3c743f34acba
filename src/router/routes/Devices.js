import { lazy } from 'react'

const AiDevicesTable = lazy(() => import('../../views/devices/ai-devices'))
const AiServiceTable = lazy(() => import('../../views/devices/ai-services'))
const CameraPage = lazy(() => import('../../views/devices/camera'))
const DeviceGroupTable = lazy(() => import('../../views/devices/device-group'))
const DeviceAdminTable = lazy(() => import('../../views/devices/device-admin'))

const DevicesRoutes = [
    {
        path: '/devices/all-devices',
        element: <AiDevicesTable />,
        meta: {
            action: 'manage',
            resource: 'DEVICES',
        }
    },
    {
        path: '/devices/device-admin',
        element: <DeviceAdminTable />,
        meta: {
            action: 'manage',
            resource: 'DEVICES',
        }
    },
    {
        path: '/devices/ai-services',
        meta: {
            action: 'manage',
            resource: 'AI_SERVICE',
        },
        element: <AiServiceTable />
    },
    {
        path: '/devices/camera',
        meta: {
            action: 'manage',
            resource: 'CAMERA',
        },
        element: <CameraPage />
    },
    {
        path: '/devices/device-group',
        element: <DeviceGroupTable />
    },
]

export default DevicesRoutes