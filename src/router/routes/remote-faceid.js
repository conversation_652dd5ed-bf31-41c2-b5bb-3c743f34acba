import { lazy } from 'react'
const RemoteResultPage = lazy(() => import('../../views/remote-faceid/remote-result'))
const RemoteUserPage = lazy(() => import('../../views/remote-faceid/remote-user'))
const RemoteAdminPage = lazy(() => import('../../views/remote-faceid/remote-admin'))
const RemoteApproverPage = lazy(() => import('../../views/remote-faceid/remote-approver'))

const RemoteRoutes = [
    {
        path: '/remote-faceid/remote-result',
        element: <RemoteResultPage />,
        meta: {
            action: 'read',
            resource: 'TIMEKEEPING',
        }
    },
    {
        path: '/remote-faceid/remote-user',
        element: <RemoteUserPage />,
        meta: {
            action: 'manage',
            resource: 'REMOTE_USER',
        }
    },
    {
        path: '/remote-faceid/remote-admin',
        element: <RemoteAdminPage />,
        meta: {
            action: 'manage',
            resource: 'REMOTE_ADMIN',
        }
    },
    {
        path: '/remote-faceid/remote-approver',
        element: <RemoteApproverPage />,
        meta: {
            action: 'manage',
            resource: 'REMOTE_APPROVE',
        }
    },
]

export default RemoteRoutes