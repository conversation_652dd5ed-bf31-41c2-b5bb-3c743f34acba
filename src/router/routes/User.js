import { lazy } from 'react'

const DashboardReport = lazy(() => import('../../views/dashboards/events/DashboardReport'))
const SecurityReport = lazy(() => import('../../views/dashboards/security/SecurityReport'))
const UserPage = lazy(() => import('../../views/urs/all-users'))
const VipUsersPage = lazy(() => import('../../views/urs/vip'))
const BlackListPage = lazy(() => import('../../views/urs/black-list'))
const RolePage = lazy(() => import('../../views/roles/role'))
const PermissionPage = lazy(() => import('../../views/roles/permission'))
const TimeShiftPage = lazy(() => import('../../views/urs/time-shift'))
const ShiftExplanationPage = lazy(() => import('../../views/urs/shift-explanation'))
const PayrollPage = lazy(() => import('../../views/urs/payroll'))
const FeedbackPage = lazy(() => import('../../views/urs/feedback'))
const EkycUsersPage = lazy(() => import('../../views/urs/ekyc-users'))
const ImportUserPage = lazy(() => import('../../views/urs/import-user'))

const UserRoutes = [
    {
        path: '/dashboards/events',
        meta: {
            action: 'read',
            resource: 'DASHBOARD',
        },
        element: <DashboardReport />
    },
    {
        path: '/dashboards/security',
        meta: {
            action: 'read',
            resource: 'DASHBOARD',
        },
        element: <SecurityReport />
    },
    {
        path: '/roles/role',
        element: <RolePage />
    },
    {
        path: '/roles/permission',
        element: <PermissionPage />
    },
    {
        path: '/urs/all-users',
        element: <UserPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/urs/ekyc-users',
        element: <EkycUsersPage />,
        meta: {
            action: 'manage',
            resource: 'USERS',
        }
    },
    {
        path: '/urs/vip',
        element: <VipUsersPage />
    },
    {
        path: '/urs/black-list',
        element: <BlackListPage />
    },
    {
        path: '/urs/payroll',
        element: <PayrollPage />,
        meta: {
            action: 'read',
            resource: 'USERS',
        }
    },
    {
        path: '/urs/time-shift',
        element: <TimeShiftPage />,
        meta: {
            action: 'read',
            resource: 'USERS',
        }
    },
    {
        path: '/urs/shift-explanation',
        element: <ShiftExplanationPage />,
        meta: {
            action: 'read',
            resource: 'USERS',
        }
    },
    {
        path: '/urs/feedback',
        element: <FeedbackPage />,
    },
    {
        path: '/urs/import-user',
        element: <ImportUserPage />,
        meta: {
            action: 'read',
            resource: 'USERS',
        }
    },
]

export default UserRoutes