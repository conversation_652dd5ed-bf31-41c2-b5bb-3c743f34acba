// ** Router imports
import { lazy, useEffect } from 'react'

// ** Router imports
import { useRoutes, Navigate } from 'react-router-dom'

// ** Layouts
import BlankLayout from '@layouts/BlankLayout'

// ** Hooks Imports
import { useLayout } from '@hooks/useLayout'

// ** Utils
import { getUserData, getHomeRouteForLoggedInUser } from '../utility/Utils'

// ** GetRoutes
import { getRoutes } from './routes'
import { useLazyQuery, useMutation } from '@apollo/client'
import { CHECK_AUTHEN, LOGOUT } from '../apolo/graphql/User'

// ** Components
const Error = lazy(() => import('../views/pages/misc/Error'))
const Login = lazy(() => import('../views/pages/authentication/Login'))
const NotAuthorized = lazy(() => import('../views/pages/misc/NotAuthorized'))
const ShowCasePage = lazy(() => import('../views/dashboards/show-cases'))

//Redux
import { useDispatch } from 'react-redux'
import { handleSessionModal, handleLogout } from '@store/authentication'

const Router = () => {
    // ** Hooks
    const dispatch = useDispatch()
    const { layout } = useLayout()
    const [queryCheck, checkData] = useLazyQuery(CHECK_AUTHEN)

    const allRoutes = getRoutes(layout)
    const getHomeRoute = () => {
        const user = getUserData()
        if (user) {
            return getHomeRouteForLoggedInUser(user.roles[0].id)
        } else {
            return '/login'
        }
    }

    const routes = useRoutes([
        {
            path: '/',
            index: true,
            element: <Navigate replace to={getHomeRoute()} />
        },
        {
            path: '/login',
            element: <BlankLayout />,
            children: [{ path: '/login', element: <Login /> }]
        },
        {
            path: '/auth/not-auth',
            element: <BlankLayout />,
            children: [{ path: '/auth/not-auth', element: <NotAuthorized /> }]
        },
        {
            path: '/dashboards/show-cases',
            element: <BlankLayout />,
            children: [{ path: '/dashboards/show-cases', element: <ShowCasePage /> }]
        },
        {
            path: '*',
            element: <BlankLayout />,
            children: [{ path: '*', element: <Error /> }]
        },
        ...allRoutes
    ])

    //#region auto logout by lost token
    const checkUserToken = () => {
        let userData = getUserData();
        if (!userData?.accessToken) {
            return '/login';
        }

        queryCheck();
        // setIsLoggedIn(true);
    }

    useEffect(() => {
        if (checkData.loading) return;
        if (!checkData.data) return;

        if (!checkData.data.checkUserAuthen) {
            return '/login';
        }
    }, [queryCheck]);
    setInterval(checkUserToken, 1800000);
    //#endregion

    //#region auto logout by inactive
    const MINUTES_UNITL_AUTO_LOGOUT = 30; // in mins
    const CHECK_INTERVAL = 15000; // in ms
    const STORE_KEY = "lastAction";
    const [logout, { }] = useMutation(LOGOUT)

    const getLastAction = () => {
        return parseInt(localStorage.getItem(STORE_KEY));
    }

    const setLastAction = (lastAction) => {
        localStorage.setItem(STORE_KEY, lastAction.toString());
    }

    const reset = () => {
        setLastAction(Date.now());
    }

    // const initListener = () => {
    document.body.addEventListener("click", () => reset());
    document.body.addEventListener("mouseover", () => reset());
    document.body.addEventListener("mouseout", () => reset());
    document.body.addEventListener("keydown", () => reset());
    document.body.addEventListener("keyup", () => reset());
    document.body.addEventListener("keypress", () => reset());
    // }


    const check = () => {
        const now = Date.now();
        const timeleft = getLastAction() + MINUTES_UNITL_AUTO_LOGOUT * 60 * 1000;
        const diff = timeleft - now;
        const isTimeout = diff < 0;

        // Check if the session is about to expire
        const warningThreshold = 1 * 60 * 1000; // 1 minute in milliseconds
        const currentPath = window.location.pathname;

        if (diff < warningThreshold && diff > 0 && currentPath !== '/login') {
            dispatch(handleSessionModal(true));
        }

        if (isTimeout) {
            logout().then(() => {
                dispatch(handleLogout());
            });
            localStorage.clear();
        }
    };
    setInterval(check, CHECK_INTERVAL);
    //#endregion

    return routes
};

export default Router;