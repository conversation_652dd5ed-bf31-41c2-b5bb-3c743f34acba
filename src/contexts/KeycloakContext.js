import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import Keycloak from 'keycloak-js';

const KeycloakContext = createContext(null);

export const useKeycloak = () => useContext(KeycloakContext);

const keycloakConfig = {
    url: import.meta.env.VITE_PUBLIC_SERVER_IAM_URL,
    realm: import.meta.env.VITE_PUBLIC_REALM,
    clientId: import.meta.env.VITE_PUBLIC_CLIENT_ID,
};

export const KeycloakProvider = ({ children }) => {
    const keycloakRef = useRef(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [isAuthenticated, setIsAuthenticated] = useState(false);

    useEffect(() => {
        const initKeycloak = async () => {
            const kc = new Keycloak(keycloakConfig);
            try {
                const authenticated = await kc.init({
                    onLoad: 'check-sso',
                    checkLoginIframe: false,
                    silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
                    pkceMethod: 'S256'
                });
                keycloakRef.current = kc;
                setIsInitialized(true);
                setIsAuthenticated(authenticated);

                // Auto refresh token before it expires
                if (authenticated) {
                    setInterval(() => {
                        kc.updateToken(60).then((refreshed) => {
                            if (refreshed) {
                                console.log('Token refreshed');
                            } else {
                                console.log('Token not refreshed, valid for another 60 seconds');
                            }
                        }).catch(() => {
                            console.error('Failed to refresh token');
                            kc.logout();
                        });
                    }, 10000); // Check every 10 seconds
                }

            } catch (error) {
                console.error('Failed to initialize Keycloak', error);
                setIsInitialized(true);
            }
        };

        if (!keycloakRef.current) {
            initKeycloak();
        }
    }, []);

    const contextValue = {
        keycloak: keycloakRef.current,
        isInitialized,
        isAuthenticated,
    };

    return (
        <KeycloakContext.Provider value={contextValue}>
            {children}
        </KeycloakContext.Provider>
    );
};
