import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Card, CardBody, Row, Col, Progress, Tooltip } from 'reactstrap';

const ProgressUsage = ({ title, data }) => {
    const { t } = useTranslation('');

    const [tooltipOpen, setTooltipOpen] = useState({});
    const formatDiskSize = (sizeInMB) => {
        const sizeInGB = sizeInMB / 1024;
        const sizeInTB = sizeInGB / 1024;

        if (sizeInTB >= 1) {
            return { value: sizeInTB.toFixed(2), unit: 'TB' };
        }
        return { value: sizeInGB.toFixed(2), unit: 'GB' };
    };

    const memoryUsage = ((1 - data?.freeMem / data?.totalMem) * 100).toFixed(2);
    const diskUsage = ((1 - data?.freeDisk / data?.totalDisk) * 100).toFixed(2);

    const series = [data?.cpuUsage, memoryUsage, diskUsage];
    const labels = [t('CPU Usage'), t('Memory Usage'), t('Disk Usage')];

    const diskFree = formatDiskSize(data?.freeDisk);
    const diskTotal = formatDiskSize(data?.totalDisk);

    const details = [
        `${data?.cpuUsage}% ${t('CPU Usage')}`,
        `${(data?.freeMem / 1024).toFixed(2)}GB free of ${(data?.totalMem / 1024).toFixed(2)}GB`,
        `${diskFree?.value} ${diskFree?.unit} free of ${diskTotal?.value} ${diskTotal?.unit}`
    ];

    const toggle = (id) => {
        setTooltipOpen((prevState) => ({
            ...prevState,
            [id]: !prevState[id]
        }));
    };

    return (
        <Card className='stats-card'>
            <CardBody>
                <h6 className='mb-2'>{title}</h6>
                <Row className='pt-50'>
                    {labels.map((label, index) => {
                        const progressId = `progress-${index}`;
                        const detail = details[index];

                        return (
                            <Col key={index} className='mb-1' md='12' sm='12'>
                                <p className='mb-50 fw-bold'>
                                    <span className='fw-bolder'>{label}:</span>
                                    <span className={
                                        series[index] <= 60 ? 'text-success' :
                                            series[index] <= 85 ? 'text-warning' :
                                                'text-danger'
                                    }>
                                        {` ${series[index]}%`}
                                    </span>
                                </p>
                                <Progress
                                    id={progressId}
                                    className={`avg-session-progress mt-25 ${series[index] <= 60 ? 'progress-bar-success' :
                                        series[index] <= 85 ? 'progress-bar-warning' :
                                            'progress-bar-danger'
                                        }`}
                                    value={series[index]}
                                />
                                <Tooltip
                                    isOpen={tooltipOpen[progressId]}
                                    target={progressId}
                                    toggle={() => toggle(progressId)}
                                >
                                    {detail}
                                </Tooltip>
                            </Col>
                        );
                    })}
                </Row>
            </CardBody>
        </Card>
    );
};

export default ProgressUsage;

// ** PropTypes
// ProgressUsage.propTypes = {
//     title: PropTypes.string.isRequired,
//     data: PropTypes.shape({
//         cpuUsage: PropTypes.number.isRequired,
//         totalMem: PropTypes.number.isRequired,
//         freeMem: PropTypes.number.isRequired,
//         totalDisk: PropTypes.number.isRequired,
//         freeDisk: PropTypes.number.isRequired
//     }).isRequired
// };