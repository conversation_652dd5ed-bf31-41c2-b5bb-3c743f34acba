import PropTypes from 'prop-types';
import { Card, CardBody } from 'reactstrap';

const StatsVertical = ({ icon, color, stats, statTitle }) => {
    return (
        <Card className='stats-card'>
            <CardBody className="d-flex justify-content-center align-items-center">
                <div className='d-flex flex-column align-items-center'>
                    <div className={`avatar p-50 m-0 mb-1 ${color ? `bg-light-${color}` : 'bg-light-primary'}`}>
                        <div className='avatar-content'>{icon}</div>
                    </div>
                    <h2 className='fw-bolder'>{stats}</h2>
                    <p className='card-text line-ellipsis'>{statTitle}</p>
                </div>
            </CardBody>
        </Card>
    )
};

export default StatsVertical;

// ** PropTypes
StatsVertical.propTypes = {
    className: PropTypes.string,
    icon: PropTypes.element.isRequired,
    color: PropTypes.string.isRequired,
    stats: PropTypes.string.isRequired,
    statTitle: PropTypes.string.isRequired
};