// =========================================================================================
//   File Name: custom-options.scss
//   Description: custom checkbox and radion button style.
//   ----------------------------------------------------------------------------------------
//   Item Name: Vuexy  - Vuejs, HTML & Laravel Admin Dashboard Template
//   Author: PIXINVENT
//   Author URL: http://www.themeforest.net/user/pixinvent
// ==========================================================================================

// Component: custom options
// ========================================================================

.custom-options-checkable {
  .custom-option-item {
    width: 100%;
    cursor: pointer;
    border-radius: 0.42rem;
    color: $secondary;
    background-color: $custom-options-bg-color;
    border: 1px solid $border-color;
    .custom-option-item-title {
      color: $secondary;
    }
  }
}

.custom-option-item-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);

  &:checked {
    + .custom-option-item {
      color: $primary;
      background-color: rgba($primary, 0.12);
      border-color: $primary;
      .custom-option-item-title {
        color: $primary;
      }
    }
  }
}

.video-section, .image-section {
  padding: 10px;
  border: 1px solid #ddd;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
}

.no-video-placeholder, .no-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 324px;
  font-size: 1.2em;
  color: #777;
  background-color: #eee;
  border-radius: 8px;
}

.image-item {
  padding: 5px;
}

.image-container {
  width: 150px;
  height: 180px;
  overflow: hidden;
  // border: 1px solid #ccc;
  // border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-content {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.stats-card {
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);

  &:hover {
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-3px); /* Lift effect */
  }
}

.stats-card.selected {
  background-color: #e3f2fd;
  box-shadow: 0px 6px 14px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
  border: 2px solid #007bff;
}

.avatar-stats {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.service-wrapper {
  position: relative;
}

.service-wrapper .delete-btn {
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
}

.service-wrapper:hover .delete-btn {
  opacity: 1;
  visibility: visible;
}

/* Fixed height for the card to ensure consistency */
.video-group-card {
  height: 500px;
  display: flex;
  flex-direction: column;
}

/* Card body takes remaining space */
.video-group-card-body {
  flex: 1;
  padding: 0 !important;
  overflow: hidden; /* Prevent overflow at card level */
  display: flex;
  flex-direction: column;
}

/* Scrollable container */
.video-group-list-container {
  flex: 1;
  max-height: calc(100% - 40px); /* Leave space for the load more button */
  overflow-y: auto !important;
  overflow-x: hidden;
  /* Force scrollbar to always show */
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  position: relative;
}

/* For WebKit browsers (Chrome, Safari) */
.video-group-list-container::-webkit-scrollbar {
  width: 8px !important;
  display: block !important;
}

.video-group-list-container::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.video-group-list-container::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 4px !important;
  border: 2px solid #f1f1f1 !important;
}

.video-group-list-container::-webkit-scrollbar-thumb:hover {
  background: #aaa !important;
}

/* Add a min-height to ensure scrollability */
.video-group-list {
  padding: 10px;
  min-height: 100%;
}

/* Group item styling */
.video-group-item {
  transition: all 0.2s ease;
  border: 1px solid transparent;
  cursor: pointer;
}

.video-group-item:hover {
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
}

/* Selected group item */
.bg-light-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  border-color: rgba(var(--bs-primary-rgb), 0.2) !important;
}

/* Load more button container */
.load-more-button-container {
  padding: 8px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* Add a class to indicate when content is scrollable */
.scrollable {
  border-bottom: 1px solid #e9ecef;
}

//#region face reid video
.video-container {
  position: relative;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.subtitle-container {
  position: relative;
  height: 100px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 1.2rem;
  line-height: 1.5;
  text-align: center;

  .current-subtitle {
      font-weight: bold;
      color: #fff;
      margin-bottom: 0.5rem;
      min-height: 1.5em;
  }

  .next-subtitle {
      color: rgba(255, 255, 255, 0.6);
      font-size: 1rem;
      min-height: 1.5em;
  }
}

.timeline-container {
  position: relative;
  height: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem;
  margin-top: 1rem;

  .timeline {
      position: relative;
      height: 100%;
      background-color: #e9ecef;
      border-radius: 4px;

      .timeline-point {
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          background-color: #7367f0;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
              width: 12px;
              height: 12px;
              background-color: #5e50ee;
          }
      }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .video-container {
      margin-bottom: 0.5rem;
  }

  .subtitle-container {
      height: 80px;
      font-size: 1rem;
      padding: 0.5rem;

      .next-subtitle {
          font-size: 0.9rem;
      }
  }

  .timeline-container {
      height: 30px;
      margin-top: 0.5rem;
  }
}
//#endregion

.custom-slider {
  width: 100%;
  height: 16px;
  margin: 0 10px;
  position: relative;
}

.custom-track {
  top: 50%;
  transform: translateY(-50%);
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #4f8cff 0%, #38d39f 100%);
  transition: background 0.3s;
}

.custom-thumb {
  height: 24px;
  width: 24px;
  background: #fff;
  border: 2px solid #4f8cff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(79, 140, 255, 0.2);
  cursor: grab;
  transition: border 0.2s, box-shadow 0.2s;
  margin-top: -5px; // căn giữa thumb với track
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-thumb:active {
  border: 2px solid #38d39f;
  box-shadow: 0 4px 16px rgba(56, 211, 159, 0.2);
}

.custom-thumb:hover {
  border: 2px solid #38d39f;
}

.custom-track.custom-track-0 {
  background: #e9ecef;
}

.custom-track.custom-track-1 {
  background: linear-gradient(90deg, #4f8cff 0%, #38d39f 100%);
}

.custom-track.custom-track-2 {
  background: #e9ecef;
}