// ================================================================================================
//  File Name: custom-rtl.scss
//  Description: RTL support SCSS file.
//  ----------------------------------------------------------------------------------------------
//  Item Name: Vuexy  - Vuejs, HTML & Laravel Admin Dashboard Template
//  Author: PIXINVENT
//  Author URL: http://www.themeforest.net/user/pixinvent
// ================================================================================================

// Variables
// ------------------------------

@import 'bootstrap-extended/include'; // Bootstrap includes
@import 'components/include'; // Components includes

pre,
code,
kbd,
samp {
  direction: ltr;
}
// Align icons position
.main-menu {
  .navigation li > a > svg,
  .navigation li > a > i,
  .dropdown-menu svg,
  .dropdown-menu i,
  .dropdown-user > a > svg,
  .dropdown-user > a > i,
  .navigation > li > a > svg,
  .navigation > li > a > i {
    float: right;
  }

  .navigation > li ul li > a {
    display: flex;
    align-items: center;
  }
}
[type='tel'],
[type='url'],
[type='email'],
[type='number'] {
  direction: rtl;
}

// Transformed menu icons
.vertical-layout.vertical-menu-modern.menu-expanded .main-menu .navigation li.has-sub > a:after,
.vertical-layout.vertical-overlay-menu.menu-open .main-menu .navigation li.has-sub > a:after {
  transform: rotate(180deg);
}
.vertical-layout.vertical-menu-modern.menu-expanded
  .main-menu
  .navigation
  li.has-sub.open:not(.menu-item-closing)
  > a:after {
  transform: rotate(90deg);
}

// Horizontal menu
.horizontal-menu .header-navbar.navbar-horizontal .dropdown-menu .dropdown-toggle::after {
  background-image: url(str-replace(str-replace($chevron-left, 'currentColor', $body-color), '#', '%23'));
}

// Dropdown RTL Changes
.header-navbar .navbar-container ul.nav li.dropdown {
  .dropdown-menu {
    &::before {
      top: 1px;
    }
  }
}

.header-navbar {
  .dropdown,
  .dropup {
    .dropdown-menu.dropdown-menu-end::before {
      right: auto;
      left: 0.5rem;
    }
  }
}

.dropdown,
.dropup,
.btn-group {
  .dropdown-menu {
    right: auto !important;
    left: auto !important;

    &.dropdown-menu-end {
      left: 0 !important;

      &::before {
        right: 0.6rem;
        left: auto;
      }
    }
  }
}

.dropstart {
  .dropdown-toggle {
    &::before {
      background-image: url(str-replace(str-replace($chevron-right, 'currentColor', $white), '#', '%23')) !important;
    }
  }
  .dropdown-menu {
    margin-right: $dropdown_spacing !important;
  }
}

.dropend {
  .dropdown-toggle {
    &::after {
      background-image: url(str-replace(str-replace($chevron-left, 'currentColor', $white), '#', '%23')) !important;
    }
  }
  .dropdown-menu {
    left: 0 !important;
    margin-left: $dropdown_spacing !important;
  }
}

// BS Toast
.toast {
  right: auto;
}

// Select2
.select2-container--default .select2-selection--single .select2-selection__arrow {
  left: 1px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  float: right;
}
.select2-search__field {
  direction: rtl;
}
.select2,
.select2-container {
  text-align: right;
}

.apexcharts-canvas .apexcharts-text.apexcharts-yaxis-label {
  transform: translate(14px, 0);
}

// Chartist
.chartjs-render-monitor {
  margin-right: 1rem;
}

// Datatable
div.dataTables_wrapper div.dataTables_filter {
  text-align: left !important;
}
table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before {
  right: 0.45rem;
}

// Datatable responsive modal
.dtr-modal .dtr-modal-close {
  left: 6px;
  right: auto !important;
}

// Avatar group
.avatar-group {
  // Avatar Group Sizings
  .avatar {
    margin-right: -0.785rem;
    margin-left: 0;
  }
  .avatar-sm {
    margin-right: -0.65rem;
  }
  .avatar-lg {
    margin-right: -1.5rem;
  }
  .avatar-xl {
    margin-right: -1.85rem;
  }
}

// Breadcrumb
.breadcrumb:not([class*='breadcrumb-']),
.breadcrumb.breadcrumb-chevron {
  .breadcrumb-item + .breadcrumb-item {
    &:before {
      transform: rotate(180deg);
    }
  }
}

// Pagination
.pagination .page-item {
  &.prev-item,
  &.prev,
  &.previous {
    .page-link {
      &:before {
        transform: rotate(180deg);
      }
      &:hover,
      &:active {
        &:before {
          transform: rotate(180deg);
        }
      }
    }
    &.disabled {
      .page-link {
        &:before {
          transform: rotate(180deg);
        }
      }
    }
  }

  &.next-item,
  &.next {
    .page-link {
      &:after {
        transform: rotate(180deg);
      }
      &:hover,
      &:active {
        &:after {
          transform: rotate(180deg);
        }
      }
    }
    &.disabled {
      .page-link {
        &:before {
          transform: rotate(180deg);
        }
      }
    }
  }
}

code[class*='language-'],
pre[class*='language-'] {
  direction: ltr;
}

@media print {
  code[class*='language-'],
  pre[class*='language-'] {
    text-shadow: none;
  }
}

// Calendar
.fc .fc-header-toolbar .fc-right .fc-button.fc-prev-button .fc-icon {
  right: 4px !important;
}

.fc .fc-header-toolbar .fc-right .fc-button.fc-next-button .fc-icon {
  left: -3px !important;
}

// Popover & Tooltip
.bs-popover-start .popover-arrow::before,
.bs-tooltip-start .tooltip-arrow::before,
.bs-popover-auto[data-popper-placement^='left'] .popover-arrow::before {
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: $tooltip-bg;
  left: -1px;
}
.bs-tooltip-start .tooltip-arrow::before {
  left: -6px;
}
.bs-popover-start > .popover-arrow::after,
.bs-tooltip-start > .tooltip-arrow::after {
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: $tooltip-color;
}
.bs-popover-start .popover-arrow,
.bs-tooltip-start .tooltip-arrow,
.bs-popover-auto[data-popper-placement^='left'] .popover-arrow {
  right: auto;
  left: 100%;
}

.bs-popover-end .popover-arrow::before,
.bs-tooltip-end .tooltip-arrow::before,
.bs-popover-auto[data-popper-placement^='right'] .popover-arrow::before {
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: $tooltip-bg;
  right: -1px;
}
.bs-tooltip-end .tooltip-arrow::before {
  right: -6px;
}
.bs-popover-end > .popover-arrow::after,
.bs-tooltip-end > .tooltip-arrow::after {
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: $tooltip-color;
}
.bs-popover-end .popover-arrow,
.bs-tooltip-end .tooltip-arrow,
.bs-popover-auto[data-popper-placement^='right'] .popover-arrow {
  left: auto;
  right: 100%;
}

// Perfect scrollbar RTL fix
body .ps__rail-y {
  right: auto !important;
  left: 1px !important;
}

// FAQ and Pricing page
.faq-navigation img,
.pricing-free-trial .pricing-trial-img {
  transform: scaleX(-1);
}

.feather-chevron-left,
.feather-chevron-right {
  transform: rotate(-180deg) !important;
}

// Kanban
.kanban-application {
  .kanban-container {
    .kanban-item {
      i,
      svg {
        margin-right: 0 !important;
        margin-left: 0.25rem;
      }
    }
  }
}

// Invoice List
.invoice-list-wrapper {
  .dataTables_filter {
    input {
      margin-left: 0 !important;
      margin-right: 0.5rem;
    }
  }

  .dropdown .dropdown-menu.dropdown-menu-end {
    left: 2rem !important;
  }
}

// File Manager
.file-manager-application {
  .sidebar-file-manager {
    .sidebar-inner {
      .my-drive .jstree-node.jstree-closed > .jstree-icon {
        transform: rotate(180deg);
      }
    }
  }
}
