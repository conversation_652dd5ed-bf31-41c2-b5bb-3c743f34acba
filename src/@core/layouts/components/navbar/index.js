import { Fragment } from 'react';
import NavbarUser from './NavbarUser';
import NavbarBookmarks from './NavbarBookmarks';
import UserProfile from '../../../../views/urs/all-users/UserProfile';

const ThemeNavbar = ({ skin, setSkin, setMenuVisibility, popUp, setPopUp }) => {

    return (
        <Fragment>
            <div className='bookmark-wrapper d-flex align-items-center'>
                <NavbarBookmarks setMenuVisibility={setMenuVisibility} />
            </div>
            <NavbarUser skin={skin} setSkin={setSkin} popUp={popUp} setPopUp={setPopUp} />

            {window.location.pathname !== "/urs/all-users" ? <UserProfile afterClose={() => { }} /> : null}
        </Fragment>
    )
};

export default ThemeNavbar;
