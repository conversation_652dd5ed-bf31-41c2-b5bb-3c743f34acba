import { NavItem, NavLink } from 'reactstrap';
import { Sun, Moon } from 'react-feather';
import { TbBellOff, TbBellRinging } from "react-icons/tb";
import IntlDropdown from './IntlDropdown';
import UserDropdown from './UserDropdown';
import NotificationDropdown from '../../../../views/urs/components/NotificationDropdown';

const NavbarUser = props => {

    const { skin, setSkin, popUp, setPopUp } = props

    // ** Function to toggle Theme (Light/Dark)
    const ThemeToggler = () => {
        if (skin === 'dark') {
            return <Sun className='ficon' onClick={() => setSkin('light')} />
        } else {
            return <Moon className='ficon' onClick={() => setSkin('dark')} />
        }
    };

    const PopUpImage = () => {
        if (popUp === true) {
            return <TbBellRinging className='ficon' onClick={() => setPopUp(false)} />
        } else {
            return <TbBellOff className='ficon' onClick={() => setPopUp(true)} />
        }
    };

    return (
        <ul className='nav navbar-nav align-items-center ms-auto'>
            <IntlDropdown />
            <NavItem className='d-none d-lg-block'>
                <NavLink className='nav-link-style'>
                    <ThemeToggler />
                </NavLink>
            </NavItem>
            <NavItem className='d-none d-lg-block'>
                <NavLink className='nav-link-style'>
                    <PopUpImage />
                </NavLink>
            </NavItem>
            {/* <NavbarSearch /> */}
            {/* <CartDropdown /> */}
            <NotificationDropdown />
            <UserDropdown />
        </ul>
    )
};

export default NavbarUser;