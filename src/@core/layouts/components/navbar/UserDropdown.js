import { Link } from 'react-router-dom';
import { Fragment, useEffect, useRef, useState } from 'react';
import Avatar from '@components/avatar';
import { isUserLoggedIn } from '@utils';
import { useNavigate } from "react-router-dom";
import { User, Power, AlertTriangle } from 'react-feather';
import { UncontrolledDropdown, DropdownMenu, DropdownToggle, DropdownItem } from 'reactstrap';
import moment from 'moment';
// import { toast } from 'react-hot-toast';
import { toast as sonnerToast } from 'sonner';
import io from 'socket.io-client';
import { floodPrevent } from '@src/utility/flooding-prevent-module';
import { useDispatch, useSelector } from 'react-redux';
import { handleLogout } from '@store/authentication';
import { handleSelectedUserId, handleShowUserProfileModal } from '@src/views/urs/all-users/store';
import { useTranslation } from 'react-i18next';
import defaultAvatar from '@src/assets/images/portrait/small/avatar-s-11.jpg';
import ApiUrlUserAvatarImage from '@src/views/urs/all-users/ApiUrlUserAvatarImage';
import warningSound from '@src/assets/sound/warning-sound.mp3';
import AlertScriptTypeConstants from "@src/constants/AlertScriptTypeConstants";
import { useKeycloak } from '@src/contexts/KeycloakContext';

const UserDropdown = () => {

    //#region states
    const AlertTypeEnum = AlertScriptTypeConstants.AlertType;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let showUserProfileModal = useSelector(state => state.user.showUserProfileModal);
    const navigate = useNavigate();
    const socketRef = useRef(null);
    const audioRef = useRef(null);
    const { keycloak } = useKeycloak();
    //#endregion

    //#region notification
    const renderUserAvatar = e => {
        if (e?.avatar !== null) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${e?.avatar}`} />
        } else {
            return <Avatar className='me-1' />
        }
    };

    /**
     * Displays a toast notification with a custom message and plays a warning sound.
     *
     * @param {Object} msg - The message object containing information to be displayed in the toast notification.
     * @param {string} msg.image - The base64 encoded image to be displayed in the toast notification.
     * @param {string} msg.userName - The user name to be displayed in the toast notification.
     * @param {string} msg.locationName - The location name to be displayed in the toast notification.
     * @param {string} msg.text - The text to be displayed in the toast notification.
     * @param {Date} msg.time - The time to be displayed in the toast notification.
     * @return {Object} The toast notification object.
     */
    function noti(msg) {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        };

        // new sound
        const audio = new Audio(warningSound);
        audioRef.current = audio;
        audio.play();

        setTimeout(() => {
            if (audioRef.current === audio) {
                audio.pause();
                audio.currentTime = 0;
                audioRef.current = null;
            }
        }, 6000);

        return (
            sonnerToast(
                <div className='row w-100' style={{ display: 'flex', alignItems: 'center' }}
                    onClick={() => {
                        if (msg?.text?.includes('Vi phạm đồng phục')) {
                            navigate("/security/human-search/mct");
                        } else if (msg?.alertType) {
                            navigate("/security/security-events");
                        } else {
                            navigate("/faceid/timekeeping_unknown");
                        }
                    }}
                >
                    <div className='col-4 d-flex justify-content-center align-self-center'>
                        {msg?.image ? (
                            <img
                                src={`data:image/jpeg;base64,${msg?.image}`}
                                style={{
                                    padding: '5px',
                                    maxHeight: '80px',
                                    maxWidth: '100px',
                                    marginRight: '5px',
                                }}
                            />
                        ) : null}
                    </div>
                    <div className='col-8 d-flex flex-column justify-content-center'>
                        <span className='fw-bolder fs-4 text-danger'>{msg?.userName}</span>
                        <span className='text-truncate fs-5 text-danger'>{msg?.locationName}</span>
                        <span className='text-truncate text-danger'>{AlertTypeEnum[msg?.alertType]}</span>
                        <span className='fw-bolder text-danger'>{msg?.text}</span>
                        <span className='text-truncate text-danger'>{moment(msg?.time).format('HH:mm:ss')}</span>
                    </div>
                </div>,
                {
                    duration: 6000,
                    position: 'bottom-right',
                    style: {
                        backgroundColor: '#FFF0F0',
                        borderRadius: '8px',
                        padding: '10px',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #FFE0E1',
                    },
                }
            )
        );
    };

    /**
     * Plays a warning sound and displays a notification with the given message.
     *
     * @param {Object} msg - The message object containing the notification details.
     * @return {Object} The toast notification object.
     */
    function notiAC(msg) {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        };

        // new sound
        const audio = new Audio(warningSound);
        audioRef.current = audio;
        audio.play();

        setTimeout(() => {
            if (audioRef.current === audio) {
                audio.pause();
                audio.currentTime = 0;
                audioRef.current = null;
            }
        }, 6000);

        return (
            sonnerToast(
                <div className='row w-100' style={{ display: 'flex', alignItems: 'center' }}>
                    <div className='col-3 d-flex justify-content-center align-self-center'>
                        <AlertTriangle className="text-danger" size={40} />
                    </div>
                    <div className='col-9 d-flex flex-column justify-content-center'>
                        <span className='fw-bolder fs-4 text-danger'>{msg?.locationName}</span>
                        <span className='text-truncate text-danger'>{moment(msg?.time).local().format('DD-MM-YYYY HH:mm:ss')}</span>
                        <span className='fw-bolder text-danger'>{msg?.text}</span>
                        {msg?.UserAlert ? (
                            <div className='d-flex justify-content-left align-items-center mt-1'>
                                {renderUserAvatar(msg?.UserAlert)}
                                <div className='d-flex flex-column'>
                                    <span className='fw-bolder text-danger'>{msg?.UserAlert?.name}</span>
                                    <small className='text-truncate text-danger'>{msg?.UserAlert?.title}</small>
                                    <small className='text-truncate text-danger'>{msg?.UserAlert?.email}</small>
                                </div>
                            </div>
                        ) : null}
                    </div>
                </div>,
                {
                    duration: 8000,
                    position: 'bottom-right',
                    style: {
                        backgroundColor: '#FFF0F0',
                        borderRadius: '8px',
                        padding: '10px',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #FFE0E1',
                    },
                }
            )
        );
    };
    //#endregion

    //#region socket init
    const socketInit = () => {
        let msgId = null;

        socketRef.current = io(`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/socket/alert-popup`, { transports: ["websocket"] });

        socketRef.current.on("msgToClient", (msg) => {
            // console.log(msg);
        });

        socketRef.current.on("popupAlarm", debounce((msg) => {
            const parsedMsg = JSON.parse(msg);
            if (parsedMsg.id && parsedMsg.id !== msgId) {
                if (floodPrevent.checkFlood(socketRef.current)) {
                    // console.log(parsedMsg);
                    noti(parsedMsg);
                    msgId = parsedMsg.id;
                } else {
                    floodPrevent.resetProtection();
                }
            }
        }, 2000));

        socketRef.current.on("textPopupAlarm", debounce((msg) => {
            const parsedMsg = JSON.parse(msg);
            if (parsedMsg.id && parsedMsg.id !== msgId) {
                if (floodPrevent.checkFlood(socketRef.current)) {
                    notiAC(parsedMsg);
                    msgId = parsedMsg.id;
                } else {
                    floodPrevent.resetProtection();
                }
            }
        }, 2000));

        function debounce(func, wait) {
            let timeout;
            return function (...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(this, args);
                }, wait);
            };
        }

        return () => {
            if (socketRef.current) {
                socketRef.current.disconnect();
            }
        };
    };
    //#endregion

    //#region check user role
    const [userData, setUserData] = useState(null);

    useEffect(() => {
        if (isUserLoggedIn() !== null) {
            setUserData(JSON.parse(localStorage.getItem('userData')));
        }
    }, []);

    let userRole = userData ? userData?.roles[0].id : null;
    //#endregion

    //#region socket connection
    useEffect(() => {
        const shouldShowPopup = JSON.parse(localStorage.getItem('popUp'));
        if (shouldShowPopup && ["SUPER_ADMIN", "ADMIN", "I_PARKING_ADMIN", "I_PARKING_VIEWER"].includes(userRole)) {
            const cleanup = socketInit();
            return cleanup;
        } else {
            if (socketRef.current) {
                socketRef.current.disconnect();
            }
        }
    }, [userRole, localStorage.getItem('popUp')]);
    //#endregion

    const userAvatar = (userData && userData.avatar) ? `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${userData.avatar}` : defaultAvatar

    return (
        <Fragment>
            <UncontrolledDropdown tag='li' className='dropdown-user nav-item'>
                <DropdownToggle href='/' tag='a' className='nav-link dropdown-user-link' onClick={e => e.preventDefault()}>
                    <div className='user-nav d-sm-flex d-none'>
                        <span className='user-name fw-bold'>{(userData && userData['fullName']) || 'Admin'}</span>
                        {/* <span className='user-status'>{(userData && userData.role[0] != null) ? UserRoleType[] || 'Admin'}</span> */}
                        <span className='user-status'>{userRole}</span>
                    </div>
                    <ApiUrlUserAvatarImage url={userAvatar} width={40} height={40} status='online' />
                </DropdownToggle>
                <DropdownMenu end>
                    <DropdownItem className='w-100' onClick={() => {
                        dispatch(handleSelectedUserId(userData?.id));
                        dispatch(handleShowUserProfileModal(!showUserProfileModal));
                    }}>
                        <User size={14} className='me-75' />
                        <span className='align-middle'>{t("Profile")}</span>
                    </DropdownItem>
                    <DropdownItem divider />
                    <DropdownItem className='w-100' {...(userData?.loginMethod !== 'iam' ? { tag: Link, to: '/login' } : {})}
                        onClick={() => {
                            dispatch(handleLogout());
                            if (userData?.loginMethod === 'iam') {
                                keycloak?.logout({
                                    redirectUri: window.location.origin + '/login'
                                });
                            }
                        }}>
                        <Power size={14} className='me-75' />
                        <span className='align-middle'>{t("Logout")}</span>
                    </DropdownItem>
                </DropdownMenu>
            </UncontrolledDropdown>
        </Fragment>
    )
};

export default UserDropdown;