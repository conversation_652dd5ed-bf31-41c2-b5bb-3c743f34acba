import { ApolloClient, ApolloLink, HttpLink, InMemoryCache } from "@apollo/client/core";
import { setContext } from "@apollo/client/link/context";
import { useNavigate } from "react-router-dom";
import { getUserData } from "../utility/Utils";
// const https = require('https')
// let fetchOptions = {}
// if (process.env.NEXT_PUBLIC_BACKEND_IP.includes("https"))
//   fetchOptions = { agent: new https.Agent({ rejectUnauthorized: false }) }
let apolloClient;

const authLink = setContext((_, { headers }) => {
  let accessToken = "";
  let userData = getUserData();
  if (userData)
    accessToken = userData.accessToken;

  if (accessToken) {
    return {
      headers: {
        ...headers,
        authorization: accessToken,
      },
    };
  }

  return {
    headers: {
      ...headers,
    },
  };
});

const checkAuthenMiddleLink = new ApolloLink((operation, forward) => {
  return forward(operation).map(response => {
    if (response?.errors)
      if (response?.errors[0]) {
        if (response.errors[0].extensions?.response.statusCode === 401) {
          localStorage.removeItem('userData');
          setTimeout(() => {
            window.location.href = "/login";
          }, 5000);
        }
      }
    return response;
  });
});

const links = ApolloLink.from([
  authLink, checkAuthenMiddleLink
]);

function createApolloClient() {
  const dev = import.meta.env.NODE_ENV !== 'production';
  return new ApolloClient({
    ssrMode: typeof window === "undefined",
    // link: authLink.concat(
    //   new HttpLink({
    //     uri: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/graphql`,
    //     // uri: `${process.env.NEXT_PUBLIC_BACKEND_IP}/graphql`,
    //     // fetchOptions
    //   })
    // ),
    link: links.concat(
      new HttpLink({
        uri: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/graphql`,
        // uri: `${process.env.NEXT_PUBLIC_BACKEND_IP}/graphql`,
        // fetchOptions
      })
    ),
    cache: new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            roles: {
              merge(existing, incoming) {
                return incoming;
              },
            },
          },
        },
      }
    }),
  });
}
// const logoutLink = onError(({ networkError }) => {
//   const dispatch = useDispatch()
//   console.log(networkError)
//   // if (networkError?.statusCode === 401) dispatch(handleLogout();
// })

export function initializeApollo(initialState = null) {
  const _apolloClient = apolloClient ?? createApolloClient();

  // If your page has Next.js data fetching methods that use Apollo Client, the initial state
  // gets hydrated here
  if (initialState) {
    // Get existing cache, loaded during client side data fetching
    const existingCache = _apolloClient.extract();
    // Restore the cache using the data passed from getStaticProps/getServerSideProps
    // combined with the existing cached data
    _apolloClient.cache.restore({ ...existingCache, ...initialState });
  }
  // For SSG and SSR always create a new Apollo Client
  if (typeof window === "undefined") return _apolloClient;
  // Create the Apollo Client once in the client
  if (!apolloClient) apolloClient = _apolloClient;

  return _apolloClient;
}
