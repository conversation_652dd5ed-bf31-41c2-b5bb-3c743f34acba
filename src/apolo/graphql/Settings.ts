import { gql } from "@apollo/client";

export const QUERY_SYSTEM_LOG = gql`
query querySystemLog(
    $userIds: [String!]!, 
    $search: String!,
    $functionIds: [String!]!, 
    $type: [String!]!, 
    $startDate: Date!,
    $endDate: Date!,
    $page: Int!, 
    $perPage: Int!,
){
    querySystemLog(
        userIds: $userIds,
        search: $search,
        functionIds: $functionIds,
        type: $type,
        startDate: $startDate,
        endDate: $endDate,
        page: $page,
        perPage: $perPage,
       
    ){
        id,
        changeNote,
        oldValue,
        newValue
        type,
        dateCreated,
        dateModified,
        functionId,
        count,
        User{id,email,name,avatar}
    }
}
`;

export const QUERY_FUNCTION_ID = gql`
query queryFunctionId{
    queryFunctionId{
        id,
        name,
        count
    }
}
`;

export const QUERY_ALL_CRON_JOBS = gql`
query getAllCronJobs(
    $search: String!
){
    getAllCronJobs(
        search: $search
    ){
        name,running,lastDate,nextDate,count
    }
}
`;

export const QUERY_ALL_INTERVAL = gql`
query getAllInterval(
    $search: String!
){
    getAllInterval(
        search: $search
    ){
        name,running,lastDate,nextDate,count,_idleStart,_repeat
    }
}
`;