import { gql } from "@apollo/client";

export const SEARCH_STUDENT = gql`
query searchStudentPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $classCode: String,
    $className: String,
    $grade: String,
    $majorCode: String,
    $majorName: String,
){
    searchStudentPage
    (
        search: $search,
        page: $page,
        perPage: $perPage,
        classCode: $classCode,
        className: $className,
        grade: $grade,
        majorCode: $majorCode,
        majorName: $majorName,
    ){
        UserGql
        {
            id,avatar,name,birthDay
        },
        avatarBase64,
        studentId,
        classCode,
        className,
        grade,
        majorCode,
        majorName,
        validUntil,
        count
    }
}
`;

export const GET_STUDENT = gql`
mutation upsertStudent(
    $userId: String!, 
    $classCode: String!, 
    $className: String!, 
    $grade: String!, 
    $majorCode: String!, 
    $majorName: String!, 
    $validUntil: Date!, 
){
    upsertStudent(
        userId: $userId,
        classCode: $classCode,
        className: $className,
        grade: $grade,
        majorCode: $majorCode,
        majorName: $majorName,
        validUntil: $validUntil,
    )
}
`;

export const DELETE_STUDENT = gql`
mutation removeStudent(
    $userId: String,
    $studentId: String,
){removeStudent
    (
        studentId: $studentId,
        userId: $userId,
    )
}
`;