import { gql } from "@apollo/client";

export const GET_TIMEKEEPING_PAGE = gql`
query GetTimekeepingPage(
    $startTime: Date!, 
    $endTime: Date!,
    $deviceIds: [String!]!,
    $page: Float!,
    $perPage: Float!,
    $type: Float!
    $companyIds: [String!],
    $departmentIds: [String!],
    $cameraIds: [String!],
    $recognitionType: String!
    $textSearch: String!
){
    timekeepingPage(
        startTime:$startTime,
        endTime:$endTime,
        deviceIds: $deviceIds,
        page:$page,
        perPage:$perPage,
        type:$type,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        cameraIds: $cameraIds,
        recognitionType:$recognitionType,
        textSearch:$textSearch
    ){
        id,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        recognitionType,
        SrcDevice{id,name},
        recognitionType,
        userId,
        DebugInfos{
            detectDuration,
            vectorDuration,
            headposePitch,
            headposeRoll,
            headposeYaw,
            faceQualityScore,
            recogConfident,
            reqTimePoint,
            reqExDuration,
            searchDuration,
            topResultDebug,
            vectorHash,
            maskConfident,
            maskDuration,
            headposeDuration,
            qualityDuration
        },
        count,
    }
}
`;

export const GET_TIMEKEEPING_PAGE_SEARCH = gql`
query GetTimekeepingPageSearch(
    $startTime: Date!, 
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $type: Float!,
    $departmentIds: [Float!]!,
    $textSearch: String!
){
    timekeepingPageSearch(
        startTime:$startTime,
        endTime:$endTime,
        page:$page,
        perPage:$perPage,
        type:$type,
        departmentIds: $departmentIds
        textSearch:$textSearch
    ){
        id,
        cameraIp,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        count
    }
}
`;

export const GET_LAST_TIMEKEEPING = gql`
query GetLastTimekeeping{
    lastTimekeeping{
        id,
        cameraIp,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        SrcDevice{id,name}
    }
}
`;

export const GET_LAST_FACE_ID = gql`
query getLastFaceId{
    getLastFaceId{
        id,
        cameraIp,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type
    }
}
`;

export const EXPORT_TIMEKEEPING_BY_ID = gql`
query ExportDataById(
    $userId: String!,
    $startTime: Date!, 
    $endTime: Date!,
    $timeZoneOffset: String!
){
    timekeepingById(
        userId:$userId,
        startTime:$startTime,
        endTime:$endTime,
        timeZoneOffset:$timeZoneOffset,
    ){
        userId,
        integrationKey,
        fullName,
        department,
        day,
        workTime,
        earlyTime,
        lateTime,
        minTime,
        maxTime
    }
}
`;

export const ACSV_GET_TIMEKEEPING_BY_USERID = gql`
query AcsvGetDataByUserId(
    $userId: String!,
    $startTime: Date!, 
    $endTime: Date!,
    $timeZoneOffset: String!
){
    acsvTimekeepingById(
        userId:$userId,
        startTime:$startTime,
        endTime:$endTime,
        timeZoneOffset:$timeZoneOffset,
    ){
        userId,
        integrationKey,
        fullName,
        allData {
            data,
            day,
            dayWorkTime,
            nightWorkTime,
            isHoliday
        }
    }
}
`;

export const GET_UNKNOWN_DATA_PAGE = gql`
query GetUnknownPage(
    $startTime: Date!, 
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $trackingId: String!,
    $cameraId: Float!
){
    unknownPage(
        startTime:$startTime,
        endTime:$endTime,
        page:$page,
        perPage:$perPage,
        trackingId:$trackingId,
        cameraId: $cameraId
    ){
        trackingId,
        data {id, image, time, deviceId},
        count
    }
}
`;

export const GET_ALL_UNKNOWN_DATA_PAGE = gql`
query allUnkownGroupByTrackingId(
    $startTime: Date!, 
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $search: String!
){
    allUnkownGroupByTrackingId(
        startTime:$startTime,
        endTime:$endTime,
        page:$page,
        perPage:$perPage,
        search:$search
    ){
        trackingId,deviceId,cameraIp,channelName,images,count,time,SrcDevice{id,name}
    }
}
`;

export const GET_UNKNOWN = gql
    `
query getUnknown (
    $startTime: Date!,
    $endTime: Date!,
    $deviceIds: [String!]!,
    $page: Float!,
    $perPage: Float!,
){getUnknown
    (
       
        startTime: $startTime,
        endTime: $endTime,
        deviceIds: $deviceIds,
        page: $page,
        perPage: $perPage,
    ){
        trackingId,
        deviceId,
        cameraIp,
        channelName,
        image,
        time,
        count,
        SrcDevice{
            id,
            name,
            isTimekeepingDevice
        }
    }
}
`;

export const TIMEKEEPING_DEVICES = gql`
query timekeepingDevices{
    timekeepingDevices
        {
           id,
           deviceName,
           type,
           ip,
           status,
           statusUpdate,
           isTimekeepingDevice
        }
    }
`;

export const GET_HISTORY_BY_USER_ID = gql`
query getHistoryByUserId (
    $startTime: Date!,
    $endTime: Date!,
    $deviceIds: [String!]!,
    $page: Float!,
    $perPage: Float!,
    $userId: String!,
){
    getHistoryByUserId(
        startTime: $startTime,
        endTime: $endTime,
        deviceIds: $deviceIds,
        page: $page,
        perPage: $perPage,
        userId: $userId,
    ){
        id,
        userId,
        deviceId,
        type,
        time,
        cameraIp,
        dateCreated,
        dateModified,
        image,
        fullName,
        avatar,
        company,
        department,
        count,
        SrcDevice{
            id,
            name,
            isTimekeepingDevice
        },
        integrationKey
    }
}
`;

export const EDIT_SHIFT = gql`
mutation editShift (
    $userId: String!,
    $shiftId: String!,
    $startDate: Date!,
    $endDate: Date!,
    $checkinKey: Float,
    $checkoutKey: Float,
){
    editShift(
        userId: $userId,
        shiftId: $shiftId,
        startDate: $startDate,
        endDate: $endDate,
        checkinKey: $checkinKey,
        checkoutKey: $checkoutKey
    ){
        userId,
        email,
        name,
        startDate,
        endDate,
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        },
        checkinTime,
        checkoutTime,
        checkinKey,
        checkoutKey,
        dateCreated,
        dateModified
    }
}
`;

export const GET_UNKNOWN_DATA = gql`
query getUnknownGroup(
    $deviceIds: [String!]!, 
    $cameraIds: [String!]!, 
    $endTime: Date!, 
    $page: Float!, 
    $perPage: Float!, 
    $startTime: Date!,
    $search: String!
){
    getUnknownGroup(
        deviceIds: $deviceIds, 
        cameraIds: $cameraIds, 
        endTime: $endTime, 
        page: $page, 
        perPage: $perPage, 
        startTime: $startTime,
        search: $search
    ){
        cameraIp,
        time,
        trackingId,
        deviceId,
        SrcDevice{name},
        Camera{name},
        count
    }
}
`;

export const GET_FACE_REID_GROUP = gql`
query getFaceReIdGroup(
    $deviceIds: [String!]!, 
    $cameraIds: [String!]!, 
    $endTime: Date!, 
    $page: Float!, 
    $perPage: Float!, 
    $startTime: Date!,
    $search: String!,
    $minReIdConf: Float,
    $maxReIdConf: Float
){
    getFaceReIdGroup(
        deviceIds: $deviceIds, 
        cameraIds: $cameraIds, 
        endTime: $endTime, 
        page: $page, 
        perPage: $perPage, 
        startTime: $startTime,
        search: $search,
        minReIdConf: $minReIdConf,
        maxReIdConf: $maxReIdConf
    ){
        cameraIp,
        time,
        reId,
        deviceId,
        SrcDevice{name},
        Camera{name},
        count
    }
}
`;

export const GET_TIMEKEEPING_ARROUNG_ID = gql`
query getTimekeepingAroundId(
    $time: Date!,
    $recognizedUserId: String!,
){
    getTimeKeepingArroungId(
        time: $time,
        recognizedUserId: $recognizedUserId
    ){
        id,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        recognitionType,
        SrcDevice{id,name},
        recognitionType,
        userId,
        DebugInfos{
            detectDuration,
            vectorDuration,
            headposePitch,
            headposeRoll,
            headposeYaw,
            faceQualityScore,
            recogConfident,
            reqTimePoint,
            reqExDuration,
            searchDuration,
            topResultDebug,
            vectorHash,
        },
        count,
    }
}
`;

export const SEARCH_IGNORE_REID = gql`
query searchIgnoreReId(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
){
    searchIgnoreReId(
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        reId, count
    }
}
`;

export const UPSERT_IGNORE_REID = gql`
mutation upsertIgnoreReId(
    $reId: String!,
){
    upsertIgnoreReId(
        reId: $reId,
    )
}
`;

export const REMOVE_IGNORE_REID = gql`
mutation removeIgnoreReId(
    $reId: String!,
){
    removeIgnoreReId(
        reId: $reId,
    )
}
`;

export const GET_UNKNOWN_BY_ID = gql`
query getUnknownById(
    $id: Int!,
){
    getUnknownById(
        id: $id,
    ){
        id,
        trackId,
        userId,
        deviceId,
        type,
        time,
        cameraIp,
        dateCreated,
        dateModified,
        image,
        DebugInfos{
            detectDuration,
            faceQualityScore,
            headposePitch,
            headposeRoll,
            headposeYaw,
            vectorDuration,
            recogConfident,
            reqExDuration,
            reqTimePoint,
            searchDuration,
            reIdConf,
            faceKpts,
            maskConfident,
            maskDuration,
            headposeDuration,
            qualityDuration,
            detectTimepoint
        }
    }
}
`;