import { gql } from "@apollo/client";

export const GET_USER_EKYC_PAGE = gql`
query searchUserEkycIntegration (
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    searchUserEkycIntegration(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        userId,
        User{
            name,
            email,
            avatar,
            company {
                name,
                id
            },
            department{
                name,
                id
            }
        },
        token,
        ekycUrl,
        count
    }
}
`;

export const GET_USER_EKYC_BY_ID = gql`
query getUserEkycIntegrationById (
    $userId: String,
){
    getUserEkycIntegrationById(
        userId: $userId,
    ){
        userId,
        User{
            name,
            email,
            avatar,
            company {
                name,
                id
            },
            department{
                name,
                id
            }
        },
        token,ekycUrl
    }
}
`;

export const UPSERT_USER_EKYC = gql`
mutation upsertUserEkycIntegration(
    $userId: String!,
    $ekycUrl: String!,
    $token: String!,
){
    upsertUserEkycIntegration(
        userId: $userId,
        ekycUrl: $ekycUrl,
        token: $token,
    ){
        userId,token,ekycUrl
    }
}
`;

export const DELETE_USER_EKYC = gql`
mutation removeUserEkycIntegration(
    $userId: String!,
){
    removeUserEkycIntegration(
        userId: $userId,
    )
}
`;