import { gql } from "@apollo/client";

export const SEARCH_AC = gql`
query searchAC(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAC(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,name,userCount,userWaittingCount,comCount,depCount,orgCount,deviceCount,deviceGroupCount,cameraCount,acTimeCount,count
    }
}
`;

export const UPSERT_AC = gql`
mutation upsertAc(
    $acId: String,
    $name: String!,
    $multipleUserWaitTime: Float,
    $userNumberRequired: Float,
    $doorHoldTime: Float,
){
    upsertAc(
        acId: $acId,
        name: $name,
        multipleUserWaitTime: $multipleUserWaitTime,
        userNumberRequired: $userNumberRequired,
        doorHoldTime: $doorHoldTime,
    ){
        id,name
    }
}
`;

export const UPSERT_AC_USER = gql`
mutation upsertAcUser(
    $acId: String!,
    $userIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAcUser(
        acId: $acId,
        userIds: $userIds,
        isEnable: $isEnable,
    ){
        accessControlId,
        userId,
        User{id,name,email,avatarBase64{data,path}},
        isEnable
    }
}
`;

export const UPSERT_AC_DEP = gql`
mutation upsertAcDep(
    $acId: String!,
    $depIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAcDep(
        acId: $acId,
        depIds: $depIds,
        isEnable: $isEnable,
    ){
        departmentId,
        accessControlId,
        isEnable,
    }
}
`;

export const UPSERT_AC_COM = gql`
mutation upsertAcCom(
    $acId: String!,
    $comIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAcCom(
        acId: $acId,
        comIds: $comIds,
        isEnable: $isEnable,
    ){
        accessControlId,
        companyId,
        isEnable,
    }
}
`;

export const UPSERT_AC_ORG = gql`
mutation upsertAcOrg(
    $acId: String!,
    $orgIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAcOrg(
        acId: $acId,
        orgIds: $orgIds,
        isEnable: $isEnable,
    ){
        accessControlId,
        orgId,
        Org{id,name},
        isEnable
    }
}
`;

export const UPSERT_AC_DEVICE = gql`
mutation upsertAcDevice(
    $acId: String!,
    $deviceIds: [String!]!,
    $inOutMode: Float,
    $isEnable: Boolean!,
){
    upsertAcDevice(
        acId: $acId,
        deviceIds: $deviceIds,
        inOutMode: $inOutMode,
        isEnable: $isEnable,
    ){
        accessControlId,
        deviceId,
        Device{id,name},
        inOutMode,
        isEnable
    }
}
`;

export const UPSERT_AC_DEVICE_GROUP = gql`
mutation upsertAcDeviceGroup(
    $acId: String!,
    $deviceGroupIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAcDeviceGroup(
        acId: $acId,
        deviceGroupIds: $deviceGroupIds,
        isEnable: $isEnable,
    ){
        DeviceGroupInfo{id,name},
        accessControlId,
    }
}
`;

export const UPSERT_AC_CAMERA = gql`
mutation upsertAccessControlCamera(
    $acId: String!,
    $cameraIds: [String!]!,
    $isEnable: Boolean!,
){
    upsertAccessControlCamera(
        acId: $acId,
        cameraIds: $cameraIds,
        isEnable: $isEnable,
    ){
        accessControlId,
        cameraId,
        Camera{id,name},
        isEnable
    }
}
`;

export const UPSERT_AC_TIME = gql`
mutation upsertAcTime(
    $acId: String!,
    $acTimeId: String!,
    $name: String!,
    $startDate: Date!,
    $endDate: Date!,
    $startTime: String!,
    $endTime: String!,
    $weekDays: [Int!]!,
    $isEnable: Boolean!,
){
    upsertAcTime(
        acId: $acId,
        acTimeId: $acTimeId,
        name: $name,
        startDate: $startDate,
        endDate: $endDate,
        startTime: $startTime,
        endTime: $endTime,
        weekDays: $weekDays,
        isEnable: $isEnable,
    ){
        id
    }
}
`;

export const SEARCH_AC_USER = gql`
query searchAcUser(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcUser(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        userId,
        userRank,
        isEnable,
        User{
            id,
            name,
            email,
            avatar
        },
        replaceUserId,
        Replacer{
            id,
            name,
            email,
            avatar
        },
        startTime,
        endTime,
        count,
    }
}
`;

export const SEARCH_AC_DEP = gql`
query searchAcDep(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcDep(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        Department{id,name},
        count
    }
}
`;

export const SEARCH_AC_COM = gql`
query searchAcCom(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcCom(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        Company{id,name},
        count
    }
}
`;

export const SEARCH_AC_ORG = gql`
query searchAcOrg(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcOrg(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        Org{id,name},
        count
    }
}
`;

export const SEARCH_AC_DEVICE = gql`
query searchAcDevice(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcDevice(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        deviceId,
        Device{id,name},
        inOutMode,
        count
    }
}
`;

export const SEARCH_AC_DEVICE_GROUP = gql`
query searchAcDeviceGroup(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAcDeviceGroup(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        DeviceGroupInfo{id,name}
        count
    }
}
`;

export const SEARCH_AC_CAMERA = gql`
query searchAccessControlCamera(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchAccessControlCamera(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        Camera{id,name},
        count
    }
}
`;

export const SEARCH_AC_TIME = gql`
query searchAcTime(
    $acId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    searchAcTime(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        accessControlId,
        name,
        startDate,
        endDate,
        startTime,
        endTime,
        weekDays,
        isEnable,
        count
    }
}
`;

export const ADD_REPLACE_OPEN_USER = gql`
mutation addReplaceOpenUser(
    $userId: String!,
    $accessControlId: String!,
    $replaceOpenUserId: String!,
    $startTime: Date!,
    $endTime: Date!,
){
    addReplaceOpenUser(
        userId: $userId,
        accessControlId: $accessControlId,
        replaceOpenUserId: $replaceOpenUserId,
        startTime: $startTime,
        endTime: $endTime,
    ){
        userId,
        User{id,name,email,avatarBase64{data,path}},
        replaceUserId,
        startTime,
        endTime,
        Replacer{id,name,email,avatarBase64{data,path}},
        accessControlId,
    }
}
`;

export const REMOVE_REPLACE_OPEN_USER = gql`
mutation removeReplacer(
    $userId: String!,
    $accessControlId: String!,
){
    removeReplacer(
        userId: $userId,
        accessControlId: $accessControlId,
    )
}
`;