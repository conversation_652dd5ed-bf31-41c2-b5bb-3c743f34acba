import { gql } from "@apollo/client";

export const GET_VEHICLE_TRACK = gql`
query searchVehicleTrack(
    $startDate: Date!,
    $endDate: Date!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $colorFilter: String!,
    $vehicleType: Int,
    $showMode: Int
){
    searchVehicleTrack(
        startDate: $startDate,
        endDate: $endDate,
        search: $search,
        page: $page,
        perPage: $perPage,
        colorFilter: $colorFilter,
        vehicleType: $vehicleType,
        showMode: $showMode
    ){
        trackId, 
        cameraIp, 
        cameraId, 
        dateCreated, 
        dateModified, 
        minTime,
        maxTime,
        lastVehicleType,
        lastLicensPlate,
        isVehicleAllowed
        count, 
        Camera{
            id,
            name
        },
        TrackResults{
            id,
            imagePath,
            time,
            bodyR,
            bodyG,
            bodyB,
            licensePlate,
            count
        }
    }
}
`;

export const GET_VEHICLE_SEARCH_EVENT_PAGE = gql`
query vehicleSearchEventPage(
    $page: Float!,
    $perPage: Float!,
    $minConfident: Float,
    ){
        vehicleSearchEventPage(
            page: $page,
            perPage: $perPage,
            minConfident: $minConfident,
        ){
            id,
            imagePath,
            startSearchTime,
            endSearchTime,
            resultCount,
            count
        }
}
`;

export const RESEARCH_VEHICLE_EVENT = gql`
mutation reSearchVehicleEvent(
    $vehicleSearchEventId: String!,
){
    reSearchVehicleEvent(
        vehicleSearchEventId: $vehicleSearchEventId,
    )
}
`;

export const SEARCH_VEHICLE_BY_SERVER_PATH = gql`
mutation searchVehicleByServerPath(
    $serverFilePath: String!,
    $topN: Float!,
    $deviceId: String,
    $startTime: Date!,
    $endTime: Date!,
    $cameraIds: [String!]!,
){
    searchVehicleByServerPath(
        serverFilePath: $serverFilePath,
        topN: $topN,
        deviceId: $deviceId,
        startTime: $startTime,
        endTime: $endTime,
        cameraIds : $cameraIds,
    )
}
`;

export const GET_VEHICLE_SEARCH_RESULT_BY_ID = gql`
query getVehicleSearchResultById(
    $eventId: String!,
    $resultNumber: Float!,
    $minConfident: Float!,
){
    getVehicleSearchResultById(
        eventId: $eventId,
        resultNumber: $resultNumber,
        minConfident: $minConfident,
    ){
        id,
        image,
        Results{
            id,
            imagePath,
            time,
            bodyR,
            bodyG,
            bodyB,
            licensePlate
        },
        minConfidence,
        maxConfidence,
        minTime,
        maxTime,
        Cameras{
            id,
            name
        }
    }
}
`;

export const GET_VEHICLE_CAPTURED_BY_EVENT_ID = gql`
query getVehicleCapturedByEventId(
    $eventId: String!,
    $minConfident: Float!,
    ){
        getVehicleCapturedByEventId(
            eventId: $eventId,
            minConfident: $minConfident,
        ){id,eventId,image,imageId}
}
`;

export const GET_VEHICLE_TRACK_PAGE_BY_CAPTURED = gql`
query getVehicleTrackPageByCaptured(
    $captureId: String!,
    $minConfident: Float!,
    $page: Float!,
    $perPage: Float!,
    ){
        getVehicleTrackPageByCaptured(
            captureId: $captureId,
            minConfident: $minConfident,
            page: $page,
            perPage: $perPage,
        ){trackId,cameraIp,cameraId,Camera{id,name},minConf,maxConf,minTime,maxTime,count}
}
`;

export const GET_VEHICLE_IMAGE_RESULTBY_TRACK_ID = gql`
query getVehicleImageResultByTrackId(
    $captureId: String!,
    $trackId: String!,
    $resultNumber: Float!,
    $minConfident: Float!,
    ){
        getVehicleImageResultByTrackId(
            captureId: $captureId,
            trackId: $trackId,
            resultNumber: $resultNumber,
            minConfident: $minConfident,
        ){id,trackId,imagePath,imageId,time,bodyR,bodyG,bodyB,licensePlate}
}
`;