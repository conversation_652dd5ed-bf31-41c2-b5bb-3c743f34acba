import { gql } from "@apollo/client";

export const UPSERT_ATTENDANT_TIME = gql`
mutation upsertAttandantTime(
$attandantId: String,
$name: String!,
$startHour: String!,
$endHour: String!,
$isNoTimeLimit: Boolean!,
$isNoUserLimit: Boolean!,
$isActive: Boolean!,
)   {
    upsertAttandantTime(
        attandantId: $attandantId,
        name: $name,
        startHour: $startHour,
        endHour: $endHour,
        isActive: $isActive,
        isNoTimeLimit: $isNoTimeLimit,
        isNoUserLimit: $isNoUserLimit,
    ) 
    {id}
}
`;

export const GET_ALL_ATTENDANT_TIME = gql`
query getAllAttandantTime{
    getAllAttandantTime{
        id,
        name,
        startHour,
        endHour,
        timezoneOffset,
        isActive,
    }
}
`;

export const SEARCH_ATTENDANT_TIME = gql`
query searchAttandantTime(
    $search: String!,
    $page:Float!,
    $perPage:Float!
)
{searchAttandantTime
    (
        search:$search,
        page:$page,
        perPage:$perPage
    ){id,name,startHour,endHour,isActive,isNoTimeLimit,isNoUserLimit,deviceCount,count}
}
`;

export const GET_ATTENDANT_TIME_PROFILE = gql`
query getAttandantTimeProfile(
    $attendantTimeId: String!,
)
{getAttandantTimeProfile
    (
        attendantTimeId:$attendantTimeId,
    ){id,name,startHour,endHour,isActive,isNoTimeLimit,isNoUserLimit,deviceCount,devices{id,deviceName},count}
}
`;

export const GET_DEVICE_NOT_USING_ATTENDANT_TIME = gql`
query getDeviceNotUsingAttandantTime(
    $attandantId: String!,
)
{getDeviceNotUsingAttandantTime
    (
        attandantId:$attandantId,
    ){id,deviceName,count}
}
`;

export const ADD_DEVICE_TO_ATTENDANT_TIME = gql`
mutation addDeviceAttandantTime(
    $attandantId: String!,
    $deviceIds: [String!]!,
)
{addDeviceAttandantTime
    (
        attandantId:$attandantId,
        deviceIds:$deviceIds,
    ){deviceId}
}
`;

export const REMOVE_DEVICE_FROM_ATTENDANT_TIME = gql`
mutation removeDeviceAttandantTime(
    $attandantId: String!,
    $deviceIds: [String!]!,
)
{removeDeviceAttandantTime
    (
        attandantId:$attandantId,
        deviceIds:$deviceIds,
    ){deviceId}
}
`;

export const UPSERT_DEVICE_GROUP = gql`
mutation upsertDeviceGroup(
$deviceGroupId: String,
$name: String,
$deviceIds: [String!]!,


)   {
    upsertDeviceGroup(
        deviceGroupId: $deviceGroupId,
        name: $name,
        deviceIds: $deviceIds,
    ) 
    {
        id,
        name,
    }
}
`;

export const GET_ALL_DEVICE_GROUP_INFO = gql`
query queryAllDeviceGroupInfo{
    queryAllDeviceGroupInfo{
        id,
        name,
        deviceGroup{
            id,
            name
        }
    }
}
`;

export const GET_DEVICE_GROUP_INFO_BY_PAGE = gql`
query queryDeviceGroupInfoByPage(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    queryDeviceGroupInfoByPage(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        deviceGroup{
            id,
            name
        },
        count
    }
}
`;

export const GET_ATTENDANT_TIME_BY_PAGE = gql`
query getAttandantTimeByPage(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    getAttandantTimeByPage(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        deviceId,
        deviceGroupId,
        name,
        startHour,
        endHour,
        timezoneOffset,
        isActive,
        device{
            id,
            name
        }
        deviceGroup{
            id,
            name
        },
        count
    }
}
`;

export const ADD_USER_ATTENDANTS = gql`
mutation addUserAttendants(
$attendants: [AttendantTimeInput!]!
) 
{addUserAttendants
    (
        attendants: $attendants,
    )
}
`;

export const ADD_ORG_ATTENDANTS = gql`
mutation addOrganizationAttendants
(
$attendants: [AttendantTimeInput!]!
) 
{addOrganizationAttendants
    (
        attendants: $attendants,
    )
}
`;

export const QUERY_USER_ATTENDANT_TIME_ADMIN_PAGE = gql`
query queryUserAttendantTimeByPageForAdminPage(
    $attendantIds: [String!]!, 
    $deviceGroupIds: [String!]!, 
    $deviceIds: [String!]!, 
    $page: Int!, 
    $startDate: Date!,
    $endDate: Date!,
    $perPage: Int!,
    $userIds: [String!]!)
{queryUserAttendantTimeByPageForAdminPage
    (
        attendantIds: $attendantIds,
        deviceGroupIds: $deviceGroupIds,
        deviceIds: $deviceIds,
        startDate: $startDate,
        endDate: $endDate,
        page: $page,
        perPage: $perPage,
        userIds: $userIds,

    ){
        userId,
        attandantId,
        startDate,
        endDate,
        isActive,
        isChecked,
        AttandantTime{
            name,
            startHour,
            endHour,
            
        },
        userName,
        integrationKey,
        company,
        department,
        count
    }
}

`;

export const QUERY_USER_ATTENDANT_TIME_HISTORY = gql`
query queryUserAttendantTimeByPageRealTime(
    $attendantIds: [String!]!, 
    $deviceGroupIds: [String!]!, 
    $startDate: Date!,
    $endDate: Date!,
    $deviceIds: [String!]!, 
    $page: Int!, 
    $perPage: Int!,
    $userIds: [String!]!)
{queryUserAttendantTimeByPageRealTime
    (
        attendantIds: $attendantIds,
        deviceGroupIds: $deviceGroupIds,
        deviceIds: $deviceIds,
        startDate: $startDate,
        endDate: $endDate,
        page: $page,
        perPage: $perPage,
        userIds: $userIds,

    ){
        userId,
        attandantId,
        startDate,
        endDate,
        isActive,
        isChecked,
        checkTime,
        # isCheckedOut,
        # checkOutTime,
        AttandantTime{
            name,
            startHour,
            endHour
        },
        userName,
        integrationKey,
        company,
        department,
        count
    }
}

`;

export const DELETE_USER_ATTENDANT_TIME = gql`
mutation deleteUserAttendantTime(
    $userId: String!, 
    $attandantId: String!, 
    $startDate: Date!, 
    $endDate: Date!, 
){
    deleteUserAttendantTime(
        userId: $userId,
        attandantId: $attandantId,
        startDate: $startDate,
        endDate: $endDate,
    ) 
}
`;

export const DELETE_DEVICE_GROUP = gql`
mutation deleteDeviceGroupById(
    $deviceGroupId: String!, 
){
    deleteDeviceGroupById(
        deviceGroupId: $deviceGroupId,
    ){
        id,
        name
    }
}
`;

export const GET_LIST_DEVICE_BY_DEVICE_GROUP_ID = gql`
query getListDeviceByDeviceGroupId(
    $deviceGroupId: String!, 
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    getListDeviceByDeviceGroupId(
        deviceGroupId: $deviceGroupId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const DELETE_DEVICE_IN_DEVICE_GROUP = gql`
mutation deleteDeviceinDeviceGroup(
    $deviceGroupId: String!, 
    $deviceId: String!, 

){
    deleteDeviceinDeviceGroup(
        deviceGroupId: $deviceGroupId,
        deviceId: $deviceId,
    ){
        id,
    }
}
`;

export const UPDATE_USER_ATTENDANT_TIME = gql`
mutation updateUserAttendantTime(
    $userId: String!, 
    $oldAttandantId: String!, 
    $newAttandantId: String!, 
    $startDate: Date!, 
    $endDate: Date!, 
){
    updateUserAttendantTime(
        userId: $userId,
        oldAttandantId: $oldAttandantId,
        newAttandantId: $newAttandantId,
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;

export const SEARCH_ABSENT_USER = gql`
query searchAttUserInDay(
  $search: String!,
  $page:Float!,
  $perPage:Float!,
  $companyIds:[String!]!,
  $departmentIds:[String!]!,
  $deviceIds:[String!]!,
  $startTime: Date!,
  $endTime: Date!,
  $isAtt: Boolean!
){
    searchAttUserInDay(
        search: $search,
        page: $page,
        perPage: $perPage,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        deviceIds: $deviceIds,
        startTime: $startTime,
        endTime: $endTime,
        isAtt: $isAtt,
    ){
        id,
        fullName,
        avatar,
        title,
        email,
        Department{id,name},
        Company{id,name},
        count,
        firstIn,
        lastOut,
    }
}
`;

export const DELETE_ATTANDANT_TIME = gql`
mutation deleteAttandantTime(
  $attTimeId: String!,
){deleteAttandantTime
  (
    attTimeId: $attTimeId,
  )
}
`;