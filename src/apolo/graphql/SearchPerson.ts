import { gql } from "@apollo/client";

export const SEARCH_FEATURE_IMAGE_PAGE = gql`
query searchFeatureImagePage(
    $page: Float!,
    $perPage: Float!,
    $status: Float!,
    $startTime: Date!,
    $endTime: Date!,
    $confident: [Float!]!
){
    searchFeatureImagePage(
        page: $page,
        perPage: $perPage,
        status: $status,
        startTime: $startTime,
        endTime: $endTime,
        confident: $confident,
    ){
        id,
        gender,
        upperColor,
        lowerColor,
        isBag,
        startTime,
        endTime,
        isRunning,
        isSearchFace,
        isSearchFeature,
        count,
        srcImgPath,
        srcImgBase64,
        startTime,endTime,
        totalMctResult,
        totalUserResult,
        totalFaceResult
    }
}
`;

export const DELETE_SMART_SEARCH_EVENT = gql`
mutation deleteSearchFeatureImageEvent(
    $eventId: String!
){
    deleteSearchFeatureImageEvent(
        eventId: $eventId
    )
}
`;

export const GET_SMART_SEARCH_MCT_RESULTS = gql`
query getEventMctResults(
    $eventId: String!,
    $page: Float!,
    $perPage: Float!
){
    getEventMctResults(
        eventId: $eventId,
        page: $page,
        perPage: $perPage
    ){ 
        personImageBase64, 
        MctResults{ 
            imageBase64, confidence, time, cameraId, cameraIp, count 
        }, 
        count 
    }
}
`;

export const GET_SMART_SEARCH_HUMAN_CAPTURED = gql`
query getEventMctCapturedHumans(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $confident: Float!
){
    getEventMctCapturedHumans(
            page: $page,
            perPage: $perPage,
            eventId: $eventId,
            confident: $confident
    ){
        personCaptureId, personImagePath, count
    }
}
`;

export const GET_SMART_SEARCH_MCT_TRACK_INFO = gql`
query getEventMctTrackInfoResults(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $personCaptureId: String!,
    $confident: Float!
){
    getEventMctTrackInfoResults(
        page: $page,
        perPage: $perPage,
        eventId: $eventId,
        personCaptureId: $personCaptureId,
        confident: $confident
    ){
        MctResultGroupByTrack{
            maxConf,
            minConf,
            maxTime,
            minTime,
            HumanTrackInfo{
                cameraId,
                cameraIp,
                Camera{
                    name
                },
                trackId,
                attribute
            },
            count
        }
    }
}
`;

export const GET_SMART_SEARCH_MCT_TRACK_RESULT = gql`
query getEventMctTrackResults(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $personCaptureId: String!,
    $trackId: String!,
    $confident: Float!
){
    getEventMctTrackResults(
        page: $page,
        perPage: $perPage,
        eventId: $eventId,
        personCaptureId: $personCaptureId,
        trackId: $trackId,
        confident: $confident
    ){
        MctResults{
            imagePath,
            confidence,
            time,
            cameraId,
            cameraIp,
            count
        }
    }
}
`;

export const GET_SMART_SEARCH_USER_RESULTS = gql`
query getEventUserResults(
    $eventId: String!,
    $page: Float!,
    $perPage: Float!,
    $confident: Float!
){
    getEventUserResults(
        eventId: $eventId,
        page: $page,
        perPage: $perPage,
        confident: $confident
    ){ 
        userName,
        userAvatarPath, 
        userMaskConfidence, 
        userNoMaskConfidence, 
        count 
    }
}
`;

export const GET_SMART_SEARCH_FACE_RESULTS = gql`
query getEventFaceResults(
    $eventId: String!,
    $page: Float!,
    $perPage: Float!
){
    getEventFaceResults(
        eventId: $eventId,
        page: $page,
        perPage: $perPage
    ){ 
        imageBase64, 
        similarity, 
        time, 
        Camera{ 
            name, 
            id 
        }, 
        cameraIp, 
        count 
    }
}
`;

export const GET_SMART_SEARCH_FACE_CAPTURED = gql`
query getEventFaceCapturedHumans(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $confident: Float!
){
    getEventFaceCapturedHumans(
            page: $page,
            perPage: $perPage,
            eventId: $eventId,
            confident: $confident
    ){
        faceCaptureId, 
        faceCaptureImagePath, 
        count
    }
}
`;

export const GET_SMART_SEARCH_FACE_TRACK_INFO = gql`
query getEventFaceTrackInfoResults(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $personCaptureId: String!,
    $confident: Float!
){
    getEventFaceTrackInfoResults(
        page: $page,
        perPage: $perPage,
        eventId: $eventId,
        personCaptureId: $personCaptureId,
        confident: $confident
    ){
        FaceResultGroupByTrack{
            trackId,
            maxConfidence,
            minConfidence,
            maxTime,
            minTime,
            Camera{name},
            cameraId,
            cameraIp,
            count
        }
    }
}
`;

export const GET_SMART_SEARCH_FACE_TRACK_RESULT = gql`
query getEventFaceTrackResults(
    $page: Float!,
    $perPage: Float!,
    $eventId: String!,
    $personCaptureId: String!,
    $trackId: String!,
    $confident: Float!
){
    getEventFaceTrackResults(
        page: $page,
        perPage: $perPage,
        eventId: $eventId,
        personCaptureId: $personCaptureId,
        trackId: $trackId,
        confident: $confident
    ){
        FaceResults{
            imageBase64,
            similarity,
            time,
            cameraId,
            cameraIp,
            count,
            FaceResult{id,time,image},
            OrphanFaceResult{id,time,image},
            FaceResult{id,time,image},
        }
    }
}
`;

export const RESEARCH_EVENT = gql`
mutation ReSearchEvent(
    $smartSearchEventId: String!
){
    reSearchEvent(
        smartSearchEventId: $smartSearchEventId
    )
}
`;

export const FIND_EVENT_VIDEOS = gql`
query findEventVideos(
    $deviceId: String!,
    $cameraId: String,
    $cameraIp: String,
    $time: Date!,
    $prevSelect: Float,
    $aboveSelect: Float,
){
    findEventVideos(
        deviceId: $deviceId,
        cameraId: $cameraId,
        cameraIp: $cameraIp,
        time: $time,
        prevSelect: $prevSelect,
        aboveSelect: $aboveSelect,
    ){
        deviceId, srcVideoId, cameraId, cameraIp, path, fileLastModified, videoType, duration, count
    }
}
`;

export const TEST_LIVE_STREAM = gql`
query testLiveStream(
    $cameraId: String,
    $time: Date!
){
    testLiveStream(
        cameraId: $cameraId,
        time: $time
    ){
        deviceId,
        srcVideoId,
        cameraId,
        cameraIp,
        path,
        fileLastModified,
        videoType,
        count
    }
}
`;

export const GET_HUMAN_TRACKING_INFO_PAGE = gql`
query getHumanTrackingPage(
    $page: Float!,
    $perPage: Float!,
    $attributeFilter: [Float!]!,
    $startDate: Date!,
    $endDate: Date!,
    $colorFilter: [String!],
    $deviceIds: [String!],
    $cameraIds: [String!],
){
    getHumanTrackingPage(
        page: $page,
        perPage: $perPage,
        attributeFilter: $attributeFilter,
        startDate: $startDate,
        endDate: $endDate,
        colorFilter: $colorFilter,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds
    ){ 
        cameraId,
        nxCameraId, 
        cameraIp, 
        Camera{ name }, 
        deviceId, 
        trackId, 
        attribute, 
        confidents, 
        count, 
        dateModified, 
        firstSeenTime, 
        BestShot{ path, path, bodyR, bodyG, bodyB, lowerR, lowerG, lowerB } 
    }
}
`;

export const GET_HUMAN_TRACKING_RESULT_PAGE = gql`
query getHumanTrackingResultPage(
    $page: Float!,
    $perPage: Float!,
    $deviceId: String!,
    $trackingId: String!,
    $colorFilter: [String!],
){
    getHumanTrackingResultPage(
        page: $page,
        perPage: $perPage,
        deviceId: $deviceId,
        trackingId: $trackingId,
        colorFilter: $colorFilter,
    ){ path, time, index, bodyR, bodyG, bodyB, lowerR, lowerG, lowerB, headPercent, bodyPercent, lowerPercent, count,
        uniform_conf, emp_card_conf, bad_kpt_head, bad_kpt_body, bad_kpt_lower, male_conf, female_conf, child_conf, adult_conf, backpack_conf, hat_conf, long_hair_conf, short_hair_conf, long_sleeve_conf, short_sleeve_conf, trouser_jeans_conf, skirt_conf, shorts_conf,
        isHaveFace, imageId
     }
}
`;

export const FIND_NX_EVENT_VIDEOS = gql`
query FindNxEventVideos(
    $deviceId: String!,
    $time: Date!,
    $aboveSelect: Float,
    $cameraId: String,
    $cameraIp: String,
    $prevSelect: Float
){
    findNxEventVideos(
        deviceId: $deviceId,
        time: $time,
        aboveSelect: $aboveSelect,
        cameraId: $cameraId,
        cameraIp: $cameraIp,
        prevSelect: $prevSelect
    ){
        cameraId
        cameraIp
        count
        deviceId
        duration
        fileLastModified
        path
        videoType
        srcVideoId
    }
}
`;

export const SEARCH_BY_SERVER_PATH = gql`
mutation searchByServerPath(
    $serverFilePath: String!,
    $startTime: Date!,
    $endTime: Date!,
    $select: Float!,
    $isSearchFace: Boolean!,
    $isSearchMct: Boolean!,
    $threshold: Float!,
    $topN: Float!,
    $cameraIds: [String!]!,
){
    searchByServerPath(
        serverFilePath: $serverFilePath,
        startTime: $startTime,
        endTime: $endTime,
        select: $select,
        isSearchFace: $isSearchFace,
        isSearchMct: $isSearchMct,
        threshold: $threshold,
        topN: $topN,
        cameraIds: $cameraIds,
    )
}
`;

export const SEARCH_EVENT_VIDEO_AI_BOX = gql`
query searchEventVideoAiBox(
    $page: Float!,
    $perPage: Float!,
    $deviceIds: [String!],
    $cameraIds: [String!],
    $cameraIps: [String!],
    $startTime: Date!,
    $endTime: Date!,
){
    searchEventVideoAiBox(
        page: $page,
        perPage: $perPage,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds,
        cameraIps: $cameraIps,
        startTime: $startTime,
        endTime: $endTime,
    ){
        deviceId, 
        srcVideoId, 
        cameraId, 
        cameraIp, 
        path, 
        thumnail, 
        startTime, 
        endTime, 
        metaData, 
        duration, 
        count
    }
}
`;

export const GET_NONE_UNIFORM_PAGE = gql`
query getNonUniformPage(
    $page: Float!,
    $perPage: Float!,
    $startDate: Date!,
    $endDate: Date!,
    $deviceIds: [String!],
    $cameraIds: [String!],
    $locationIds: [String!],
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
    $isFaceRecognizedOnly: Boolean,
    $attFilterMode: Int,
    $violationMode: Int,
){
    getNonUniformPage(
        page: $page,
        perPage: $perPage,
        startDate: $startDate,
        endDate: $endDate,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds,
        locationIds: $locationIds,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
        isFaceRecognizedOnly: $isFaceRecognizedOnly,
        attFilterMode: $attFilterMode,
        violationMode: $violationMode,
    ){ 
        cameraId,
        nxCameraId, 
        cameraIp, 
        Camera{ name }, 
        deviceId, 
        trackId, 
        attribute, 
        confidents, 
        count, 
        dateModified, 
        firstSeenTime, 
        BestShot{ path, path, bodyR, bodyG, bodyB, lowerR, lowerG, lowerB },
        User{id,name,email,avatar, firstIn, lastOut, Company{id,name}, Department{id,name}},
        isUniform, isWearingCard, firstIn, lastOut, lastOutDevice{id, name, deviceName, type, ip}
    }
}
`;

export const COUNT_NON_UNIFORM_BY_LOCATION = gql`
query countNonUniformByLocation(
    $locationId: String!,
    $startTime: Date!,
    $endTime: Date!,
){
    countNonUniformByLocation(
        locationId: $locationId,
        startTime: $startTime,
        endTime: $endTime
    )
}
`;

export const COUNT_BIDV_ACTIVE_GUEST = gql`
query countBidvActiveGuestByLocation(
    $locationId: String!,
    $startTime: Date!,
    $endTime: Date!,
){
    countBidvActiveGuestByLocation(
        locationId: $locationId,
        startTime: $startTime,
        endTime: $endTime
    )
}
`;

export const COUNT_BIDV_REID_BY_LOCATION = gql`
query countBidvReidByLocation(
    $locationId: String!,
    $startTime: Date!,
    $endTime: Date!,
){
    countBidvReidByLocation(
        locationId: $locationId,
        startTime: $startTime,
        endTime: $endTime
    )
}
`;

export const GET_BIDV_DASHBOARD = gql`
query getBidvDashboard(
    $startTime: Date!,
    $endTime: Date!,
){
    getBidvDashboard(
        startTime: $startTime,
        endTime: $endTime
    ){
        locationId,
        Location {
            id, name
        },
        totalUser,
        totalNoUniform,
        totalUniform,
        totalGuest,
        totalUnknown,
        count,
    }
}
`;

export const GET_NON_UNIFORM_HISTORY_BY_USER_ID = gql`
query getNonUniformHistoryByUserId(
    $page: Float!,
    $perPage: Float!,
    $filterUserId: String!,
    $startDate: Date!,
    $endDate: Date!,
){
    getNonUniformHistoryByUserId(
        page: $page,
        perPage: $perPage,
        filterUserId: $filterUserId,
        startDate: $startDate,
        endDate: $endDate
    ){
        cameraId,
        nxCameraId, 
        cameraIp, 
        Camera{ id, name }, 
        deviceId, 
        trackId, 
        attribute, 
        confidents, 
        count, 
        dateModified, 
        firstSeenTime, 
        User{id,name,email,avatar, firstIn, lastOut, Company{id,name}, Department{id,name}},
        isUniform, isWearingCard, lastOutDevice{id, name, deviceName, type, ip},
        Location { id, name }
    }
}
`;

export const GET_BIDV_MOVEMENT_HISTORY_BY_USER_ID = gql`
query getBidvUserMovementHistory(
    $page: Float!,
    $perPage: Float!,
    $filterUserId: String!,
    $startDate: Date!,
    $endDate: Date!,
){
    getBidvUserMovementHistory(
        page: $page,
        perPage: $perPage,
        filterUserId: $filterUserId,
        startDate: $startDate,
        endDate: $endDate
    ){
        locationId,
        Location { id, name },
        deviceId,
        Device { id, name },
        time, image , count
    }
}
`;

export const SEARCH_LATE_OR_NOT_IN = gql`
query searchLateOrNotIn(
    $startDate: String,
    $endDate: String,
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
    $startHour: String,
){
    searchLateOrNotIn(
        startDate: $startDate,
        endDate: $endDate,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
        startHour: $startHour
    ){
        name, email, avatar, title,
        Department { id, name }, Company { id, name },
        firstIn, lastOut,
    }
}
`;

export const SEARCH_LATE_ONLY = gql`
query searchLateOnly(
    $startDate: String,
    $endDate: String,
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
    $startHour: String,
){
    searchLateOnly(
        startDate: $startDate,
        endDate: $endDate,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
        startHour: $startHour
    ){
        name, email, avatar, title, integrationKey,
        Department { id, name }, Company { id, name },
        firstIn, lastOut, 
        lateMinutes, earlyMinutes
    }
}
`;

export const SEARCH_EARLY_ONLY = gql`
query searchEarlyOnly(
    $startDate: String,
    $endDate: String,
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
    $endHour: String,
){
    searchEarlyOnly(
        startDate: $startDate,
        endDate: $endDate,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
        endHour: $endHour
    ){
        name, email, avatar, title, integrationKey,
        Department { id, name }, Company { id, name },
        firstIn, lastOut, 
        lateMinutes, earlyMinutes
    }
}
`;

export const SEARCH_EARLY_OR_NOT_OUT = gql`
query searchEarlyOrNotOut(
    $startDate: String,
    $endDate: String,
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
    $endHour: String,
){
    searchEarlyOrNotOut(
        startDate: $startDate,
        endDate: $endDate,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
        endHour: $endHour
    ){
        name, email, avatar, title,
        Department { id, name }, Company { id, name },
        firstIn, lastOut,
    }
}
`;