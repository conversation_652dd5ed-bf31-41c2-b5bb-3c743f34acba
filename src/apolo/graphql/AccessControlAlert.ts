import { gql } from "@apollo/client";

export const SEARCH_AC_ALERT = gql`
query searchAccessControlAlert(
    $acIds:[String!]!,
    $search:String!,
    $page:Int!,
    $perPage:Int!,
    $startTime:Date!,
    $endTime:Date!,
    $acAlertTypes:[Int!],
    $isRead:Boolean,
) {
    searchAccessControlAlert(
        acIds:$acIds,
        search:$search,
        page:$page,
        perPage:$perPage,
        startTime:$startTime,
        endTime:$endTime,
        acAlertTypes:$acAlertTypes,
        isRead:$isRead,
    ){
        id,time,messeage,isRead,
        AccessControl{id,name},
        AccessControlUser{
            User{id,email,name,title,avatar},
            Replacer{id,email,name,title,avatar},
            startTime,endTime,
            actionType,invalidType
        },
        AccessControlUserLog{
            isValid,invalidType,
            FaceResult{
                id,image,time,srcDeviceId,
                User{id,name,email,title,avatar}
            }
        },
        Unknown{
            id,time,image,deviceId,
        },
        AcOpenLog{
            openDeviceId, time, isOpen, type, localId,
            OpenDevice{
                id,name
            },
            openUserIds, OpenUsers{
                id,name,email,title,avatar
            },
            scanUserIds, ScanUsers{
                id,name,email,title,avatar,userRank
            },
            invalidUserIds, InvalidUsers{
                id,name,email,title,avatar,userRank
            },
            missingUserIds, MissingUsers{
                id,name,email,title,avatar,userRank
            },
            totalIn, totalOut, count
        },
        UserRegis{
            id,email,name,title,avatar,idCardNumber,idCardFaceSimilarity
        },
        cachedImagePath,
        acDeviceIds,
        count
    }
}
`;