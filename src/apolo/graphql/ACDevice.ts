import { gql } from "@apollo/client";

export const ADD_AC_DEVICE = gql`
mutation AddACDevice(
    $deviceIds: [String!]!,
    $locationId: String!
) {addACDevice (
    deviceIds: $deviceIds
    locationId: $locationId
){
    deviceId,
    deviceName
}}
`;

export const DELETE_AC_DEVICE = gql`
mutation RemoveACDevice(
    $locationId: String!,
    $deviceIds: [String!]!
) {removeACDevice (
    locationId: $locationId,
    deviceIds: $deviceIds
)}
`;