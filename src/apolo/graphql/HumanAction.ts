import { gql } from "@apollo/client";

export const SEARCH_HUMAN_ACTION = gql`
query searchHumanAction(
    $actionTypes: [Float!]!,
    $startTime: Date!,
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
){
    searchHumanAction(
        actionTypes: $actionTypes,
        startTime: $startTime,
        endTime: $endTime,
        page: $page,
        perPage: $perPage,
    ){
        id,
        deviceId,
        cameraId,
        cameraIp,
        Camera{name},
        trackId,
        action,
        confident,
        startTime,
        endTime,
        base64Image,
        listImage,
        count
    }
}
`;