import { gql } from "@apollo/client";

//#region CREATE
//#endregion

//#region READ
export const GET_ALL_SERVER_CONFIG = gql`
query GetAllServerConfig {
    allServerConfig{key,value,dateModified} }
`;

export const SEARCH_SERVER_CONFIG = gql`
query SearchServerConfig(
    $search: String!
) {
searchServerConfig(
    search: $search
){
    key,
    value,
    dataType,
    dateModified
    }
}
`;
//#endregion

//#region UPDATE
export const UPDATE_SERVER_CONFIG = gql`
mutation SetConfig(
    $key: String!,
    $value: String!
    ) {
  updateServerConfig(
      key: $key,
      value: $value
  ) {key,value}
  }
`;
//#endregion

export const GET_ALL_PM2_THREADS = gql`
query AllPm2Threads(
    $page: Float!, 
    $perPage: Float!
){
    allPm2Threads(
        page: $page, 
        perPage: $perPage
    ){
        count
        dateCreated
        dateModified
        isRunning
        threadName
        type
    }
}
`;

export const UPSERT_PM2_THREAD = gql`
mutation UpsertPm2Thread(
    $threadName: String!, 
    $type: Float!
){
    upsertPm2Thread(
        threadName: $threadName, 
        type: $type
    )
}
`;

export const RESTART_PM2_THREAD = gql`
mutation restartThread(
    $password: String!, 
    $threadName: String!, 
    $tokenType: Float
){
    restartThread(
        password: $password, 
        threadName: $threadName, 
        tokenType: $tokenType
    )
}
`;

export const RESTART_CORE_BACKEND = gql`
mutation restartBackEnd(
    $password: String!,
    $tokenType: Float
){
    restartBackEnd(
        password: $password, 
        tokenType: $tokenType
    )
}
`;

export const RESTART_SCHEDULER = gql`
mutation restartScheduler(
    $password: String!, 
    $tokenType: Float
){
    restartScheduler(
        password: $password, 
        tokenType: $tokenType
    )
}
`;

export const RESTART_REPORT = gql`
mutation restartReport(
    $password: String!, 
    $tokenType: Float
){
    restartReport(
        password: $password, 
        tokenType: $tokenType
    )
}
`;

export const RESTART_FE= gql`
mutation restartFE(
    $password: String!, 
    $tokenType: Float
){
    restartFE(
        password: $password, 
        tokenType: $tokenType
    )
}
`;

export const DELETE_THREAD = gql`
mutation deleteThread(
    $password: String!, 
    $threadName: String!, 
    $tokenType: Float
){
    deleteThread(
        password: $password, 
        threadName: $threadName, 
        tokenType: $tokenType
    )
}
`;