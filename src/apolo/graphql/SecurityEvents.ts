import { gql } from "@apollo/client";

export const GET_ALARM_EVENT_BY_FILTER = gql`

query getAlarmEventByFilter(
    $types: [Float!],
    $text: String!,
    $startTime: Date!,
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    ){
        getAlarmEventByFilter(
            types: $types,
            text: $text,
            startTime: $startTime,
            endTime: $endTime,
            page: $page,
            perPage: $perPage,
        ){
            id,
            cameraIp,
            cameraId,
            Camera{id,name},
            deviceId,
            trackId,
            type,
            filePath,
            dateCreated,
            count
        }
}
`;