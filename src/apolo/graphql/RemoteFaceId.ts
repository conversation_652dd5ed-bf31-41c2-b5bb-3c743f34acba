import { gql } from "@apollo/client";

export const SET_REMOTE_LIMIT = gql`
mutation setRemoteLimit(
  $timesLimit: Float!,
  $dateLimit: Date,
  $userIds: [String!],
  $depIds: [String!],
  $comIds: [String!],
  $orgIds: [String!],
){
setRemoteLimit(
    timesLimit:$timesLimit,
    dateLimit:$dateLimit,
    userIds:$userIds,
    depIds:$depIds,
    comIds:$comIds,
    orgIds:$orgIds,
)
}
`;

export const GET_REMOTE_DEP_LIMIT = gql`
query getDepRemoteLimit(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
){getDepRemoteLimit
    (
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        departmentId,Department{id,name},limitTimes,dateLimit,count
    }
}
`;

export const GET_REMOTE_COM_LIMIT = gql`
query getComRemoteLimit(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
){getComRemoteLimit
    (
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        companyId,Company{id,name},limitTimes,dateLimit,count
    }
}
`;

export const GET_REMOTE_ORG_LIMIT = gql`
query getOrgRemoteLimit(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
){getOrgRemoteLimit
    (
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        orgId,Organization{id,name},limitTimes,dateLimit,count
    }
}
`;

export const GET_APPROVE_HISTORY = gql`
query searchApproveHistory(
    $remoteId: Float!,
){searchApproveHistory
    (
        remoteId: $remoteId,
    ){
        id,
        detail,
        time,
        status,
        ApproverHistory{
            id,
            oldStatus,
            newStatus,
            dateCreated,
            Approver{
                id,
                name,
                email,
                avatar
            }
        }
    }
}
`;