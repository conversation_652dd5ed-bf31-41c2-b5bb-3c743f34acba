import { gql } from "@apollo/client";

// export const GET_ALL_TIME_SHIFT = gql`
// query GetAllTimeShift {getAllShift
//     {
//         id,
//         name
//     }
// }
// `;

export const GET_TIME_SHIFT_BY_ID = gql`
query GetShiftById (
    $id: Float!,
){getShiftById
    (
        id: $id,
    ){
        id,
        name,
        payRate,
        startRecordTime,
        endRecordTime,
        startWorkTime,
        endWorkTime,
        startBreakTime,
        endBreakTime,
        arriveLateMinutes,
        leaveSoonMinutes,
        dayOfWeeks
    }
}
`;

export const GET_ALL_TIME_SHIFT = gql`
query getShifts
{getShifts
{
        code, 
        totalWorkHour,
        startHour,
        endHour,
        breakCode, 
        startBreak,
        endBreak,
        breakwithPaid,
        breakwithoutPaid, 
        breakBonus,
        work,
        actualWorkHour,
        overnight,
    }
}
`;

export const GET_SHIFTS_BY_PAGE = gql`
query getShiftsByPage(
$page: Float!, 
$perPage: Float!,
$search: String!, 
){getShiftsByPage
    (
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        code, 
        description,
        totalWorkHour,
        startHour,
        endHour,
        breakCode, 
        startBreak,
        endBreak,
        breakwithPaid,
        breakwithoutPaid, 
        breakBonus,
        work,
        actualWorkHour,
        overnight,
        autoDays,
        count
    }
} 
`;

export const SEARCH_SHIFT_USERS = gql`
query queryShift (
    $userId: String!,
    $startTime: Date!,
    $endTime: Date!,
){queryShift
    (
        userId: $userId,
        startTime: $startTime,
        endTime: $endTime,
    ){
        userId,
        email,
        name,
        startDate,
        endDate,
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        },
        ShiftExplainations{
            id,startExplainTime,endExplainTime,explainType,explainText,status,count
        },
        checkinTime,
        checkoutTime,
        checkinKey,
        checkoutKey,
        color,
        dateCreated,
        dateModified,
        faceResultIn{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        },
        faceResultOut{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        }
    }
}
`;

export const QUERY_SHIFT = gql`
query MonthQueryShift(
    $endTime: Date!, 
    $startTime: Date!, 
    $userId: String!
){
    monthQueryShift(
        endTime: $endTime, 
        startTime: $startTime, 
        userId: $userId
    ){
        userId,
        email,
        name,
        startDate,
        endDate,
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        },
        ShiftExplainations{
            id,startExplainTime,endExplainTime,explainType,explainText,status,count
        },
        checkinTime,
        checkoutTime,
        checkinKey,
        checkoutKey,
        color,
        dateCreated,
        dateModified,
        faceResultIn{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        },
        faceResultOut{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        }
    }
}
`;

export const CREATE_SHIFT_TYPE = gql`
mutation addShiftType(
    $code: String!, 
    $description: String, 
    $totalWorkHour: Float!,
    $startHour: String!,
    $endHour: String!,
    $breakCode: String!, 
    $startBreak: String!,
    $endBreak: String!,
    $breakwithPaid: Float!,
    $breakwithoutPaid: Float!, 
    $breakBonus: Float!,
    $work: Float!,
    $actualWorkHour: Float!,
    $overnight: Float!,
    $autoDays: String,
){
    addShiftType(
        code: $code, 
        description: $description,
        totalWorkHour: $totalWorkHour,
        startHour: $startHour,
        endHour: $endHour,
        breakCode: $breakCode, 
        startBreak: $startBreak,
        endBreak: $endBreak,
        breakwithPaid: $breakwithPaid,
        breakwithoutPaid: $breakwithoutPaid, 
        breakBonus: $breakBonus,
        work: $work,
        actualWorkHour: $actualWorkHour,
        overnight: $overnight,
        autoDays: $autoDays,
    ){
        code, 
        description,
        totalWorkHour,
        startHour,
        endHour,
        breakCode, 
        startBreak,
        endBreak,
        breakwithPaid,
        breakwithoutPaid, 
        breakBonus,
        work,
        actualWorkHour,
        overnight,
        autoDays
    }
}
`;

export const UPDATE_SHIFT_TYPE = gql`
mutation upsertShiftType(
    $code: String!, 
    $description: String, 
    $totalWorkHour: Float!,
    $startHour: String!,
    $endHour: String!,
    $breakwithPaid: Float!,
    $startBreak: String,
    $endBreak: String,
    $autoDays: String,
) {
    upsertShiftType(
        code: $code, 
        description: $description,
        totalWorkHour: $totalWorkHour,
        startHour: $startHour,
        endHour: $endHour,
        breakwithPaid: $breakwithPaid,
        startBreak: $startBreak,
        endBreak: $endBreak,
        autoDays: $autoDays,
    ){
        code, 
        description,
        totalWorkHour,
        startHour,
        endHour,
        breakCode, 
        startBreak,
        endBreak,
        breakwithPaid,
        breakwithoutPaid, 
        breakBonus,
        work,
        actualWorkHour,
        overnight,
        autoDays
    }
}
`;

export const DELETE_SHIFT_TYPE = gql`
mutation deleteShiftType(
    $code: String!,
    $force: Boolean
){
    deleteShiftType(
        code: $code,
        force: $force
    )
}
`;

export const GET_USER_SHIFT = gql`
query getUserShift(
    $page: Int!, 
    $pageSize: Int!,
    $search: String!,
) {
    getUserShift(
        page: $page,
        pageSize: $pageSize,
        search: $search,
    ){
        userId,
        name,
        email,
        startDate,
        endDate
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        }
    }
}
`;

export const SEARCH_USER_SHIFT_BY_USER = gql`
query searchUserShiftByUser(
    $page: Int!, 
    $pageSize: Int!,
    $userId: String!,
    $shiftId: String!,
) {
    searchUserShiftByUser(
        page: $page,
        pageSize: $pageSize,
        userId: $userId,
        shiftId: $shiftId
    ){
        userId,
        name,
        email,
        startDate,
        endDate,
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        }
    }
}
`;

export const CREATE_USER_SHIFT = gql`
mutation addUserShift(
    $userId: String!, 
    $shiftId: String!,
    $startDate: String!, 
    $endDate: String!,
) {
    addUserShift(
        userId: $userId,
        shiftId: $shiftId,
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;

export const ADD_USER_SHIFTS = gql`
mutation addUserShifts(
    $shifts: [ShiftUserInput!]!
) {
    addUserShifts(
        shifts: $shifts,
    )
}
`;

export const ADD_ORG_SHIFTS = gql`
mutation addOrganizationShifts(
    $shifts: [ShiftUserInput!]!
) {
    addOrganizationShifts(
        shifts: $shifts,
    )
}
`;

export const ADMIN_SHIFT_PAGE = gql`
query adminShiftPage (
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $startTime: Date!,
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $listAll: Boolean!,
){
    adminShiftPage(
        userIds: $userIds,
        departmentIds: $departmentIds
        startTime: $startTime,
        endTime: $endTime,
        page: $page,
        perPage: $perPage,
        listAll: $listAll
    ){
        userId,
        count,
        integrationKey,
        departmentName,
        email,
        name,
        startDate,
        endDate,
        shift{
            code, 
            totalWorkHour,
            startHour,
            endHour,
            breakCode, 
            startBreak,
            endBreak,
            breakwithPaid,
            breakwithoutPaid, 
            breakBonus,
            work,
            actualWorkHour,
            overnight,
        },
        checkinTime,
        checkoutTime,
        checkinKey,
        checkoutKey,
        color,
        dateCreated,
        dateModified,
        faceResultIn{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        },
        faceResultOut{
            id,
            userId,
            cameraIp,
            deviceId,
            type,
            time,
            image,
            SrcDevice{
                id,
                name,
                type,
                ip,
                lastUpdateTime,
                statusUpdate,
                activeStatus,
                dateCreated,
                isTimekeepingDevice,
                dateModified
            }
        }
    }
}
`;

export const DELETE_SHIFT_USER = gql`
mutation deleteShiftUser(
    $userId: String!, 
    $shiftId: String!, 
    $startDate: Date!, 
    $endDate: Date!, 
){
    deleteShiftUser(
        userId: $userId,
        shiftId: $shiftId,
        startDate: $startDate,
        endDate: $endDate,
    ) 
}
`;

export const UPDATE_SHIFT_USER = gql`
mutation updateShiftUser(
    $userId: String!, 
    $oldShiftId: String!, 
    $newShiftId: String!, 
    $startDate: Date!, 
    $endDate: Date!, 
){
    updateShiftUser(
        userId: $userId,
        oldShiftId: $oldShiftId,
        newShiftId: $newShiftId,
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;

export const UPDATE_MULTI_SHIFT_USER = gql`
mutation updateMultiShiftUser(
    $userIds: [String!]!, 
    $oldShiftIds: [String!]!,
    $newShiftIds: [String!]!, 
    $startDate: Date!, 
    $endDate: Date!, 
){
    updateMultiShiftUser(
        userIds: $userIds,
        oldShiftIds: $oldShiftIds,
        newShiftIds: $newShiftIds,
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;

export const GET_WORK_TIME = gql`
query getCalculatedWorkMonth(
    $userId: String,
    $month: String!,
){
    getCalculatedWorkMonth(
        userId: $userId,
        month: $month,
    ){
        cycleDate,
        year,
        month,
        days,
        tongCong,
        congThucTe,
        congPhep,
        congCheDo,
        phatTien
    }
}
`;

export const SET_AUTO_DAYS = gql`
mutation updateAutoDays(
    $code: String!,
    $autoDays: String!,
){
    updateAutoDays(
        code: $code,
        autoDays: $autoDays,
    ){
        code,description,autoDays,totalWorkHour,startHour,
        endHour,breakCode,startBreak,endBreak,
        breakwithPaid,breakwithoutPaid,breakBonus,work,actualWorkHour,overnight
    }
}
`;

export const RECALCULATE_SHIFT_USER = gql`
mutation reCalculateShiftUser(
    $userId: String!,
    $startDate: Date!,
    $endDate: Date!,
    $password:String!,
    $tokenType: Float
){
    reCalculateShiftUser(
        userId:$userId,
        startDate: $startDate,
        endDate: $endDate,
        password: $password,
        tokenType: $tokenType
    )
}
`;

export const RECALCULATE_ALL_SHIFT_USER = gql`
mutation autoRecalculateAllShiftUser(
    $startDate: Date!,
    $endDate: Date!,
    $password: String!,
    $tokenType: Float
){
    autoRecalculateAllShiftUser(
        startDate: $startDate,
        endDate: $endDate,
        password: $password,
        tokenType: $tokenType
    )
}
`;

export const FIND_ALL_SHIFT_NOT_ASSIGNED = gql`
query findAllShiftDefaultNotAssigned(
    $startDate: String!,
    $endDate: String!,
){
    findAllShiftDefaultNotAssigned(
        startDate: $startDate,
        endDate: $endDate,
    ){
        userId,
        shiftId,
        startDate,
        endDate
    }
}
`;

export const AUTO_CREATE_SHIFT_DEFAULT_NOT_ASSIGNED = gql`
mutation autoCreateAllShiftDefaultNotAssigned(
    $startDate: String!,
    $endDate: String!,
){
    autoCreateAllShiftDefaultNotAssigned(
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;

export const SEARCH_NO_SHIFT_USER = gql`
query searchNoShiftUser(
    $startDate: Date!,
    $endDate: Date!,
    $search: String,
    $comIds: [String!],
    $depIds: [String!],
    $userIds: [String!],
){
    searchNoShiftUser(
        startDate: $startDate,
        endDate: $endDate,
        search: $search,
        comIds: $comIds,
        depIds: $depIds,
        userIds: $userIds,
    ){
        id, name, email, avatar, Department { id, name }, Company { id, name }
    }
}
`;