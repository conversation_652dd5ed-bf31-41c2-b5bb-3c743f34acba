import { gql } from "@apollo/client";

export const GET_SERVER_PROFILE = gql`
query GetServerProfile{
    serverProfile{
        cpuUsage,
        totalMem,
        freeMem,
        totalDisk,
        freeDisk,
        uptime,
        time
    }
}
`;

export const RESTART_SERVER = gql`
mutation restartAllServer(
    $password: String!, 
    $tokenType: Float
){
    restartAllServer(
        password: $password, 
        tokenType: $tokenType
    )
}
`;

export const GET_MQTT_ADD_IN = gql`
query getMqttAddIn(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $type: Float,
) {
    getMqttAddIn(
        search: $search,
        page: $page,
        perPage: $perPage,
        type: $type
    ){
        id, address, isMqtts, type, topic, isDeviceLimit, dataFormat, count
    }
}
`;

export const UPSERT_MQTT_ADD_IN = gql`
mutation upsertMqttAddIn(
    $configId: String!,
    $address: String!,
    $isMqtts: Boolean!,
    $type: Float!,
    $isDeviceLimit: Boolean!,
    $topic: String!,
    $dataFormat: String!,
) {
    upsertMqttAddIn(
        configId : $configId
        address : $address
        isMqtts : $isMqtts
        type : $type
        isDeviceLimit: $isDeviceLimit
        topic: $topic
        dataFormat: $dataFormat
    ) {
        id, address, isMqtts, type, topic, isDeviceLimit, dataFormat
    }
}
`;

export const DELETE_MQTT_ADD_IN = gql`
mutation deleteMqttAddIn(
    $configId: String!,
) {
    deleteMqttAddIn(
        configId : $configId
    )
}
`;

export const GET_MQTT_CONFIG_DEVICES = gql`
query getMqttConfigDevices(
    $search: String!,
    $configId: String!,
) {
    getMqttConfigDevices(
        search: $search,
        configId: $configId
    ){
        configId, deviceId, Device { id, deviceName }, isDeleted, count
    }
}
`;

export const TOGGLE_MQTT_CONFIG_DEVICES = gql`
mutation toggleMqttConfigDevices(
    $configId: String!,
    $deviceIds: [String!]!,
    $isDeleted: Boolean!,
) {
    toggleMqttConfigDevices(
        configId : $configId,
        deviceIds: $deviceIds,
        isDeleted: $isDeleted
    )
}
`;