import { gql } from "@apollo/client";

export const DELETE_DEVICE = gql`
mutation DeleteDevice(
    $deviceId: String!
){
    deleteDevice(
        deviceId:$deviceId
    )
}
`;

export const SEARCH_ALL_DEVICE = gql`
query SearchAllDevice(
    $deviceTypes: [Float!]!,
    $deviceStatus: Float!
    $deviceUpdateStatus: Float!,
    $search: String!
){
    searchAllDevice(
        deviceTypes:$deviceTypes,
        deviceStatus:$deviceStatus,
        deviceUpdateStatus:$deviceUpdateStatus,
        search: $search
    ){
        id,
        deviceName,
        type,
        ip,
        lastUpdatedTime,
        statusUpdate,
        status,
        isTimekeepingDevice,
        count
    }
}
`;

export const GET_DEVICE_PAGE = gql`
query GetDevicePage(
    $deviceTypes: [Float!]!,
    $deviceStatus: Float!
    $deviceUpdateStatus: Float!,
    $page: Float!,
    $perPage: Float!,
    $search: String!,
    $sortBy: String!,
    $order: String!,
    $isAlive: Boolean,
){
    devicePage(
        deviceTypes:$deviceTypes,
        deviceStatus:$deviceStatus,
        deviceUpdateStatus:$deviceUpdateStatus,
        page:$page,
        perPage:$perPage,
        search: $search,
        sortBy: $sortBy,
        order: $order,
        isAlive: $isAlive,
    ){
        id,
        deviceName,
        type,
        ip,
        coreAiUuid,
        lastTimeSignal,
        lastUpdatedTime,
        statusUpdate,
        status,
        isTimekeepingDevice,
        isFirmwareAutoUpdate
        isDefaultDevice,
        isAuthenticated,
        isFaceResultEditable
        count,
        runStatus{
            deviceId,
            runningStatus,
            time,
            isClusterSync,
            isAuthen,
            versionCode,
            versionName,
            lived,
            maxAge,
            vol,
            current_vol,
            request_queue,
            mask_queue,
            quality_queue,
            headpose_queue,
            detect_queue,
            feature_queue,
            search_queue,
            resetAuthenCount,
            serialNumber
        },
        isAleart,
        userSyncProgress,totalUser,totalDeviceUser,userSyned,deviceUserSyned
        Cameras{cameraId,cameraName,cameraIp},
        uptime,cpuUsage,memTotal,memFree,freeDisk,
        macAddress,
        faceCheckType,
        licenseDeviceId, licenseExpireDate, licenseNumberUser,
    }
}
`;

export const GET_ALL_DEVICE = gql`
query GetAllDevice {
    allDevices{
        id,
        deviceName,
        type
    }
}
`;

export const COUNT_ALL_DEVICE = gql`
query CountDevice {countDevice}
`;

export const COUNT_PROCESSOR = gql`
query CountProcessor {countProcessor}
`;

export const COUNT_DISPLAY = gql`
query CountDisplay {countDisplay}
`;

export const DEVICE_CATERGORY = gql`
query deviceCatergory{
    deviceCatergory{
        id,
        type,
        deviceName
    }
}
`;

export const UPDATE_DEVICE = gql`
mutation UpdateDevice(
    $deviceId: String!,
    $deviceName: String!,
    $deviceStatus: NumberStatus!
    $isTimekeepingDevice: Boolean!,
    $isDefaultDevice: Boolean!
    $isFirmwareAutoUpdate: Boolean!,
    $isFaceResultEditable: Boolean!,
){
    updateDevice(
        deviceId:$deviceId,
        deviceName:$deviceName,
        deviceStatus:$deviceStatus,
        isTimekeepingDevice:$isTimekeepingDevice,
        isDefaultDevice:$isDefaultDevice
        isFirmwareAutoUpdate:$isFirmwareAutoUpdate,
        isFaceResultEditable:$isFaceResultEditable
    ){
        id
    }
}
`;

export const GET_ALL_RUNNING_DEVICE = gql`
query GetAllRunningDevice {
    allRunningDevices{
        id,
        deviceName
    }
}
`;

export const ADD_AI_SERVICE_TO_DEVICE = gql`
mutation AddAiServiceToDevice(
    $serviceId: Float!,
    $deviceId: String!
){
    addAiServiceToDevice (
        serviceId: $serviceId,
        deviceId: $deviceId
    ){
        serviceId,
        deviceId,
        piority,
        status
    }
}
`;

export const GET_CAMERAS_IN_DEVICE = gql`
query GetCamerasInDevice(
    $deviceId: String!,
){
    camerasInDevice(
        deviceId: $deviceId,
    ){
        cameraId,
        cameraName,
        cameraIp,
        cameraRtsp,
        faceCheckType
    }
}
`;

export const GET_CAMERAS_NOT_IN_DEVICE = gql`
query GetCamerasNotInDevice(
    $deviceId: String!,
){
    camerasNotInDevice(
        deviceId: $deviceId,
    ){
        cameraId,
        cameraName
    }
}
`;

export const ADD_CAMERAS_TO_DEVICE = gql`
mutation AddCamerasToDevice(
    $deviceId: String!,
    $cameraIds: [Float!]!
){
    addCamerasToDevice(
        deviceId: $deviceId,
        cameraIds: $cameraIds
    ){
        cameraId
    }
}
`;

export const REMOVE_CAMERAS_IN_DEVICE = gql`
mutation RemoveCamerasInDevice(
    $deviceId: String!,
    $cameraIds: [Float!]!
){
    removeCamerasInDevice(
        deviceId: $deviceId,
        cameraIds: $cameraIds
    ){
        cameraId
    }
}
`;

export const UPDATE_CAMERAS_TO_DEVICE = gql`
mutation SendUpdateToDevice(
    $deviceId: String!
){updateCameraInDevice
    (
        deviceId: $deviceId
    ){
        cameraId
    }
}
`;

export const GET_ALL_DEVICE_RUNNING_CAMERA = gql`
query GetAllDeviceRunningCamera(
    $cameraId: String!
){
    getAllDeviceRunningCamera(
        cameraId:$cameraId
    ){
        id,
        deviceName,
        type,
        ip,
    }
}
`;

export const SEND_DEVICE_CAMERA_SNAP_SHOT_REQUEST = gql`
mutation SendDeviceCameraSnapshotRequest(
    $deviceId: String!,
    $cameraId: Float!
){
    deviceCameraSnapshotRequest(
        deviceId:$deviceId,
        cameraId:$cameraId
    )
}
`;

export const GET_CAMERA_SNAP_SHOT = gql`
query GetDeviceCameraSnapshot(
    $deviceId: String!,
    $cameraId: Float!
){
    deviceCameraSnapshot(
        deviceId:$deviceId,
        cameraId:$cameraId
    ){
        id,
        cameraId,
        deviceId,
        frame,
        faceRoiEnable,
        faceRoiX,
        faceRoiY,
        faceRoiWidth,
        faceRoiHeight,
        faceZoomEnable,
        faceZoomMaxWidth,
        faceZoomMaxHeight,
        faceMinWidth,
        faceMinHeight,
        dateCreated,
        dateModified
    }
}
`;

export const UPDATE_DEVICE_STREAM_CONFIG = gql`
mutation UpdateStreamConfig(
    $faceCheckType: Float!,
    $cameraId: Float!,
    $deviceId: String!,
    $faceRoiEnable: Boolean!,
    $faceRoiX: Float!,
    $faceRoiY: Float!,
    $faceRoiWidth: Float!,
    $faceRoiHeight: Float!,
    $faceZoomEnable: Boolean!,
    $faceZoomMaxWidth: Float!,
    $faceZoomMaxHeight: Float!,
    $faceMinWidth: Float!,
    $faceMinHeight: Float!
){
    deviceStreamConfig(
        faceCheckType: $faceCheckType,
        cameraId: $cameraId,
        deviceId: $deviceId,
        faceRoiEnable: $faceRoiEnable,
        faceRoiX: $faceRoiX,
        faceRoiY: $faceRoiY,
        faceRoiWidth: $faceRoiWidth,
        faceRoiHeight: $faceRoiHeight,
        faceZoomEnable: $faceZoomEnable,
        faceZoomMaxWidth: $faceZoomMaxWidth,
        faceZoomMaxHeight: $faceZoomMaxHeight,
        faceMinWidth: $faceMinWidth,
        faceMinHeight: $faceMinHeight
    )
}
`;

export const GET_STREAM_STATUS_BY_DEVICE = gql`
query GetDeviceStreamStatusByDevice{
    streamStatusByDevices{
        cameraIp,
        deviceId,
        data{
            status,
            dateCreated
        }
    }
}
`;

export const GET_USER_PAGE_IN_DEVICE = gql`
query SearchUserInDevice(
    $page: Float!,
    $perPage: Float!,
    $deviceId: String!,
    $searchText: String!
){
    usersInDevice(
        page: $page,
        perPage: $perPage,
        deviceId: $deviceId,
        searchText: $searchText
    ){
        userId,
        userName,
        avatar,
        count
    }
}
`;

export const GET_ALL_ACCESS_CONTROL_DEVICE = gql`
query GetAllACDevice {
    acDevices{
        deviceId,
        deviceName,
        deviceType
    }
}
`;

export const RESET_DEVICE_AUTHEN_TOKEN = gql`
mutation resetDeviceToken(
    $deviceId: String!
) {
    resetDeviceToken(
        deviceId: $deviceId,
    )
}
`;

export const REBOOT_DEVICE = gql`
mutation sendDeviceReboot(
    $deviceId: String!,
){
    sendDeviceReboot(
        deviceId: $deviceId,
    )
}
`;

export const SEND_CONFIG = gql`
mutation SendAiCoreConfigData(
    $deviceId: String!
) {
    sendAiCoreConfigData(
        deviceId: $deviceId
    )
}
`;

export const GET_GST_CONFIG = gql`
query getGstConfig(
    $deviceId: String!
) {
    getGstConfig(
        deviceId: $deviceId
    ) {
        deviceId,
        faceConfident,
        alignMode,
        bBoxMargin,
        maxSize,
        faceBestShotEnable,
        faceBestShotDuration,
        namedSentDuration,
        personDetectConfident,
        kptConfident,
        personBestShotDuration,
        fakeFaceEnable,
        fasValidationCount,
        fasConfident,
        serverUrl,
        timeout
    }
}
`;

export const GST_CONFIG = gql`
mutation setGstConfig(
    $deviceId: String!,
    $faceConfident: String!,
    $alignMode: String!,
    $bBoxMargin: String!,
    $maxSize: String!,
    $faceBestShotEnable: String!,
    $faceBestShotDuration: String!,
    $namedSentDuration: String!,
    $personDetectConfident: String!,
    $kptConfident: String!,
    $personBestShotDuration: String!,
    $fakeFaceEnable: String!,
    $fasValidationCount: String!,
    $fasConfident: String!,
    $serverUrl: String!,
    $timeout: String!
){
    setGstConfig(
        deviceId: $deviceId,
        faceConfident: $faceConfident,
        alignMode: $alignMode,
        bBoxMargin: $bBoxMargin,
        maxSize: $maxSize,
        faceBestShotEnable: $faceBestShotEnable,
        faceBestShotDuration: $faceBestShotDuration,
        namedSentDuration: $namedSentDuration,
        personDetectConfident: $personDetectConfident,
        kptConfident: $kptConfident,
        personBestShotDuration: $personBestShotDuration,
        fakeFaceEnable: $fakeFaceEnable,
        fasValidationCount: $fasValidationCount,
        fasConfident: $fasConfident,
        serverUrl: $serverUrl,
        timeout: $timeout
    )
}
`;

export const GET_AI_BOX_CONFIG = gql`
query getAiBoxConfigDisplay(
    $deviceId: String!,
) {
    getAiBoxConfigDisplay(
        deviceId: $deviceId,
    ){
        deviceId,
        cameraIp,
        cameraUser,
        cameraPass,
        cameraRtsp,
        cameraFps,
        doorHoldTime,
        lastSyncTime,
        deviceJsonConfig
    }
}
`;

export const UPSERT_AI_BOX_CONFIG = gql`
mutation upsertAiBoxConfig(
    $deviceId: String!,
    $cameraIp: String,
    $cameraUser: String,
    $cameraPass: String,
    $cameraRtsp: String,
    $cameraFps: Float,
    $doorHoldTime: Float,
    $customeConfig: DeviceCustomeConfigGql,
) {
    upsertAiBoxConfig(
        deviceId: $deviceId,
        cameraIp: $cameraIp,
        cameraUser: $cameraUser,
        cameraPass: $cameraPass,
        cameraRtsp: $cameraRtsp,
        cameraFps: $cameraFps,
        doorHoldTime: $doorHoldTime,
        customeConfig: $customeConfig,
    ){
        deviceId
    }
}
`;

export const GET_DEVICE_SIGNAL_LOG = gql`
query getDeviceSignalLog(
    $deviceId: String!,
    $startTime: String!,
    $endTime: String!,
){getDeviceSignalLog
    (
        deviceId: $deviceId,
        startTime: $startTime,
        endTime: $endTime,
    ){
        time,isOnline,count
    }
}
`;

export const GET_DISPLAY_ONLY_CONFIG = gql`
query getDeviceType4Config(
    $deviceId: String!,
){
    getDeviceType4Config(
        deviceId: $deviceId,
    ){
        deviceId,
        camIp,
        camRtsp,
        isDoorAccess,
        language,
        serverIp,
        serverPort,
        httpBrokerIp,
        httpBrokerPort,
        httpApi,
        isPlaySound,
        lastSyncTime,
        dateCreated,
        dateModified
    }
}
`;

export const UPDATE_DISPLAY_ONLY_CONFIG = gql`
mutation updateDeviceType4Config(
    $deviceId: String!,
    $camIp: String,
    $camRtsp: String,
    $isDoorAccess: Boolean,
    $language: String,
    $serverIp: String,
    $serverPort: Float,
    $httpBrokerIp: String,
    $httpBrokerPort: Float,
    $httpApi: String,
    $isPlaySound: Boolean,
    $lastSyncTime: Date,
){
    updateDeviceType4Config(
        deviceId: $deviceId,
        camIp: $camIp,
        camRtsp: $camRtsp,
        isDoorAccess: $isDoorAccess,
        language: $language,
        serverIp: $serverIp,
        serverPort: $serverPort,
        httpBrokerIp: $httpBrokerIp,
        httpBrokerPort: $httpBrokerPort,
        httpApi: $httpApi,
        isPlaySound: $isPlaySound,
        lastSyncTime: $lastSyncTime,
    ){
        deviceId,
        camIp,
        camRtsp,
        isDoorAccess,
        language,
        serverIp,
        serverPort,
        httpBrokerIp,
        httpBrokerPort,
        httpApi,
        isPlaySound,
        lastSyncTime
    }
}
`;

export const GET_FACE_TERMINAL_CONFIG = gql`
query getFaceTerminalConfig(
    $deviceId: String!,
){
    getFaceTerminalConfig(
        deviceId: $deviceId,
    ){
        deviceId,
        showCompanyName,
        showCompanyLabel,
        companyLabel,
        showDepartmentLabel,
        departmentLabel,
        showTimekeeping,
        showDoorAccess,
        showIntegrationKey,
        integrationKeyLabel,
        showResultErrCall,
        showQrCode,
        showAddCard,
        showAddFinger,
        recogMode,
        language,
        fasRgb,
        fasIr,
        fasStrictModOn,
        showIr,
        imageImprove,
        ledOn,
        ledLevel,
        speakerOn,
        soundLevel,
        autoStart,
        autoRestart,
        checkMask,
        checkTemperature,
        temOnFaceOnly,
        debugModeOn,
        unknownDefineTime,
        doorHoldTime,
        screenOnTime,
        recogDistance,
        temperatureThreshold, 
        logSaveDays,
        samePersonDelay,
        mqttBrokerIp,
        mqttBrokerPort,
        defaultCompanyId,
        apiUrl,
        socketUrl,
        gRpcUrl,
        gRpcPort,
        updateUrl,
        numberUserRequired,
        maxUserWaitTime,
    }
}
`;

export const UPDATE_FACE_TERMINAL_CONFIG = gql`
mutation updateFaceTerminalConfig(
    $deviceId: String!,
    $showCompanyName: Boolean,
    $showCompanyLabel: Boolean,
    $companyLabel: String,
    $showDepartmentLabel: Boolean,
    $departmentLabel: String,
    $showTimekeeping: Boolean,
    $showDoorAccess: Boolean,
    $showIntegrationKey: Boolean,
    $integrationKeyLabel: String,
    $showResultErrCall: Boolean,
    $showQrCode: Boolean,
    $showAddCard: Boolean,
    $showAddFinger: Boolean,
    $recogMode: Float,
    $language: Float,
    $fasRgb: Boolean,
    $fasIr: Boolean,
    $fasStrictModOn: Boolean,
    $showIr: Boolean,
    $imageImprove: Boolean,
    $ledOn: Boolean,
    $ledLevel: Float,
    $speakerOn: Boolean,
    $soundLevel: Float,
    $autoStart: Boolean,
    $autoRestart: Boolean,
    $checkMask: Boolean,
    $checkTemperature: Boolean,
    $temOnFaceOnly: Boolean,
    $debugModeOn: Boolean,
    $unknownDefineTime: Float,
    $doorHoldTime: Float,
    $screenOnTime: Float,
    $recogDistance: Float,
    $temperatureThreshold: Float,
    $logSaveDays: Float,
    $samePersonDelay: Float,
    $mqttBrokerIp: String,
    $mqttBrokerPort: Float,
    $defaultCompanyId: String,
    $apiUrl: String,
    $socketUrl: String,
    $gRpcUrl: String,
    $gRpcPort: Float,
    $updateUrl: String,
    $numberUserRequired: Int,
    $maxUserWaitTime: Int,
){
    updateFaceTerminalConfig(
        deviceId: $deviceId,
        showCompanyName: $showCompanyName,
        showCompanyLabel: $showCompanyLabel,
        companyLabel: $companyLabel,
        showDepartmentLabel: $showDepartmentLabel,
        departmentLabel: $departmentLabel,
        showTimekeeping: $showTimekeeping,
        showDoorAccess: $showDoorAccess,
        showIntegrationKey: $showIntegrationKey,
        integrationKeyLabel: $integrationKeyLabel,
        showResultErrCall: $showResultErrCall,
        showQrCode: $showQrCode,
        showAddCard: $showAddCard,
        showAddFinger: $showAddFinger,
        recogMode: $recogMode,
        language: $language,
        fasRgb: $fasRgb,
        fasIr: $fasIr,
        fasStrictModOn: $fasStrictModOn,
        showIr: $showIr,
        imageImprove: $imageImprove,
        ledOn: $ledOn,
        ledLevel: $ledLevel,
        speakerOn: $speakerOn,
        soundLevel: $soundLevel,
        autoStart: $autoStart,
        autoRestart: $autoRestart,
        checkMask: $checkMask,
        checkTemperature: $checkTemperature,
        temOnFaceOnly: $temOnFaceOnly,
        debugModeOn: $debugModeOn,
        unknownDefineTime: $unknownDefineTime,
        doorHoldTime: $doorHoldTime,
        screenOnTime: $screenOnTime,
        recogDistance: $recogDistance,
        temperatureThreshold: $temperatureThreshold,
        logSaveDays: $logSaveDays,
        samePersonDelay: $samePersonDelay,
        mqttBrokerIp: $mqttBrokerIp,
        mqttBrokerPort: $mqttBrokerPort,
        defaultCompanyId: $defaultCompanyId,
        apiUrl: $apiUrl,
        socketUrl: $socketUrl,
        gRpcUrl: $gRpcUrl,
        gRpcPort: $gRpcPort,
        updateUrl: $updateUrl,
        numberUserRequired: $numberUserRequired,
        maxUserWaitTime: $maxUserWaitTime,
    ){
        deviceId,
        showCompanyName,
        showCompanyLabel,
        companyLabel,
        showDepartmentLabel,
        departmentLabel,
        showTimekeeping,
        showDoorAccess,
        recogMode,
        language,
        fasRgb,
        fasIr,
        fasStrictModOn,
        showIr,
        imageImprove,
        ledOn,
        ledLevel,
        speakerOn,
        soundLevel,
        autoStart,
        autoRestart,
        checkMask,
        checkTemperature,
        temOnFaceOnly,
        debugModeOn,
        unknownDefineTime,
        doorHoldTime,
        screenOnTime,
        recogDistance,
        temperatureThreshold, 
        logSaveDays,
        samePersonDelay,
        mqttBrokerIp,
        mqttBrokerPort,
        defaultCompanyId,
        apiUrl,
        socketUrl,
        gRpcUrl,
        gRpcPort,
        updateUrl,
        numberUserRequired
        maxUserWaitTime
    }
}
`;

export const GET_DEVICE_ADMIN = gql`
query getDeviceAdmins(
    $deviceId: String!,
    $search: String!,
){
    getDeviceAdmins(
        deviceId: $deviceId,
        search: $search,
    ){
        id,
        email,
        name,
        avatar,
        count
    }
}
`;

export const UPSERT_DEVICE_ADMIN = gql`
mutation upsertDeviceAdmin(
    $deviceId: String!,
    $adminId: String!,
    $isActive: Boolean,
){
    upsertDeviceAdmin(
        deviceId: $deviceId,
        isActive: $isActive,
        adminId: $adminId,
    ){
        deviceId,
        userId,
    }
}
`;

export const UPDATE_DEVICE_FACE_CHECK_TYPE = gql`
mutation updateDeviceFaceCheckType(
    $deviceId: String!,
    $faceCheckType: Float!,
){
    updateDeviceFaceCheckType(
        deviceId: $deviceId,
        faceCheckType: $faceCheckType,
    )
}
`;

export const SEND_DEVICE_CMD = gql`
mutation sendDeviceCmd(
    $deviceId: String!,
    $cmd: String!,
) {
    sendDeviceCmd(
        deviceId: $deviceId,
        cmd: $cmd,
    )
}
`;

export const GET_SERVER_USAGE = gql`
query getServerUsage{
    getServerUsage{
        cpus,cpuCores,cpuUsage,
        totalMem,freeMem,isRamHealth,
        totalDisk,freeDisk,isDiskHealth,
        uptime,time
    }
}
`;

export const SEARCH_USER_SYNC_ERROR = gql`
query searchUserSyncError (
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $deviceId: String,
    $userIds: [String!],
) {
    searchUserSyncError(
        search:$search,
        page:$page, 
        perPage:$perPage,
        deviceId:$deviceId,
        userIds:$userIds,
    ){
        deviceId,
        userName,
        status,
        syncLogDetail,
        dateModified,
        User{
            id,name,Company{id,name},Department{id,name}
        },
        count
    }
}
`;

export const SEARCH_DEVICE_SYNC_LOG = gql`
query searchDeviceSyncLog (
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchDeviceSyncLog(
        search:$search,
        page:$page,
        perPage:$perPage,
    ){
        id, deviceId, logType, isSuccess, status, syncLogDetail, 
        userId, User { 
            id, email, fullName, avatar, 
            Department { id, name },
            Company { id, name }
        }, 
        userName, userType, dateModified, count
    }
}
`;