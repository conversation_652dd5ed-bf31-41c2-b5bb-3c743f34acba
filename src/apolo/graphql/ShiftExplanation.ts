import { gql } from "@apollo/client";

export const SEARCH_SHIFT_USER_EXPLANATIONS = gql`
query searchShiftUserExplain(
    $startSearch: Date!,
    $endSearch: Date!,
    $explainType: Float!,
    $explainStatus: Float!,
    $explainSearch: String!,
    $page: Float!,
    $perPage: Float!,
    $departmentIds:[String!]!,
    $userIds:[String!]!
){searchShiftUserExplain
    (
        startSearch: $startSearch,
        endSearch: $endSearch,
        explainType: $explainType,
        explainStatus: $explainStatus,
        explainSearch: $explainSearch,
        page:$page,
        perPage:$perPage,
        userIds:$userIds,
        departmentIds:$departmentIds
    ){id,userId,userName,email,startExplainTime,endExplainTime,explainType,explainText,status,count,explainImageId,explainImageGql,firstInString,lastOutString}
}
`;

export const APPROVE_SHIFT_USER_EXPLANATIONS = gql`
mutation approveShiftUserExplain(
    $explainIds: [String!]!
){approveShiftUserExplain
    (
        explainIds: $explainIds
    )
}
`;

export const REJECT_SHIFT_USER_EXPLANATIONS = gql`
mutation rejectShiftUserExplain(
    $explainIds: [String!]!
){rejectShiftUserExplain
    (
        explainIds: $explainIds
    )
}
`;

export const REMOVE_SHIFT_USER_EXPLANATIONS = gql`
mutation memoveShiftUserExplain(
    $explainIds: [String!]!
){removeShiftUserExplain
    (
        explainIds: $explainIds
    )
}
`;

export const CREATE_SHIFT_EXPLAINATION = gql`
mutation createShiftUserExplain(
    $startExplainTime: Date!,
    $endExplainTime: Date!,
    $explainType: Float!,
    $explainText: String!
){createShiftUserExplain
    (
        startExplainTime: $startExplainTime,
        endExplainTime: $endExplainTime,
        explainType: $explainType,
        explainText: $explainText,
    ){userId,startExplainTime,endExplainTime,explainType,explainText,status}
}
`;

export const SEARCH_SHIFT_USER_EXPLAIN_ADMIN = gql`
query searchShiftUserExplainAdmin(
    $search: String!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $page: Float!,
    $perPage: Float!,
){
    searchShiftUserExplainAdmin(
        search: $search,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        page: $page,
        perPage: $perPage,
    ){
        Admin{
            name,
            email,
            avatar
        },
        ShiftUserExplainAdmins{
            id,
            userId,
            Company{
                id,
                name
            },
            Department{
                id,
                name
            },
            count
        },
        count
    }
}
`;

export const UPSERT_SHIFT_USER_EXPLAIN_ADMIN = gql`
mutation upsertShiftUserExplainAdmin(
    $configId: String!,
    $adminId: String!,
    $companyId: String!,
    $departmentId: String!
){upsertShiftUserExplainAdmin
    (
        configId: $configId,
        adminId: $adminId,
        companyId: $companyId,
        departmentId: $departmentId,
    )
}
`;

export const REMOVE_SHIFT_USER_EXPLAIN_ADMIN = gql`
mutation removeShiftUserExplainAdmin(
    $configId: String!,
){
    removeShiftUserExplainAdmin(
        configId: $configId,
    )
}
`;

export const SET_REMOTE_RECOGNIZE_ADMIN = gql`
mutation upsertRemoteAdmin(
    $adminIds: [String!]!,
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $orgIds: [String!]!,
    $isEnable: Boolean!,
){upsertRemoteAdmin
    (
        adminIds: $adminIds,
        userIds: $userIds,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        orgIds: $orgIds,
        isEnable: $isEnable,
    )
}
`;

export const SET_REMOTE_RECOGNIZE_USER = gql`
mutation setRemoteRecognizeUser(
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $organizationIds: [String!]!,
    $isEnable: Boolean!,
){setRemoteRecognizeUser
    (
        userIds: $userIds,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        organizationIds: $organizationIds,
        isEnable: $isEnable,
    )
}
`;

export const REMOVE_REMOTE_LIMIT = gql`
mutation removeRemoteLimit(
    $userIds: [String!]!,
    $depIds: [String!]!,
    $comIds: [String!]!,
    $orgIds: [String!]!,
){removeRemoteLimit
    (
        userIds: $userIds,
        depIds: $depIds,
        comIds: $comIds,
        orgIds: $orgIds,
    )
}
`;

export const FULL_DELETE_REMOTE_ADMIN = gql`
mutation fullDeleteRemoteAdmin(
    $adminId: String!,
){fullDeleteRemoteAdmin
    (
        adminId: $adminId,
    )
}
`;

export const GET_REMOTE_RECORGANIZE_ADMIN = gql`
query searchRemoteAdmin(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
    $userIds: [String!],
    $depIds: [String!],
    $comIds: [String!],
    $orgIds: [String!],
){searchRemoteAdmin
    (
        page: $page,
        perPage: $perPage,
        search: $search,
        userIds: $userIds,
        depIds: $depIds,
        comIds: $comIds,
        orgIds: $orgIds,
    ){
        id,
        email,
        name,
        avatar,
        Company{id,name},
        Department{id,name},
        adminCount,
        userCount,
        depCount,
        comCount,
        orgCount,
        count
    }
}
`;

export const GET_REMOTE_RECORGANIZE_USER = gql`
query getRemoteRecognizeEnableUser(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
    $departmentIds: [String!],
    $companyIds: [String!],
    $organizationIds: [String!],
){getRemoteRecognizeEnableUser
    (
        page: $page,
        perPage: $perPage,
        search: $search,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        organizationIds: $organizationIds,
    ){
        id,email,name,avatar,Company{id,name},Department{id,name},RemoteLimit{limitTimes,currentTimes,dateLimit},count
    }
}
`;

export const GET_REMOTE_CHECK_RESULT = gql`
query getRemoteCheckResults(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $startDate: Date!, 
    $endDate: Date!,
    $status: Float,
    $isSuperAdminView: Boolean,
){getRemoteCheckResults
    (
        search:$search,
        page:$page,
        perPage:$perPage,
        startDate:$startDate,
        endDate:$endDate,
        status:$status,
        isSuperAdminView:$isSuperAdminView,
    ){
        id,
        time,
        image,
        remoteStatus,
        remoteDetail,
        User{id,name,email,integrationKey},
        count
    }
}
`;

export const APPROVE_REMOTE_RESULT = gql`
mutation approveRemoteResult(
    $remoteId: Float!,
    $status: Float!,
){approveRemoteResult
    (
        remoteId:$remoteId,
        status:$status,
    )
}
`;

export const DELETE_REMOTE_RESULT = gql`
mutation deleteRemoteResult(
    $remoteId: Float!,
){deleteRemoteResult
    (
        remoteId:$remoteId,
    )
}
`;

export const GET_REMOTE_USER_ADMIN = gql`
query getRemoteUserAdmin (
    $adminId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteUserAdmin(
        adminId: $adminId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        email,
        avatar,
        Company{id,name},
        Department{id,name},
        count
    }
}
`;

export const GET_REMOTE_COM_ADMIN = gql`
query getRemoteComAdmin (
    $adminId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteComAdmin(
        adminId: $adminId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const GET_REMOTE_DEP_ADMIN = gql`
query getRemoteDepAdmin (
    $adminId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteDepAdmin(
        adminId: $adminId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const GET_REMOTE_ORG_ADMIN = gql`
query getRemoteOrgAdmin (
    $adminId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteOrgAdmin(
        adminId: $adminId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const REGIS_FULL_SHIFT_FOR_USER = gql`
mutation regisFullShiftForUsers(
    $userIds: [String!]!,
    $depIds: [String!]!,
    $comIds: [String!]!,
    $orgIds: [String!]!,
    $startDate: String!,
    $endDate: String!,
){regisFullShiftForUsers
    (
        userIds: $userIds,
        depIds: $depIds,
        comIds: $comIds,
        orgIds: $orgIds,
        startDate: $startDate,
        endDate: $endDate,
    )
}
`;
