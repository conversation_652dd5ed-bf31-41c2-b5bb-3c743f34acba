import { gql } from "@apollo/client";

export const GET_CAMERA_PAGE = gql`
query GetCameraPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!
){cameraPage
    (
        search:$search,
        page:$page,
        perPage:$perPage
    ){
        id,
        name,
        rtsp,
        ip,
        isActive,
        count
    }
}
`;

export const ADD_CAMERA = gql`
mutation upsertCamera(
    $name: String!,
    $ip: String!,
    $rtsp: String!
    $subRtsp: String,
){
    upsertCamera(
        name:$name,
        ip:$ip,
        rtsp:$rtsp
        subRtsp:$subRtsp
    ){
        id,name,rtsp,subRtsp
    }
}
`;

export const UPDATE_CAMERA = gql`
mutation upsertCamera(
    $cameraId: String!,
    $name: String!,
    $ip: String!,
    $rtsp: String!,
    $subRtsp: String,
){
    upsertCamera(
        cameraId:$cameraId,
        name:$name,
        ip:$ip,
        rtsp:$rtsp,
        subRtsp:$subRtsp
    ){
        id,name, rtsp, subRtsp
    }
}
`;

export const GET_CAMERA_PROFILE = gql`
query getCameraProfile(
    $cameraId: String!
)
{getCameraProfile
    (
        cameraId:$cameraId
    ){
        id,
        name,
        ip,
        rtsp,
        subRtsp,
        frameRate,
        streamMode,
        recordDuration,
        inputCodec,
        outputCodec,
        distanceMin,
        faceService,
        personService
    }
}
`;

export const COUNT_ALL_CAMERA = gql`
query CountCamera {countCamera}
`;

export const DELETE_CAMERA = gql`
mutation DeleteCamera(
    $cameraId: String!){
        deleteCamera(
            cameraId:$cameraId)
}
`;

// roiEnable,
//         roiX,
//         roiY,
//         roiWidth,
//         roiHeight,
//         faceMinWidth,
//         faceMinHeight,
//         faceZoomEnable,
//         faceZoomMaxWidth,
//         faceZoomMaxHeight

// $roiEnable: Int!,
// $roiX: Int!,
// $roiY: Int!,
// $roiWidth: Int!,
// $roiHeight: Int!,
// $faceMinWidth: Int!,
// $faceMinHeight: Int!,
// $faceZoomEnable: Int!,
// $faceZoomMaxWidth: Int!,
// $faceZoomMaxHeight: Int!

export const DELETE_AI_SERVICE = gql`
mutation deleteAiService(
    $id: String!,
){deleteAiService
    (
        id: $id,
    )
}
`;

export const ADD_AI_SERVICE_TO_CAMERA = gql`
mutation AddAiServiceToCamera(
    $cameraId: String!,
    $serviceId: String!
){addAiServiceToCamera
    (
        cameraId: $cameraId,
        serviceId: $serviceId
    ){
        cameraId,
        serviceId
    }
}
`;

export const GET_ALL_CAMERA_RUNNING_SERVICE = gql`
query GetAllCameraRunningService(
    $serviceId: String!,
){allCameraRunningService
    (
        serviceId: $serviceId
    ){
        serviceId,
        cameraId,
        cameraIp,
        cameraName,
        status
    }
}
`;

export const GET_ALL_DEVICE_RUNNING_CAMERA = gql`
query GetDeviceCameraSnapshot(
    $cameraId: String!){
        deviceCameraSnapshot(
            cameraId:$cameraId){
                id,
                cameraId,
                deviceId,
                frame,
                dateCreated,
                dateModified
            }
}

`;

export const GET_AI_SERVICES = gql`
query searchAiService(
    $search:String!,
    $type:[Float!]!,
    $page:Float!,
    $perPage:Float!
){
    searchAiService(
        search:$search,
        type:$type,
        page:$page,
        perPage:$perPage
    ){id,name,type,count}
}
`;

export const UPSERT_CAMERA_ROI = gql`
mutation upsertCameraRoi(
    $id:String,
    $name:String!,
    $xs:[String!]!,
    $ys:[String!]!,
    $cameraId: String!,
    $deviceId: String!,
    $aiServiceTypes: [Float!],
    $aiServiceName: String,
){upsertCameraRoi
    (
        id: $id,
        name: $name,
        xs: $xs,
        ys: $ys,
        cameraId: $cameraId,
        deviceId: $deviceId,
        aiServiceTypes: $aiServiceTypes
        aiServiceName: $aiServiceName
    ){
        id,
        name,
        xs,
        ys
    }
}
`;

export const GET_CAMERA_ROI_CONFIG = gql`
query getCameraRoiConfig(
    $cameraId: String!,
    $deviceId: String!,
    $cameraRoiId: String,
    $aiServiceType: Float,
){getCameraRoiConfig
    (
        cameraId: $cameraId,
        deviceId: $deviceId,
        cameraRoiId: $cameraRoiId,
        aiServiceType: $aiServiceType
    ){
        id,
        name,
        xs,
        ys,
        AiServices {
            id,
            name,
            dropFps
        },
        isActive
    }
}
`;

export const ADD_AI_SERVICE_TO_ROI = gql`
mutation AddAiServiceToRoi(
    $aiServiceId: String,
    $name: String,
    $roiId: String!,
    $aiServiceType: Float,
){addAiServiceToRoi
    (
        aiServiceId: $aiServiceId,
        name: $name,
        roiId: $roiId,
        aiServiceType: $aiServiceType
    )
}
`;

export const REMOVE_AI_SERVICE_IN_ROI = gql`
mutation deleteAiServiceInRoi(
    $aiServiceId: String!,
    $roiId: String!,
){deleteAiServiceInRoi
    (
        aiServiceId: $aiServiceId,
        roiId: $roiId,
    )
}
`;

export const REMOVE_CAMERA_ROI = gql`
mutation deleteCameraRoi(
    $id: String!,
){deleteCameraRoi
    (
        id: $id,
    )
}
`;

export const SEND_CAMERA_ROI_CONFIG = gql`
mutation SendCameraRoiConfigData(
    $cameraId: String!, 
    $deviceId: String!, 
    $cameraRoiId: String
){
    sendCameraRoiConfigData(
        cameraId: $cameraId, 
        deviceId: $deviceId, 
        cameraRoiId: $cameraRoiId
    ) 
}
`;

export const UPDATE_CAMERA_CONFIG = gql`
mutation UpdateCameraConfig(
    $cameraId: String,
    $frameRate: String!,
    $streamMode: String!,
    $recordDuration: String!,
    $inputCodec: String!,
    $outputCodec: String!,
    $distanceMin: String!,
    $faceService: String!,
    $personService: String!
){
    updateCameraConfig(
        cameraId: $cameraId,
        frameRate: $frameRate,
        streamMode: $streamMode,
        recordDuration: $recordDuration,
        inputCodec: $inputCodec,
        outputCodec: $outputCodec,
        distanceMin: $distanceMin,
        faceService: $faceService,
        personService: $personService
    )
}
`;

export const SEND_CAMERA_TOKEN = gql`
mutation sendCameraAuthenToken(
    $cameraId: String!,
){
    sendCameraAuthenToken(
        cameraId: $cameraId,
    )
}
`;

export const GET_CAMERA_SNAPSHOT = gql`
query getCameraSnapshot(
    $cameraId: String!,
){
    getCameraSnapshot(
        cameraId: $cameraId,
    )
}
`;

export const TOGGLE_CAMERA_STATUS = gql`
mutation toggleCameraStatus(
    $cameraId: String!,
    $status: Boolean!
){
    toggleCameraStatus(
        cameraId: $cameraId,
        status: $status
    )
}
`;

export const TOGGLE_CAMERA_ROI = gql`
mutation toggleCameraRoi(
    $id: String!,
    $isEnable: Boolean!,
){
    toggleCameraRoi(
        id:$id,
        isEnable:$isEnable,
    ){
        id,
        isActive,
    }
}
`;