import { gql } from "@apollo/client";

export const SEARCH_AC_MB = gql`
query searchMbAccessControl(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    searchMbAccessControl(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        userCount,
        userWaittingCount,
        deviceCount,
        acTimeCount,
        BindingCompany{id,name},
        totalIn,
        totalOut,
        isDoorOpen,
        BindingCompany{id,name,companyType},
        count
    }
}
`;

export const SEARCH_UNBINDING_COMPANY = gql`
query searchUnbindingCompany(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    searchUnbindingCompany(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,name,count
    }
}
`;

export const UPSERT_AC_MB = gql`
mutation upsertMbAc(
    $bindingCompanyId: String!,
){
    upsertMbAc(
        bindingCompanyId: $bindingCompanyId,
    ){
        id,name,userCount,userWaittingCount,deviceCount,acTimeCount,BindingCompany{id,name}
    }
}
`;

export const DELETE_AC_MB = gql`
mutation deleteMbAc(
    $acId: String!,
){
    deleteMbAc(
        acId: $acId,
    )
}
`;

export const SEARCH_AC_USER_MB = gql`
query searchMbAcUser(
    $acId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchMbAcUser(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        FirstUser{
            userId,
            userRank,
            isEnable,
            User{
                id,
                name,
                email,
                avatar,
                title,
            },
            replaceUserId,
            Replacer{
                id,
                name,
                email,
                avatar,
                title,
            },
            startTime,
            endTime,
        },
        SecondUser{
            userId,
            userRank,
            isEnable,
            User{
                id,
                name,
                email,
                avatar,
                title,
            },
            replaceUserId,
            Replacer{
                id,
                name,
                email,
                avatar,
                title,
            },
            startTime,
            endTime,
        },
        ThirdUser{
            userId,
            userRank,
            isEnable,
            User{
                id,
                name,
                email,
                avatar,
                title,
            },
            replaceUserId,
            Replacer{
                id,
                name,
                email,
                avatar,
                title,
            },
            startTime,
            endTime,
        },
        AccessUserList{
            userId,
            userRank,
            isEnable,
            User{
                id,
                name,
                email,
                avatar,
                title,
            },
            replaceUserId,
            Replacer{
                id,
                name,
                email,
                avatar,
                title,
            },
            startTime,
            endTime,
            count
        },
    }
}
`;

export const SEARCH_AC_USER_WAITTING_MB = gql`
query searchMbAcUserWaitting(
    $acIds:[String!],
    $search:String!,
    $page:Int!,
    $perPage:Int!,
) {
    searchMbAcUserWaitting(
        acIds:$acIds,
        search:$search,
        page:$page,
        perPage:$perPage,
    ){
        userId,AccessControl{id,name},userRank,isEnable,
        User{id,email,name,title,avatar},
        Replacer{id,email,name,title,avatar},
        actionType,
        startTime,endTime,count
    }
}
`;

export const UPSERT_AC_USER_MB = gql`
mutation upsertMbAcUser(
    $acId: String,
    $userId: String!,
    $userRank: Int!,
){
    upsertMbAcUser(
        acId: $acId,
        userId: $userId,
        userRank: $userRank,
    ){
        accessControlId,
        userId,
        userRank,
    }
}
`;

export const APPROVE_AC_USER_WAITTING = gql`
mutation approveMbAcUserWaitting(
    $accessControlId:String!,
    $userId:String!,
) {
    approveMbAcUserWaitting(
        accessControlId:$accessControlId,
        userId:$userId,
    )
}
`;

export const DELETE_AC_USER_WAITTING = gql`
mutation deleteMbAcUserWaitting(
    $accessControlId:String!,
    $userId:String!,
) {
    deleteMbAcUserWaitting(
        accessControlId:$accessControlId,
        userId:$userId,
    )
}
`;

export const DELETE_MB_AC_USER = gql`
mutation removeMbAcUser(
    $userId:String!,
    $acId:String!,
) {
    removeMbAcUser(
        userId:$userId,
        acId:$acId,
    )
}
`;

export const ADD_MB_AC_REPLACER = gql`
mutation addMbAcReplace(
    $userId:String!,
    $accessControlId:String!,
    $replaceOpenUserId:String!,
    $startTime:Date!,
    $endTime:Date!,
) {
    addMbAcReplace(
        userId:$userId,
        accessControlId:$accessControlId,
        replaceOpenUserId:$replaceOpenUserId,
        startTime:$startTime,
        endTime:$endTime,
    ){
        userId,accessControlId,userRank,isEnable
    }
}
`;

export const DELETE_MB_AC_REPLACER = gql`
mutation removeMbAcReplace(
    $userId:String!,
    $accessControlId:String!,
) {
    removeMbAcReplace(
        userId:$userId,
        accessControlId:$accessControlId,
    )
}
`;

export const GET_AC_USER_BY_ID = gql`
query getMbAcUserById(
    $userId:String!,
    $acId:String!,
){
    getMbAcUserById(
        userId:$userId,
        acId:$acId,
    ){
        userId,
        userRank,
        isEnable,
        User{
            id,
            name,
            email,
            avatar
        },
        replaceUserId,
        Replacer{
            id,
            name,
            email,
            avatar
        },
        startTime,
        endTime,
        count,
    }
}
`;

export const GET_COUNT_MB_AC_USER_WAITING = gql`
query countMbAcUserWaitting(
    $acId:String!,
) {
    countMbAcUserWaitting(
        acId:$acId,
    )
}
`;

export const GET_USER_RANK_BY_ID = gql`
query getUserRankById(
    $userId:String!,
){
    getUserRankById(
        userId:$userId,
    )
}
`;

export const GET_MB_AC_USER_WAITING_DETAIL = gql`
query getMbAcUserWaittingDetail(
    $acId:String!,
    $userId:String!,
) {
    getMbAcUserWaittingDetail(
        acId:$acId,
        userId:$userId,
    ){
        userId,
        AccessControl{id,name},
        userRank,
        isEnable,
        User{id,email,name,title,avatar},Replacer{id,email,name,title,avatar},
        startTime,
        endTime,
        count,
        SystemLog{
            User{id,email,name,avatar,title},
        }
    }
}
`;

export const UPSERT_MB_AC_TIME = gql`
mutation upsertAcTime(
    $acId: String!,
    $acTimeId: String!,
    $name: String!,
    $startDate: Date!,
    $endDate: Date!,
    $startTime: String!,
    $endTime: String!,
    $weekDays: [Int!]!,
    $isEnable: Boolean!,
){
    upsertAcTime(
        acId: $acId,
        acTimeId: $acTimeId,
        name: $name,
        startDate: $startDate,
        endDate: $endDate,
        startTime: $startTime,
        endTime: $endTime,
        weekDays: $weekDays,
        isEnable: $isEnable,
    ){
        id
    }
}
`;

export const SEARCH_MB_AC_TIME = gql`
query searchAcTime(
    $acId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    searchAcTime(
        acId: $acId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        accessControlId,
        name,
        startDate,
        endDate,
        startTime,
        endTime,
        weekDays,
        isEnable,
        count
    }
}
`;