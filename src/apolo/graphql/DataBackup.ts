import { gql } from '@apollo/client';

export const GET_ALL_BACKUP_JOBS = gql`
query GetAllBackupJobs(
    $page: Float!, 
    $perPage: Float!, 
    $tables: String!,
    $type: String!
    ){
        getAllBackupJobs(
            page: $page, 
            perPage: $perPage, 
            tables: $tables,
            type: $type
        ){
            backupTimeStamp
            count
            dateCreated
            dateModified
            id
            isClusterSync
            isRunning
            tableName
            backupFilePath
            fileType
            totalRow
            processedRow
    }
}
`;

export const RESTORE_BACKUP = gql`
mutation restoreBackup(
    $jobId: String
    ){
    restoreBackup(
        jobId: $jobId
    )
  }
`;

export const DELETE_BACKUP = gql`
mutation deleteBackup(
    $jobId: String
    ){
    deleteBackup(
        jobId: $jobId
    )
  }
`;

export const AUTO_BACKUP = gql`
mutation AutoBackups(
    $tables: String
    ){
    autoBackups(
        tables: $tables
    ) 
  }
`;

//#region Ctel Backup
export const GET_CTEL_ORG_INFO = gql`
mutation getCtelOrganizationFullInfo{
    getCtelOrganizationFullInfo{
        totalOrg,
        upsertedOrg,
        skipOrg
    }
}
`;

export const GET_CTEL_EMPLOYEE_INFO = gql`
mutation getCtelAllEmployee{
    getCtelAllEmployee{
        totalUser,
        countUpdateUsers,
        countNewUsers,
        countSkipUsers,
        countLockUsers
    }
}
`;

export const IMPORT_CTEL_TIMEKEEPING_LOG = gql`
mutation importCtelTimeKeepingLog(
    $date: String!,
){
    importCtelTimeKeepingLog(
        date: $date,
    ){
        totalLog,
        importedLog
    }
}
`;

export const IMPORT_CTEL_MONTH_WORKTIME = gql`
mutation importCtelMonthWorkTime(
    $date: String!,
){
    importCtelMonthWorkTime(
        date: $date,
    ){
        totalLog,
        importedLog,
        errLogs
    }
}
`;

export const IMPORT_CTEL_TIMEKEEPING_LOG_BY_MONTH = gql`
mutation importCtelTimeKeepingLogByMonth(
    $month: String!,
){
    importCtelTimeKeepingLogByMonth(
        month: $month,
    ){
        totalLog,
        importedLog
    }
}
`;
//#endregion

export const CLEAN_FACE_RESULT_STORAGE = gql`
mutation cleanFaceResultStorage(
    $maxTime: Date!,
    $password: String!,
){
    cleanFaceResultStorage(
        maxTime: $maxTime,
        password: $password,
    )
}
`;

//#region data import

export const SEARCH_IMPORT_JOB = gql`
query searchImportJob(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $startTime: Date!,
    $endTime: Date!,
    $jobType: Int,
) {
    searchImportJob(
        search: $search,
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        jobType: $jobType,
    ) {
        id,
        jobType,
        importFileName,
        isOverwrite,
        importerId,
        totalRowNumber,
        processedRowNumber,
        errRowsNumber,
        count
    }
}
`;

export const SEARCH_IMPORT_JOB_ROW = gql`
query searchImportJobRow(
    $jobId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $processed: Boolean,
    $isErr: Boolean,
) {
    searchImportJobRow(
        jobId: $jobId,
        search: $search,
        page: $page,
        perPage: $perPage,
        processed: $processed,
        isErr: $isErr
    ) {
        id,
        processed,
        processTime,
        rawData,
        isOverwrite,
        errs,
        resultType,
        count
    }
}
`;

export const EDIT_IMPORT_JOB_ROW = gql`
mutation editImportJobRow(
    $rowId: String!,
    $data: String!,
) {
    editImportJobRow(
        rowId: $rowId,
        data: $data,
    )
}
`;