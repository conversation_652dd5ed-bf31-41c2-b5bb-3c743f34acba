import { gql } from "@apollo/client";

export const GET_FEEDBACK_BY_ID = gql`
query findFeedbackByUserId (
    $search: String!,
    $startTime: Date!,
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
){findFeedbackByUserId
    (
        search: $search,
        startTime: $startTime,
        endTime: $endTime,
        page: $page,
        perPage: $perPage,
    ){
        userId,
        id,
        status,
        type,
        title,
        detail,
        responseTime,
        count,
        User{
            id,
            name
        },
        UserFeedbackResponse{
            id,
            detail,
        },
        UserFeedbackImage{
            id,
            path,
        }
    }
}
`;

export const RESPONSE_FEEDBACK = gql`
query responseFeedback (
    $userFeedbackId: String!,
    $detail: String!,
    $type: Float!,
    $status: Float!,
){responseFeedback
    (
        userFeedbackId: $userFeedbackId,
        detail: $detail,
        type: $type,
        status: $status,
    ){
        userId,
        title,
        detail,
        type,
        status,
        responseTime,
        count, 
        User{
            id,
            name
        },
        UserFeedbackResponse{
            id,
            detail
        },
        UserFeedbackImage{
            id,
        }
    }
}
`;