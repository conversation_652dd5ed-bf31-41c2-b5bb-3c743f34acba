import { gql } from "@apollo/client";

export const FAKE_RESULT_SEARCH = gql`
query searchFakeResult(
    $startDate: Date!, 
    $endDate: Date!,
    $page: Float!,
    $perPage: Float!,
    $userId: String
){searchFakeResult
    (
        startDate:$startDate,
        endDate:$endDate,
        page:$page,
        perPage:$perPage,
        userId:$userId,
    ){
        id,
        deviceId,
        srcDeviceId,
        image,
        fullName,
        integrationKey,
        email,
        time,
        count
    }
}
`;

export const DELETE_FAKE_RESULT = gql`
mutation deleteFakeResult(
    $fakeId: Float!
){deleteFakeResult
    (
        fakeId:$fakeId,
    )
}
`;