import { gql } from "@apollo/client";

export const SEARCH_BLACK_LIST_USER = gql`
query searchBlackListUser(
    $search: String!, 
    $page: Float!, 
    $perPage: Float!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $status: Float,
){
    searchBlackListUser(
        search: $search,
        page: $page,
        perPage: $perPage,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        status: $status
    ){
        id,email,
        fullName,
        integrationKey,
        avatar,
        Company{name,id},
        Department{name,id},
        count
    }
}
`;

export const ADD_BLACK_LIST_USER = gql`
mutation toggleBlacklistUser(   
	$userIds: [String!]!,
    $isBlacklist: Boolean!
){
	toggleBlacklistUser(        
		userIds: $userIds
		isBlacklist: $isBlacklist
    )
}
`;

export const REMOVE_BLACK_LIST_USER = gql`
mutation toggleBlacklistUser(   
	$userIds: [String!]!,
    $isBlacklist: Boolean!
){
	toggleBlacklistUser(        
		userIds: $userIds
		isBlacklist: $isBlacklist
    )
}
`;