import { gql } from "@apollo/client";

export const GET_PROCESSED_REPORT = gql`
query GetProcessedReport(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    GetProcessedReport(
        search: $search,
        page: $page,
        perPage: $perPage
    ){
        companyIds
        dateCreated
        departmentIds
        id
        isRunning
        totalUser
        processedUser
        userIds
        reportFileName
    }
}
`;

export const DOWNLOAD_FILE_REPORT = gql`
query DownloadFileReport(
  $downloadFileReportId: String!
){
  DownloadFileReport(
    id: $downloadFileReportId
  ){
    id
    isRunning
    reportFileName
  }
}
`;

export const DELETE_FILE_REPORT = gql`
mutation DeleteFileReport(
  $deleteFileReportId: String!
) {
  DeleteFileReport(
    id: $deleteFileReportId
  )
}
`;