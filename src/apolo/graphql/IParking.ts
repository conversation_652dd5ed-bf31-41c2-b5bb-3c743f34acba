import { gql } from "@apollo/client";

export const GET_PARKING_LANES = gql`
query getParkingLanePage(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $laneTypes: [Int!]!,
    $vehicleTypes: [Int!]!,
    $barrieModes: [Int!]!,
){
    getParkingLanePage(
        search: $search,
        page: $page,
        perPage: $perPage,
        laneTypes: $laneTypes,
        vehicleTypes: $vehicleTypes,
        barrieModes: $barrieModes,
    ){
        id,
        name,
        laneType,
        vehicleType,
        barrieMode,
        count
    }
}
`;

export const UPSERT_PARKING_LANE = gql`
mutation upsertParkingLane(
    $laneId: String,
    $name: String!,
    $laneType: Int!,
    $vehicleType: Int!,
    $barrieMode: Int!,
){
    upsertParkingLane(
        laneId: $laneId,
        name: $name,
        laneType: $laneType,
        vehicleType: $vehicleType,
        barrieMode: $barrieMode,
    ){
        id,
        name,
        laneType,
        vehicleType,
        barrieMode
    }
}
`;

export const DELETE_PARKING_LANE = gql`
mutation deleteParkingLane(
    $id: String!,
){
    deleteParkingLane(
        id: $id,
    )
}
`;

export const GET_ALL_DEVICES_IN_PARKING_LANE = gql`
query getAllDevicesInParkingLane(
    $parkingId: String!,
){
    getAllDevicesInParkingLane(
        parkingId: $parkingId,
    ){
        id,
        parkingId,
        deviceId,
        cameraId,
        deviceType,
        channel,
        count,
        Device{
            id,
            deviceName
        },
        Camera{
            id,
            name
        }
    }
}
`;

export const ADD_DEVICE_TO_PARING_LANE = gql`
mutation addDeviceToParkingLane(
    $parkingId: String!,
    $deviceId: String!,
    $cameraId: String,
    $deviceType: Int!,
    $channel: Int!,
){
    addDeviceToParkingLane(
        parkingId: $parkingId,
        deviceId: $deviceId,
        cameraId: $cameraId,
        deviceType: $deviceType,
        channel: $channel,
    ){
        id,
        parkingId,
        deviceId,
        cameraId,
        deviceType,
        channel
    }
}
`;

export const DELETE_DEVICE_FROM_PARKING_LANE = gql`
mutation removeDeviceFromParkingLane(
    $id: String!,
){
    removeDeviceFromParkingLane(
        id: $id,
    )
}
`;

export const GET_ALL_LICENSE_ALLOW_PAGE = gql`
query getLicenseAllowPage(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){
    getLicenseAllowPage(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        licensePlate,
        User{id,email,name},
        Guest{id,email,name},
        count
    }
}
`;

export const UPSERT_LICENSE_PLATE_ALLOW = gql`
mutation upsertLicenseAllow(
    $licensePlate: String!,
    $userId: String,
){
    upsertLicenseAllow(
        licensePlate: $licensePlate,
        userId: $userId,
    ){
        licensePlate,
        User {
            id,
            email,
            name
        }
    }
}
`;

export const DELETE_LICENSE_PLATE_ALLOW = gql`
mutation removeLicenseAllow(
    $licensePlate: String!,
){
    removeLicenseAllow(
        licensePlate: $licensePlate,
    )
}
`;

export const GET_LAST_RESULTS_BY_LANE_ID = gql`
query getLastResultsByLaneId(
    $laneId: String!,
){
    getLastResultsByLaneId(
        laneId: $laneId,
    ){
        laneId,
        laneType,
        barrieMode,
        licenseResultId,
        licensePlate,
        licensePlateTime,
        licensePlateImagePath,
        faceResultId,
        userId,
        userTime,
        userImage,
        User{id,name,
            Company{id,name},
            Department{id,name}
        },
        guestResultId,
        guestId,
        guestTime,
        guestImage,
        Guest{id,name,
            Company{id,name},
            Department{id,name}
        },
        isMatched,
        isCheckedIn,
        openBarrie,
        HistoryIn{
            laneId,
            laneType,
            barrieMode,
            licenseResultId,
            licensePlate,
            licensePlateTime,
            licensePlateImagePath,
            faceResultId,
            userId,
            userTime,
            userImage,
            User{id,name,
                Company{id,name},
                Department{id,name}
            },
            guestResultId,
            guestId,
            guestTime,
            guestImage,
            Guest{id,name,
                Company{id,name},
                Department{id,name}
            },
            isMatched,
            isCheckedIn,
            openBarrie,
        },UserAllowed{
            name,
            Company{name},
            Department{name},
        },
        GuestAllowed{
            name,
            Company{name},
            Department{name},
            AcLocations{name}
        }
    }
}
`;

export const GET_LANE_HISTORY_PAGE = gql`
query getLaneHistoryPage(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $laneIds: [String!],
){getLaneHistoryPage
    (
        search: $search,
        page: $page,
        perPage: $perPage,
        laneIds: $laneIds,
    ){
        laneId,
        laneType,
        barrieMode,
        licenseResultId,
        licensePlate,
        licensePlateTime,
        licensePlateImagePath,
        faceResultId,
        userId,
        userTime,
        userImage,
        User{id,name,
            Company{id,name},
            Department{id,name}
        },
        guestResultId,
        guestId,
        guestTime,
        guestImage,
        Guest{id,name,
            Company{id,name},
            Department{id,name}
        },
        isMatched,
        isCheckedIn,
        openBarrie,
        VehicleParkingLane{
            name,laneType
        },
        count
    }
}
`;

export const OPEN_BARRIER = gql`
mutation mannualOpenBarrie(
    $laneId: String!,
){
    mannualOpenBarrie(
        laneId: $laneId,
    ){
        laneId,
        laneType,
        barrieMode,
        licenseResultId,licensePlate,licensePlateTime,licensePlateImagePath,faceResultId,userId,userTime,userImage,User{id,name,Company{id,name},Department{id,name}},guestResultId,guestId,guestTime,guestImage,Guest{id,name,Company{id,name},Department{id,name}},isMatched,isCheckedIn,openBarrie
    }
}
`;

export const TOTAL_LICENSE_PARKING_SUMMARY = gql`
query totalLicenseParkingSummary(
    $startDate: String!,
    $endDate: String!,
){
    totalLicenseParkingSummary(
        startDate: $startDate,
        endDate: $endDate,
    ){
       ocrPlate,countIn,countOut,count
    }
}
`;

export const LICENSE_PARKING_LEFT_IN = gql`
query licenseParkingLeftIn(
    $startDate: String!,
    $endDate: String!,
){
    licenseParkingLeftIn(
        startDate: $startDate,
        endDate: $endDate,
    ){
       ocrPlate,countIn,countOut,count
    }
}
`;