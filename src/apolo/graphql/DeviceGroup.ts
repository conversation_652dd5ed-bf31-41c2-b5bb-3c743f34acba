import { gql } from "@apollo/client";

export const GET_DEVICE_GROUP_INFO = gql`
query searchDeviceGroupInfo(
    $page: Int!
    $perPage: Int!
    $search: String!
){
    searchDeviceGroupInfo(
        page : $page
        perPage : $perPage
        search : $search
    ){
        id,
        name,
        totalDevice,
        piority
        count
    }
}
`;

export const UPSERT_DEVICE_GROUP_INFO = gql`
mutation upsertDeviceGroupInfo(
    $deviceGroupId: String!
    $name: String!
    $piority: Float!
){
    upsertDeviceGroupInfo(
        deviceGroupId: $deviceGroupId
        name: $name
        piority: $piority
    ){
        id,name,piority
    }
}
`;

export const GET_DEVICE_GROUP_BY_ID = gql`
query getDeviceGroupById(
    $deviceGroupId: String!
){
    getDeviceGroupById(
        deviceGroupId: $deviceGroupId
    ){
        id,name
    }
}
`;

export const SEARCH_DEVICE_IN_GROUP = gql`
query searchDeviceInGroup(
    $deviceGroupId: String!
    $page: Int!
    $perPage: Int!
    $search: String!
){
    searchDeviceInGroup(
        deviceGroupId: $deviceGroupId
        page: $page
        perPage: $perPage
        search: $search
    ){
        id,deviceName,count
    }
}
`;

export const SEARCH_DEVICE_NOT_IN_GROUP = gql`
query searchDeviceNotInGroup(
    $deviceGroupId: String!
    $deviceTypes: [Float!]!
    $page: Int!
    $perPage: Int!
    $search: String!
){
    searchDeviceNotInGroup(
        deviceGroupId: $deviceGroupId
        deviceTypes: $deviceTypes
        page: $page
        perPage: $perPage
        search: $search
    ){
        id,deviceName,count
    }
}
`;

export const ADD_DEVICE_TO_GROUP = gql`
mutation addDeviceToGroup(
    $deviceGroupId: String
    $deviceIds: [String!]!
){
    addDeviceToGroup(
        deviceGroupId : $deviceGroupId
        deviceIds : $deviceIds
    ){
        id,deviceName
    }
}
`;

export const DELETE_DEVICE_IN_DEVICE_GROUP = gql`
mutation deleteDeviceinDeviceGroup(
    $deviceGroupId: String!
    $deviceIds: [String!]!
){
    deleteDeviceinDeviceGroup(
        deviceGroupId: $deviceGroupId
        deviceIds: $deviceIds
    )
}
`;

export const DELETE_DEVICE_GROUP_INFO = gql`
mutation deleteDeviceGroupInfo(
    $deviceGroupId: String!
){
    deleteDeviceGroupInfo(
        deviceGroupId: $deviceGroupId
    )
}
`;