import { gql } from "@apollo/client";

export const REPORT_ORG_WORKTIME_BY_MULTIPLE_ID = gql`
query reportOrgWorktimeByMultipleId(
    $organizationIds: [String!]!,
    $startDate: Date!,
    $endDate: Date!,
) {reportOrgWorktimeByMultipleId (
    organizationIds: $organizationIds,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,totalWorkTime
}}
`;

export const REPORT_ORG_AVG_WORKTIME_BY_MULTIPLE_ID = gql`
query reportAvgOrgAvgWorktimeByMultipleId(
    $organizationIds: [String!]!,
    $startDate: Date!,
    $endDate: Date!,
) {reportAvgOrgAvgWorktimeByMultipleId (
    organizationIds: $organizationIds,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,avgTotalWorkTime,totalWorkTime
}}
`;

export const SEARCH_USER_WORKTIME = gql`
query userWorkTimeReport(
    $userRank: Float!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $startDate: String,
    $endDate: String,
    $organizationIds: [String!]!,
){userWorkTimeReport
    (
        userRank: $userRank,
        search: $search,
        page: $page,
        perPage: $perPage,
        startDate: $startDate,
        endDate: $endDate,
        organizationIds: $organizationIds,
    ){
        User{id,name,email},
        totalWorkTime,
        avgTotalWorkTime,
        count
    }
}
`;

export const SEARCH_ALL_SHIFT_USER_WORKTIME = gql`
query allShiftUserWorkTimeReport(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $startDate: String,
    $endDate: String,
){allShiftUserWorkTimeReport
    (
        search: $search,
        page: $page,
        perPage: $perPage,
        startDate: $startDate,
        endDate: $endDate,
    ){
        User{id,name,email},
        totalWorkTime,
        avgTotalWorkTime,
        count
    }
}
`;


export const GET_ORGANIZATION_BY_TYPE = gql`
query searchOrgByType(
    $search: String!,
    $orgLevel: Float!,
) {searchOrgByType (
    search: $search,
    orgLevel: $orgLevel,
    ){
        id,
        name,
        count
    }
}
`;

export const GET_REPORT_ORGANIZATION_WORKTIME_BY_TYPE = gql`
query reportOrgWorktimeByType(
    $search: String!,
    $orgLevel: Float!,
    $startDate: Date!,
    $endDate: Date!,
) {reportOrgWorktimeByType (
    search: $search,
    orgLevel: $orgLevel,
    startDate: $startDate,
    endDate: $endDate,
    ){
        id,
        name,
        totalUser,
        maxInvalid,
        totalInvalid,
        totalLateSecs,
        totalEarlySecs,
        minWorkTime,
        totalWorkTime
    }
}
`;