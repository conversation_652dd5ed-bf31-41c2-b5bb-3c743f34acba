import { gql } from "@apollo/client";

export const LIST_PERMISSIONS = gql`
query listPermissions{
    listPermissions{
        functionId,
        roleId,
        canCread,
        canRead,
        canUpdate,
        canDelete
    }
}
`;

export const EDIT_PERMISSION = gql`
mutation upsertPermission(
    $functionId: String!,
    $oldRoleId: String!,
    $newRoleId: String!,
    $create: Boolean!,
    $read: Boolean!,
    $update: Boolean!,
    $del: Boolean!,
){
    upsertPermission(
        functionId: $functionId,
        oldRoleId: $oldRoleId,
        newRoleId: $newRoleId,
        create: $create,
        read: $read,
        update: $update,
        del:$del,
    ){
        functionId,
        roleId,
        canCread,
        canRead,
        canUpdate,
        canDelete
    }
}
`;

export const DEL_PERMISSION = gql`
mutation delPermission(
    $functionId: String!,
    $roleId: String!
){
    delPermission(
        functionId: $functionId,
        roleId: $roleId,
    )
}
`;