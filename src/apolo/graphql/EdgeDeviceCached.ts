import { gql } from "@apollo/client";

export const SEARCH_CACHED_UNKNOWN_FACE = gql`
query searchCachedUnknownFace(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $startTime: Date!,
    $endTime: Date!,
    $isSentCoreAi: Boolean,
    $deviceIds: [String!],
    $cameraIds: [String!],
){
    searchCachedUnknownFace(
        search: $search,
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        isSentCoreAi: $isSentCoreAi,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds,
    ){
        id,
        trackingId,
        deviceId,
        cameraId,
        image,
        time,
        count
    }
}
`;

export const SEARCH_CACHED_PERSON_RESULT = gql`
query searchCachedPersonResults(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $startTime: Date!,
    $endTime: Date!,
    $isSentCoreAi: <PERSON>olean,
    $deviceIds: [String!],
    $cameraIds: [String!],
){
    searchCachedPersonResults(
        search: $search,
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        isSentCoreAi: $isSentCoreAi,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds,
    ){
        id,
        trackingId,
        deviceId,
        cameraId,
        image,
        time,
        count
    }
}
`;

export const SEARCH_CACHED_GROUP = gql`
query searchCachedGroup(
    $deviceIds: [String!], 
    $cameraIds: [String!], 
    $endTime: Date!, 
    $search: String!,
    $page: Int!, 
    $perPage: Int!, 
    $startTime: Date!,
    $isSentCoreAi: Boolean,
){
    searchCachedGroup(
        deviceIds: $deviceIds, 
        cameraIds: $cameraIds, 
        endTime: $endTime, 
        search: $search
        page: $page, 
        perPage: $perPage, 
        startTime: $startTime,
        isSentCoreAi: $isSentCoreAi
    ){
        cameraId,
        time,
        trackingId,
        deviceId,
        count
    }
}
`;