import { gql } from "@apollo/client";

export const QUERY_GUEST = gql`
query searchUserGuestPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $guestStatus: Float!
){
    searchUserGuestPage(
        search: $search,
        page: $page,
        perPage: $perPage,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        guestStatus: $guestStatus
    ){
        id,
        name,
        cardId,
        phone,
        birthday,
        placeOfBirth,
        address,
        gender,
        nationality,
        Company{
            id,
            name
        },
        Department{
            id,
            name
        },
        count,
        status,
        expiredTime,
        avatarBase64,
        avatar,
        cardImage,
        dateModified,
        AcLocations{id,name},
        ReferenceUser{id,name,avatar}
    }
}
`;

export const QUERY_GUEST_BY_ID = gql`
query searchGuestById(
  $guestId: String!,
){
    searchGuestById(
        guestId: $guestId,
    ){
        id,
        name,
        cardId,
        phone,
        birthday,
        placeOfBirth,
        address,
        gender,
        nationality,
        Company{
            id,
            name
        },
        Department{
            id,
            name
        },
        count,
        status,
        expiredTime,
        avatarBase64,
        cardImage,
        avatar
    }
}
`;

export const EXTEND_TIME = gql`
mutation ExtendTime(
    $duration: String!, 
    $guestId: String!
) {
    extendTime(
        duration: $duration, 
        guestId: $guestId
    )
}
`;

export const FACE_RESULT_GUEST_PAGE = gql`
query faceResultGuestPage(
    $startTime: Date!, 
    $endTime: Date!,
    $page: Float,
    $perPage: Float,
    $textSearch: String,
    $type: Float!,
    $deviceIds: [String!]!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
){
    faceResultGuestPage(
        startTime:$startTime,
        endTime:$endTime,
        page:$page,
        perPage:$perPage,
        textSearch:$textSearch,
        type: $type,
        deviceIds: $deviceIds,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
    ){
        id,
        fullName,
        guestId,
        time,
        image,
        type,
        Device{id,deviceName},
        Company{id,name},
        Department{id,name},
        count
    }
}
`;

export const UPDATE_GUEST = gql`
mutation UpdateGuestInfo(
    $birthday: Date!,
    $placeOfBirth: String!,
    $address: String!,
    $cardId: String!,
    $companyId: String!,
    $departmentId: String!,
    $gender: String!,
    $guestId: String!,
    $nationality: String!,
    $userName: String!
) {
    updateGuestInfo(
        birthday: $birthday,
        placeOfBirth: $placeOfBirth,
        address: $address,
        cardId: $cardId,
        companyId: $companyId,
        departmentId: $departmentId,
        gender: $gender,
        guestId: $guestId,
        nationality: $nationality,
        userName: $userName
    )
}
`;

export const CHANGE_GUEST_STATUS = gql`
mutation changeGuestStatus(
    $isActive: Boolean!,
    $guestIds: [String!]!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
){
    changeGuestStatus(
        isActive: $isActive,
        guestIds: $guestIds,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
    )
}
`;