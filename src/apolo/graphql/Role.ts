import { gql } from "@apollo/client";

export const GET_ROLES = gql`
  query Roles {
    roles {
      id
      name
      description
    }
  }
`;

export const GET_USERS_ROLE = gql`
query GetAllUsersRole {usersRole
  {
      userId,
      roleId,
  }
}
`;

export const UPDATE_USERS_ROLE = gql`
mutation UpdateUserRole(
  $userId: String!,
  $roleId: String!){
      updateUserRole(
          userId:$userId,
          roleId:$roleId){
              userId,
              roleId
  }
}
`;

export const GET_USER_ROLE_PAGE = gql`
query GetUsersRolePage(
  $page: Float!,
  $perPage: Float!,
  $nameSearch: String!,
  $roleId: String!){
      usersRolePage(
          page:$page,
          perPage:$perPage,
          nameSearch:$nameSearch,
          roleId:$roleId){
              userId,
              roleId,
              userName,
              companyName,
              departmentName,
              avatar,
              avatarBase64{
                path,
                data
              },
              email,
              count
  }
}
`;

export const UPDATE_ROLE = gql`
  mutation UpdateRole($id: String!, $description: String!) {
    updateRole(input: { id: $id, description: $description }) {
      id
      name
      description
    }
  }
`;

export const CREATE_ROLE = gql`
  mutation CreateRole ($name: String!, $description: String!){
    createRole(input: {name: $name, description : $description}) {
      id, name, description
    }
  }
`


export const DELETE_ROLE = gql`
mutation DeleteRole($id: String!){
  deleteRole( input: {id :$id }) {
    id, name, description
  }
}
`