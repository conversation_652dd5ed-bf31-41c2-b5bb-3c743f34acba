import { gql } from "@apollo/client";

export const SEARCH_REPORT_USER_FILTER = gql`
query searchReportUserFilter(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){searchReportUserFilter(
    search: $search,
    page: $page,
    perPage: $perPage,
){
    id,
    name,
    companyCount,
    departmentCount,
    userCount,
    excludeCount,
    excludeRuleCount,
    totalUserCount,
    count
}}
`;

export const UPSERT_REPORT_USER_FILTER = gql`
mutation upsertReportUserFilter(
    $id: String,
    $name: String!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $userIds: [String!]!,
    $excludeFilterUserIds: [String!]!,
    $excludeRuleUserIds: [String!]!,
){upsertReportUserFilter(
    id: $id,
    name: $name,
    companyIds: $companyIds,
    departmentIds: $departmentIds,
    userIds: $userIds,
    excludeFilterUserIds: $excludeFilterUserIds,
    excludeRuleUserIds: $excludeRuleUserIds,
){
    id,name
}}
`;

export const GET_REPORT_USER_FILTER = gql`
query getReportUserFilter(
    $id: String!,
){getReportUserFilter(
    id: $id,
){
    id,
    name,
    companyCount,
    departmentCount,
    userCount,
    excludeCount,
    excludeRuleCount,
    totalUserCount
}}
`;

export const GET_REPORT_USER_FILTER_COMPANY = gql`
query getReportUserFilterCompany(
    $filterId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getReportUserFilterCompany(
    filterId: $filterId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    companyId,
    companyName,
    count
}}
`;

export const GET_REPORT_USER_FILTER_DEPARTMENT = gql`
query getReportUserFilterDepartment(
    $filterId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getReportUserFilterDepartment(
    filterId: $filterId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    departmentId,
    departmentName,
    count
}}
`;

export const GET_REPORT_USER_FILTER_USER = gql`
query getReportUserFilterUser(
    $filterId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getReportUserFilterUser(
    filterId: $filterId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    userId,
    userName,
    count,
    avatarBase64 {
        data
        path
      }
}}
`;

export const GET_REPORT_USER_FILTER_EX_USER = gql`
query getReportUserFilterExUser(
    $filterId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getReportUserFilterExUser(
    filterId: $filterId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    userId,
    userName,
    count,
    avatarBase64 {
        data
        path
      }
}}
`;

export const GET_REPORT_USER_FILTER_EX_RULE_USER = gql`
query getReportUserFilterExRuleUser(
    $filterId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getReportUserFilterExRuleUser(
    filterId: $filterId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    userId,
    userName,
    count,
    avatarBase64 {
        data
        path
      }
}}
`;

export const UPSERT_REPORT_USER_FILTER_COMPANY = gql`
mutation upsertReportUserFilterCompany(
    $filterId: String!,
    $companyIds: [String!]!,
){
    upsertReportUserFilterCompany(
    filterId: $filterId,
    companyIds: $companyIds,
)}
`;

export const UPSERT_REPORT_USER_FILTER_DEPARTMENT = gql`
mutation upsertReportUserFilterDepartment(
    $filterId: String!,
    $departmentIds: [String!]!,
){upsertReportUserFilterDepartment(
    filterId: $filterId,
    departmentIds: $departmentIds,
)}
`;

export const UPSERT_REPORT_USER_FILTER_USER = gql`
mutation upsertReportUserFilterUser(
    $filterId: String!,
    $userIds: [String!]!,
){upsertReportUserFilterUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;

export const UPSERT_REPORT_USER_FILTER_EX_USER = gql`
mutation upsertReportUserFilterExUser(
    $filterId: String!,
    $userIds: [String!]!,
){upsertReportUserFilterExUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;

export const UPSERT_REPORT_USER_FILTER_RULE_USER = gql`
mutation upsertReportUserFilterExRuleUser(
    $filterId: String!,
    $userIds: [String!]!,
){upsertReportUserFilterExRuleUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;

export const DELETE_REPORT_USER_FILTER = gql`
mutation deleteReportUserFilter(
    $id: String!,
){deleteReportUserFilter(
    id: $id,
)}
`;

export const DELETE_REPORT_USER_FILTER_COMPANY = gql`
mutation deleteReportUserFilterCompany(
    $filterId: String!,
    $companyIds: [String!]!,
){deleteReportUserFilterCompany(
    filterId: $filterId,
    companyIds: $companyIds,
)}
`;

export const DELETE_REPORT_USER_FILTER_DEPARTMENT = gql`
mutation deleteReportUserFilterDepartment(
    $filterId: String!,
    $departmentIds: [String!]!,
){deleteReportUserFilterDepartment(
    filterId: $filterId,
    departmentIds: $departmentIds,
)}
`;

export const DELETE_REPORT_USER_FILTER_USER = gql`
mutation deleteReportUserFilterUser(
    $filterId: String!,
    $userIds: [String!]!,
){deleteReportUserFilterUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;

export const DELETE_REPORT_USER_FILTER_EX_USER = gql`
mutation deleteReportUserFilterExUser(
    $filterId: String!,
    $userIds: [String!]!,
){deleteReportUserFilterExUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;

export const DELETE_REPORT_USER_FILTER_EX_RULE_USER = gql`
mutation deleteReportUserFilterExRuleUser(
    $filterId: String!,
    $userIds: [String!]!,
){deleteReportUserFilterExRuleUser(
    filterId: $filterId,
    userIds: $userIds,
)}
`;