import { gql } from "@apollo/client";

export const SEARCH_AI_SERVICE = gql`
query searchAiService(
    $search:String!,
    $type:[Float!]!,
    $page:Float!,
    $perPage:Float!
){
    searchAiService(
        search:$search,
        type:$type,
        page:$page,
        perPage:$perPage
    ){
        id,
        name,
        type,
        count,
        isActive,
        dropFps,
        isTimeLimit, 
        startTime, 
        endTime, 
        isPopupAlert, 
        alertDelaySecs,
        configCount,
        roiCount,
        cameraCount
    }
}
`;

export const TOGGLE_AI_SERVICE = gql`
mutation toggleAiService(
    $id:String!,
    $isEnable:Boolean!,
){
    toggleAiService(
        id:$id,
        isEnable:$isEnable,
    )
}
`;

//#region CREATE
export const ADD_FACE_RECOG_SERVICE = gql`
mutation AddFaceRecogService(
    $name: String!,
    $faceCheckType: Float!
    ) {
  createFaceRecognitionService(
      name: $name,
      faceCheckType: $faceCheckType
      ) {id}
  }
`;

export const ADD_PERSON_INTRUSTION_SERVICE = gql`
mutation CeateFaceIntrustionService(
    $name: String!
    ) {
    createPersonIntrustionService(
      name: $name
      ) {id}
  }
`;

//#endregion

//#region READ
export const SEARCH_FACE_SERVICE = gql`
query SearchFaceService(
    $search: String!, 
    $page: Float!,
    $perPage: Float!, 
    ) {
  searchFaceService(
      search: $search,
      page: $page,
      perPage: $perPage
  ) {id,name,faceResultType,status,count}
  }
`;

export const GET_ALL_FACE_SERVICE = gql`
query SearchFaceService {
    allFaceService {id,name,faceResultType,status}
  }
`;

export const SEARCH_PERSON_SERVICE = gql`
query SearchPersonService(
    $search: String!, 
    $page: Float!,
    $perPage: Float!, 
    ) {
  searchPersonService(
      search: $search,
      page: $page,
      perPage: $perPage
  ) {id,name,status,count}
  }
`;

export const GET_ALL_PERSON_SERVICE = gql`
query AllPersonService {
    allPersonService {id,name,status}
  }
`;
//#endregion

//#region UPDATE
export const UPDATE_FACE_SERVICE = gql`
mutation UpdateFaceRecognitionService(
    $serviceId: String!,
    $name: String!,
    $faceCheckType: Float!
  ) {
updateFaceRecognitionService(
    serviceId: $serviceId,
    name: $name,
    faceCheckType: $faceCheckType
){id,name,faceResultType} }
`;

export const UPDATE_PERSON_SERVICE = gql`
mutation UpdatePersonRecognitionService(
    $serviceId: String!,
    $name: String!
  ) {
updatePersonRecognitionService(
    serviceId: $serviceId,
    name: $name
){id,name} }
`;
//#endregion

//#region DELETE
export const DELETE_FACE_SERVICES = gql`
mutation DeletePersonServices(
    $serviceIds: [String!]!
    ) {
  deleteFaceServices(
      serviceIds: $serviceIds
  ) }
`;

export const DELETE_PERSON_SERVICES = gql`
mutation DeletePersonServices(
    $serviceIds: [String!]!
    ) {
  deletePersonServices(
      serviceIds: $serviceIds
  ) }
`;
//#endregion


//#region Old gql
export const GET_AI_SERVICE_PAGE_BY_TYPE = gql`
query GetAiServicePageByType(
    $type: String!,
    $page: Float!,
    $perPage: Float!,
){aiServicePage
    (
        type: $type,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        dateCreated,
        dateModified
    }
}
`;

export const GET_ALL_SERVICE_BY_TYPE = gql`
query GetAllAiServiceByType(
    $type: String!
){allAiServiceByType
    (
        type: $type
    ){
        id,
        name,
        dateCreated,
        dateModified
    }
}
`;

export const GET_AI_SERVICE_BY_ID = gql`
query GetAiServiceById(
    $id: String!,
){aiServiceById
    (
        id: $id,
    ){
        id,
        name,
        type,
        dateCreated,
        dateModified
    }
}
`;

export const GET_AI_SERVICE_IN_CAMERA = gql`
query GetAiServiceInCamera(
    $cameraId: String!,
){cameraAllAiService
    (
        cameraId: $cameraId,
    ){
        id,
        name,
        pipeline,
        type,
        dateCreated,
        dateModified
    }
}
`;

export const GET_ALL_AI_SERVICE_PAGE = gql`
query GetAllAiServicePage(
    $page: Float!,
    $perPage: Float!,
){allAiServicePage
    (
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        type,
        dateCreated,
        dateModified
    }
}
`;

export const GET_ALL_DEVICE_RUNNING_SERVICE = gql`
query GetAllDeviceRunningService(
    $serviceId: String!,
){allDeviceRunningService
    (
        serviceId: $serviceId
    ){
        serviceId,
        deviceId,
        deviceName,
        deviceType,
        piority,
        status
    }
}
`;
//#endregion

export const UPSERT_AISERVICE = gql`
mutation upsertAiService(
    $id: String!,
    $name: String!,
    $type: Float!,
    $configs: [AiServiceConfigInput!],
    $dropFps: Float,
    $isTimeLimit: Boolean,
    $startTime: String,
    $endTime: String,
    $isPopupAlert: Boolean,
    $alertDelaySecs: Float
){
    upsertAiService(
        id: $id,
        name: $name,
        type: $type,
        configs: $configs,
        dropFps: $dropFps,
        isTimeLimit: $isTimeLimit,
        startTime: $startTime,
        endTime: $endTime,
        isPopupAlert: $isPopupAlert,
        alertDelaySecs: $alertDelaySecs
    ){
        id, name, type, dropFps, isTimeLimit, startTime, endTime, isPopupAlert, alertDelaySecs
    }
}
`;

export const COUNT_AISERVICE = gql`
query countAiServiceInDevice(
    $deviceId: String!,
    $cameraId: String!,
){
    countAiServiceInDevice(
        deviceId: $deviceId,
        cameraId: $cameraId
    ){
        deviceId,
        totalService,
        AiServices{
            count,
            type
        }
    }
}
`;
