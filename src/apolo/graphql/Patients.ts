import { gql } from "@apollo/client";

export const SEARCH_PATIENTS = gql`
query searchPatientPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $status: Float!
){searchPatientPage
  (
    search: $search,
    page: $page,
    perPage: $perPage,
    companyIds: $companyIds,
    departmentIds: $departmentIds,
    status: $status
  ){
    id,
    name,
    gender,
    birthDay,
    avatar,
    avatarBase64,
    integrationKey,
    companyId,
    departmentId,
    company{
        id,
        name
    },
    department{
        id,
        name
    },
    count,
    status
    }
}
`;

export const GET_PATIENT_BY_ID = gql`
query searchPatientById(
  $patientId: String!, 
){
  searchPatientById(
    patientId:$patientId,
  ){
    id,
    name,
    gender,
    integrationKey
    avatar,
    avatarBase64,
    faceImages,
    faceImagesArrayBase64{
        id,
        userId,
        faceImageType,
        path,
        data
    },
    status,
    dateCreated,
    dateModified
  }
}
`;

export const DELETE_ALL_PATIENT_IMAGE = gql`
mutation deletePatientImageById(
  $patientId: String!, 
){
  deletePatientImageById(
    patientId:$patientId,
  )
}
`;