import { gql } from "@apollo/client";

export const SEARCH_AC_LOG = gql`
query searchAcLog(
    $page: Int!,
    $perPage: Int!,
    $startTime: Date!,
    $endTime: Date!,
    $acLocationIds: [String!],
    $deviceIds: [String!],
    $types: [Int!]!,
    $isOpen: Boolean,
){
    searchAcLog(
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        acLocationIds: $acLocationIds,
        deviceIds: $deviceIds,
        types: $types,
        isOpen: $isOpen,
    ){
        openDeviceId,
        OpenDevice{id,name},
        AcLocations{id,name},
        localId,
        time,
        isOpen,
        type,
        openUserIds,
        count
    }
}
`;

export const GET_AC_LOG_DETAIL = gql`
query getAcLogDetail(
    $deviceId: String!,
    $localId: String!,
){
    getAcLogDetail(
        deviceId: $deviceId,
        localId: $localId,
    ){
        openDeviceId, 
        time,
        isOpen,
        type,
        localId,
        OpenDevice{id,name},
        openUserIds,
        OpenUsers{id,name,email,title,avatar, userRank},
        scanUserIds,
        ScanUsers{id,name,email,title,avatar,userRank},
        invalidUserIds,
        InvalidUsers{id,name,email,title,avatar,userRank},
        missingUserIds,
        MissingUsers{id,name,email,title,avatar,userRank},
        totalIn,
        totalOut,
        count
    }
}
`;