import { gql } from "@apollo/client";

export const GET_AUTO_S3_CONFIG = gql`
query getAutoS3Config {
    getAutoS3Config{
        id,
        isEnable,
        bucketName,
        prefix,
        resizedPrefix,
        lastScanTime,
        totalFile,
        processedFile
    }
}
`;

export const SCAN_S3_AUTO_CONFIG_FOR_FILES = gql`
mutation scanAutoS3ConfigForFiles(
    $bucketName:String,
    $prefix:String,
    $s3ConfigId:String,
) {
    scanAutoS3ConfigForFiles(
        bucketName:$bucketName,
        prefix:$prefix,
        s3ConfigId:$s3ConfigId
    )
}
`;

export const UPSERT_AUTO_S3_CONFIG = gql`
mutation upsertAutoS3Config(
    $bucketName:String!,
    $prefix:String!,
    $isEnable:Boolean!,
    $resizedPrefix:String!,
    $s3ConfigId:String!,
) {
    upsertAutoS3Config(
        bucketName:$bucketName,
        prefix:$prefix,
        isEnable:$isEnable,
        resizedPrefix:$resizedPrefix,
        s3ConfigId:$s3ConfigId
    ){
        id,
        isEnable,
        bucketName,
        prefix,
        resizedPrefix,
        lastScanTime
    }
}
`;