import { gql } from "@apollo/client";

export const GET_ALL_DEPARTMENT = gql`
query GetAllDepartment {departments
    {
        id,
        parentId,
        name
    }
}
`;

export const SEARCH_DEPARTMENT_PAGE = gql`
query searchDepartmentPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $companyIds: [String!]!,
){searchDepartmentPage
    (
        search: $search,
        page: $page,
        perPage: $perPage,
        companyIds: $companyIds
    ){
        id,
        name,company{id,name},count
    }
}
`;

export const ADD_DEPARTMENT = gql`
mutation AddDepartmentTree(
    $name: String!,
    $parentId: String!,
    $companyId: String!
){createDepartmentTree
    (
        name: $name,
        parentId: $parentId,
        companyId: $companyId
    ){
        id,
        name
    }
}
`;

export const EDIT_DEPARTMENT = gql`
mutation EditDepartment(
    $id: String!,
    $name: String!
){editDepartment
    (
        id: $id,
        name: $name
    ){
        id,
        name
    }
}
`;

export const DELETE_DEPARTMENT = gql`
mutation DeleteDepartment(
    $departmentId: String!
){deleteDepartment
    (
        departmentId: $departmentId
    )
}
`;