import { gql } from "@apollo/client";

export const GET_NEW_ALERT_MESS = gql`
query getNewAlertMess(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getNewAlertMess
    (
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        messeage,
        alertRuleId,
        deviceId,
        time,
        count,
    }
}
`;

export const GET_ALL_NEW_ALERT_MESS = gql`
query getAllNewAlertMess(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $type: String!
){
    getAllNewAlertMess(
        search: $search,
        page: $page,
        perPage: $perPage,
        type: $type
    ){
        id,
        messeage,
        alertRuleId,
        deviceId,
        time,
        count,
    }
}
`;

export const MARK_READ_MESS = gql`
mutation markReadMess(
    $messId: Float!,
){
    markReadMess
    (
        messId: $messId,
    )
}
`;

export const MARK_READ_ALL_MESS = gql`
mutation markAllReadMess {
    markAllReadMess
}
`;