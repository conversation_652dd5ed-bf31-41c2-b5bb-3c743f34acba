import { gql } from "@apollo/client";

export const ALERT_RULE_PAGE = gql`
query alertRulePage(
    $search:String!,
    $types:[Float!]!,
    $alertLevels:[Float!]!,
    $page:Float!,
    $perPage:Float!
){
    alertRulePage(
        search:$search,
        types:$types,
        alertLevels:$alertLevels,
        page:$page,
        perPage:$perPage
    ){
        id,
        type,
        name,
        alertLevel,
        description,
        isRangeLimit,
        isTimeLimit,
        startDate,
        endDate,
        startHour,
        endHour,
        weekDays,
        count
    }
}
`;

export const UPSERT_ALERT_SCRIPT = gql`
mutation upsertAlertScript(
    $ruleId: String!,
    $type: Float!,
    $receiverEmails: [String!]!,
    $receiverIds: [String!]!
){upsertAlertScript
    (
        ruleId: $ruleId,
        type: $type,
        receiverEmails: $receiverEmails,
        receiverIds:$receiverIds
    )
}
`;

export const FIND_ALERT_RULE_DEVICE = gql`
query findAlertRuleDevice(
    $ruleId: String!,
    $type: Float!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    findAlertRuleDevice(
        ruleId: $ruleId,
        type: $type,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        receiverId,
        receiverEmail,
        User{
            avatarBase64{ data, path },
            name,
            id
        },
        count
    }
}
`;

export const DELETE_ALERT_SCRIPT = gql`
mutation deleteAlertScript(
    $ruleId: String!,
    $receiverEmails: [String!],
    $receiverIds: [String!],
){
    deleteAlertScript(
        ruleId: $ruleId,
        receiverEmails: $receiverEmails,
        receiverIds: $receiverIds,
    )
}
`;

export const GET_ALL_TELEGRAM_RECEIVER = gql`
query GetAllTelegramReceiver(
    $page: Float!, 
    $perPage: Float!, 
    $search: String
) {
    getAllTelegramReceiver(
        page: $page, 
        perPage: $perPage, 
        search: $search
    ){
        telegramId
        userId
        User {
            id, name, avatarBase64 {data, path}
        }
        count
    }
}
`;

export const GET_ALL_TELEGRAM_BOT = gql`
query GetAllTelegramBot(
    $page: Float!, 
    $perPage: Float!, 
    $search: String
){
    getAllTelegramBot(
        page: $page, 
        perPage: $perPage, 
        search: $search
    ){
        id
        firstName
        userName
        isEnable
        count
    }
}
`;

export const UPSERT_TELEGRAM_BOT = gql`
mutation UpsertTelegramBot(
    $firstName: String!, 
    $upsertTelegramBotId: String!, 
    $token: String!, 
    $userName: String!
){
    upsertTelegramBot(
        first_name: $firstName, 
        id: $upsertTelegramBotId, 
        token: $token, 
        userName: $userName
    )
}
`;

export const DELETE_TELEGRAM_BOT = gql`
mutation DeleteTelegramBot(
    $deleteTelegramBotId: String!
) {
    deleteTelegramBot(
        id: $deleteTelegramBotId
    )
}
`;