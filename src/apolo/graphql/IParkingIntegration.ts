import { gql } from "@apollo/client";

//#region query from iparking
export const GET_INTEGRATED_CUSTOMER = gql`
query getIntegratedCustomer(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIntegratedCustomer(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, code, phone, address, dob, count
    }
}
`;

export const GET_INTEGRATED_IDENTITY_GROUP = gql`
query getIntegratedIdentityGroups(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIntegratedIdentityGroups(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, code, type, plateNumberValidation, plateNumberComparison, vehicleType, iparkingSyncTime, count
    }
}
`;

export const GET_INTEGRATED_VEHICLE = gql`
query getIntegratedVehicle(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIntegratedVehicle(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, plateNumber,type, customerId, Customer { id, code, name }, expiredDate, checkInByPlate, checkOutByPlate, count
    }
}
`;

export const GET_INTEGRATED_LANE = gql`
query getIntegratedLane(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIntegratedLane(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, code, type, iparkingSyncTime, count
    }
}
`;

export const GET_INTEGRATED_IDENTITY = gql`
query getIntegratedIdentity(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIntegratedIdentity(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, code, type, status, count
    }
}
`;
//#endregion

export const UPSERT_IPARKING_VEHICLE = gql`
mutation upsertIparkingVehicle(
    $name: String!,
    $plateNumber: String!,
    $type: Int!,
    $customerId: String!,
    $expiredDate: Date!,
    $lastActivatedDate: Date!,
    $checkInByPlate: Boolean!,
    $checkOutByPlate: Boolean!,
) {
    upsertIparkingVehicle(
        name : $name
        plateNumber : $plateNumber
        type : $type
        customerId : $customerId
        expiredDate: $expiredDate
        lastActivatedDate: $lastActivatedDate
        checkInByPlate: $checkInByPlate
        checkOutByPlate: $checkOutByPlate
    ) {
        id, name, plateNumber, type, customerId, expiredDate, lastActivatedDate, checkInByPlate, checkOutByPlate
    }
}
`;

export const GET_IPARKING_VEHICLE = gql`
query getIparkingVehicle(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $customerIds: [String!]!,
    $type: Int,
) {
    getIparkingVehicle(
        search: $search,
        page: $page,
        perPage: $perPage,
        customerIds: $customerIds,
        type: $type
    ){
        id, name, plateNumber, type, customerId, Customer { id, name, code }, expiredDate, lastActivatedDate, checkInByPlate, checkOutByPlate, count
    }
}
`;

export const DELETE_IPARKING_VEHICLE = gql`
mutation deleteIparkingVehicle(
    $vehicleId: String!,
) {
    deleteIparkingVehicle(
        vehicleId : $vehicleId
    )
}
`;

export const UPSERT_IPARKING_INTEGRATION_CONFIG = gql`
mutation upsertIparkingIntegration(
    $url: String!,
    $authen_url: String,
    $clientId: String!,
    $clientSecret: String!,
    $configs: String!,
) {
    upsertIparkingIntegration(
        url : $url
        authen_url : $authen_url
        clientId : $clientId
        clientSecret: $clientSecret
        configs: $configs
    ) {
        url, authen_url, client_id, client_secret, isActive, configs, count
    }
}
`;

export const GET_IPARKING_INTEGRATION_CONFIG = gql`
query getIparkingIntegration(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    getIparkingIntegration(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        url, authen_url, authen_url, client_id, client_secret, isActive, configs, count
    }
}
`;

export const TOGGLE_IPARKING_INTEGRATION_CONFIG = gql`
mutation toogleIparkingIntegration(
    $url: String!,
    $isActive: Boolean!,
) {
    toogleIparkingIntegration(
        url : $url
        isActive : $isActive
    ){
        url, authen_url, authen_url, client_id, client_secret, isActive, configs
    }
}
`;

export const DELETE_IPARKING_INTEGRATION_CONFIG = gql`
mutation deleteIparkingIntegration(
    $url: String!,
) {
    deleteIparkingIntegration(
        url : $url
    )
}
`;

//#region query from civams
export const SEARCH_CIVAMS_IDENTITY_GROUP = gql`
query searchCivamsIdentityGroup(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $type: String,
    $plateNumberValidation: Float,
    $plateNumberComparison: Float,
    $vehicleType: Float
) {
    searchCivamsIdentityGroup(
        search: $search,
        page: $page,
        perPage: $perPage,
        type: $type,
        plateNumberValidation: $plateNumberValidation,
        plateNumberComparison: $plateNumberComparison,
        vehicleType: $vehicleType
    ){
        id, name, code, type, plateNumberValidation, plateNumberComparison, vehicleType, iparkingSyncTime, count
    }
}
`;

export const SEARCH_CIVAMS_IDENTITY = gql`
query searchCivamsIdentity(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $type: Int,
    $status: String
) {
    searchCivamsIdentity(
        search: $search,
        page: $page,
        perPage: $perPage,
        type: $type,
        status: $status,
    ){
        id, name, code, type, status, note, iparkingGroupId, iparkingSyncTime, count
    }
}
`;

export const DELETE_CIVAMS_IDENTITY = gql`
mutation deleteCivamsIdentity(
    $id: String!,
) {
    deleteCivamsIdentity(
        id : $id
    )
}
`;

export const SEARCH_CIVAMS_CUSTOMER = gql`
query searchCivamsCustomer(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
) {
    searchCivamsCustomer(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id, name, code, phone, address, dob, iparkingSyncTime, count
    }
}
`;

export const DELETE_CIVAMS_CUSTOMER = gql`
mutation deleteCivamsCustomer(
    $id: String!,
) {
    deleteCivamsCustomer(
        id : $id
    )
}
`;

export const SEARCH_CIVAMS_LANE = gql`
query searchCivamsLane(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $type: Int,
) {
    searchCivamsLane(
        search: $search,
        page: $page,
        perPage: $perPage,
        type: $type,
    ){
        id, name, code, type, iparkingSyncTime, count
    }
}
`;
//#endregion

//#region report dashboard
export const COUNT_VEHICLE_IN_PARKING = gql`
query countVehicleInParking(
    $startTime: Date!,
    $endTime: Date!,
) {
    countVehicleInParking(
        startTime: $startTime,
        endTime: $endTime,
    ){
        day, countByVehicleType { vehicleType, count }
    }
}
`;

export const GET_VEHICLE_IN_OUT = gql`
query getVehicleInOut(
    $startTime: Date!,
    $endTime: Date!,
) {
    getVehicleInOut(
        startTime: $startTime,
        endTime: $endTime,
    ){
        id, plateNumber, status, createdBy, createdUtc, lane { id, name, code, type }, identity { id, name, code, status, type }, identityGroup { id, name, code, type, plateNumberValidation, plateNumberComparison, vehicleType }
    }
}
`;

export const REPORT_TOTAL_FACE_BY_DAY = gql`
query reportTotalFaceByDay(
    $day: Date!,
) {
    reportTotalFaceByDay(
        day: $day
    ){
        day, countUser, countGuest, countReId
    }
}
`;

export const REPORT_TOTAL_FACE_BY_MONTH = gql`
query reportTotalFaceByMonth(
    $day: Date!,
) {
    reportTotalFaceByMonth(
        day: $day
    ){
        day, countUser, countGuest, countReId
    }
}
`;

export const REPORT_FACE_BY_DAY_GROUP_BY_AC = gql`
query ReportFaceByDayGroupByAc(
    $day: Date
) {
    reportFaceByDayGroupByAc(
        day: $day
    ) {
        countUser
        countGuest
        countReId
        day
        deviceId
        deviceName
        AccessControl {
            id
            name
        }
    }
}

`;

export const REPORT_FACE_BY_MONTH_GROUP_BY_AC = gql`
query reportFaceByMonthGroupByAc(
    $day: Date!,
) {
    reportFaceByMonthGroupByAc(
        day: $day
    ){
        day, countUser, countGuest, countReId, deviceId, deviceName, AccessControl { id, name }
    }
}
`;

export const GET_HISTORY_DETAIL_BY_ID = gql`
query getHistoryDetailById(
    $startTime: Date!,
    $endTime: Date!,
    $guestId: String!,
){
    getHistoryDetailById(
        startTime: $startTime,
        endTime: $endTime,
        guestId: $guestId
    ){
        guestId,
        fullName,
        avatar,
        Company { id, name },
        Department { id, name },
        time,
        image,
        deviceId,
        AccessLocations { id, name, isAcValid },
        count
    }
}
`;

export const GET_VEHICLE_IN_OUT_BY_GROUP = gql`
query getVehicleInOutByGroup(
    $startTime: Date!,
    $endTime: Date!,
){
    getVehicleInOutByGroup(
        startTime: $startTime,
        endTime: $endTime,
    ){
        eventIn { groupName, count }, eventOut { groupName, count }
    }
}
`;

export const GET_VEHICLE_IN_OUT_BY_TIME = gql`
query getVehicleInOutByTime(
    $startTime: Date!,
    $endTime: Date!,
){
    getVehicleInOutByTime(
        startTime: $startTime,
        endTime: $endTime,
    ){
        startDate, endDate, eventIn { groupName, count }, eventOut { groupName, count }
    }
}
`;
//#endregion