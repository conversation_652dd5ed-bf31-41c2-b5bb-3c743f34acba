import { gql } from "@apollo/client";

export const GET_REMOTE_RECORGANIZE_APPROVER = gql`
query searchRemoteApprover(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
    $userIds: [String!],
    $depIds: [String!],
    $comIds: [String!],
    $orgIds: [String!],
){searchRemoteApprover
    (
        page: $page,
        perPage: $perPage,
        search: $search,
        userIds: $userIds,
        depIds: $depIds,
        comIds: $comIds,
        orgIds: $orgIds,
    ){
        id,
        email,
        name,
        avatar,
        Company{id,name},
        Department{id,name},
        approverCount,
        userCount,
        depCount,
        comCount,
        orgCount,
        count
    }
}
`;

export const SET_REMOTE_APPROVER = gql`
mutation upsertRemoteApprover(
    $aproverIds: [String!]!,
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $orgIds: [String!]!,
    $isEnable: Boolean!,
){upsertRemoteApprover
    (
        aproverIds: $aproverIds,
        userIds: $userIds,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        orgIds: $orgIds,
        isEnable: $isEnable,
    )
}
`;

export const FULL_DELETE_REMOTE_APPROVER = gql`
mutation fullDeleteRemoteApprove(
    $approverId: String!,
){fullDeleteRemoteApprove
    (
        approverId: $approverId,
    )
}
`;

export const GET_REMOTE_APPROVER_USER = gql`
query getRemoteApproverUser (
    $approverId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteApproverUser(
        approverId: $approverId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        email,
        avatar,
        Company{id,name},
        Department{id,name},
        count
    }
}
`;

export const GET_REMOTE_APPROVER_DEP = gql`
query getRemoteApproverDep (
    $approverId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteApproverDep(
        approverId: $approverId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const GET_REMOTE_APPROVER_COM = gql`
query getRemoteApproverCom (
    $approverId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteApproverCom(
        approverId: $approverId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;

export const GET_REMOTE_APPROVER_ORG = gql`
query getRemoteApproverOrg (
    $approverId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getRemoteApproverOrg(
        approverId: $approverId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        count
    }
}
`;