import { gql } from "@apollo/client";

export const GET_USER_FACE_GENERAL_REPORT_BY_DAYS = gql`
query getUserFaceGeneralReportByDays(
    $companyId: String, 
    $departmentId: String,
    $userId: String,
    $startDate: String!,
    $endDate: String!,
    $timeZoneOffset: String
) {
    getUserFaceGeneralReportByDays(
        companyId: $companyId,
        departmentId: $departmentId,
        userId: $userId,
        startDate: $startDate,
        endDate: $endDate,
        timeZoneOffset: $timeZoneOffset,
    ) {
        fullName,
        company,
        department,
        checkInOutTimes{
            day,
            minTime,
            maxTime
        }
    }
}
`;

export const REPORT_TIMEKEEPING = gql`
query reportTimekeeping(
    $startTime: Date!, 
    $endTime: Date!,
    $deviceIds: [String!]!,
    $page: Float!,
    $perPage: Float!,
    $type: Float!
    $textSearch: String!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $timekeepingOnly: Boolean!
){
    reportTimekeeping(
        startTime:$startTime,
        endTime:$endTime,
        deviceIds: $deviceIds,
        page:$page,
        perPage:$perPage,
        type:$type,
        textSearch:$textSearch,
        departmentIds:$departmentIds,
        companyIds:$companyIds,
        timekeepingOnly:$timekeepingOnly
    ){
        id,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        count,
        SrcDevice{id,name}
    }
}
`;