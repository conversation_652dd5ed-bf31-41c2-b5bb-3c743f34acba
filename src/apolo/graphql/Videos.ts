import { gql } from "@apollo/client";

export const GET_VIDEO_ANALYTIC_LIST = gql`
query getVideoAnalyticList(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $deviceIds: [String!]!,
    $startTime: Date!,
    $endTime: Date!,
) {
    getVideoAnalyticList(
        search: $search,
        page: $page,
        perPage: $perPage,
        deviceIds: $deviceIds,
        startTime: $startTime,
        endTime: $endTime
    ){
        deviceId,
        name,
        videoSrcType,
        cameraId,
        cameraName,
        countSeen,
        count,
    }
}
`;

export const GET_VIDEO_ANALYTIC_SUMMARY = gql`
query getVideoAnalyticSummary(
    $search: String!,
    $deviceId: String!,
    $cameraId: String!,
    $startTime: Date!,
    $endTime: Date!,
) {
    getVideoAnalyticSummary(
        search: $search,
        deviceId: $deviceId,
        cameraId: $cameraId,
        startTime: $startTime,
        endTime: $endTime
    ){
        deviceId,
        name,
        videoSrcType,
        reId, reIdLabel,
        countSeen,
        firstSeen,
        lastSeen,
        count,
    }
}
`;

export const GET_DETAIL_REID_IN_VIDEO = gql`
query getDetailReIdInVideo(
    $cameraId: String!,
    $reId: String!,
) {
    getDetailReIdInVideo(
        cameraId: $cameraId,
        reId: $reId,
    ){
        reId,
        orphanId,
        image,
        timeSeen,
        count,
    }
}
`;

export const SEARCH_UPLOAD_VIDEO = gql`
query searchUploadVideo(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $startTime: Date!,
    $endTime: Date!,
    $groupIds: [String!]!,
){
    searchUploadVideo(
        search: $search,
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        groupIds: $groupIds,
    ){
       id,groupId,fileName,filePath,fileType,fileSize,count
    }
}
`;

export const SEARCH_VIDEO_ANALYTIC_GROUP = gql`
query searchVideoAnalyticGroup(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $types: [String!]!,
){
    searchVideoAnalyticGroup(
        search: $search,
        page: $page,
        perPage: $perPage,
        types: $types,
    ){
       id,name,type,description,faceIdEnable, faceSmartSearchEnable, faceReIdEnable, ageGenderEnable, humanAttEnable, humanSmartSearchEnable, vehicleAttEnable, vehicleSmartSearchEnable, videoAbnormalEnable, s2tEnable, count
    }
}
`;

export const UPSERT_VIDEO_ANALYTIC_GROUP = gql`
mutation upsertVideoGroup(
    $groupId: String!,
    $name: String!,
    $type: String!,
    $description: String!,
    $videoIds: [String!]!,
    $faceIdEnable: Boolean,
    $faceSmartSearchEnable: Boolean,
    $faceReIdEnable: Boolean,
    $ageGenderEnable: Boolean,
    $humanAttEnable: Boolean,
    $humanSmartSearchEnable: Boolean,
    $vehicleAttEnable: Boolean,
    $vehicleSmartSearchEnable: Boolean,
    $videoAbnormalEnable: Boolean,
    $s2tEnable: Boolean
) {
    upsertVideoGroup(
        groupId : $groupId
        name : $name
        type : $type
        description : $description
        videoIds: $videoIds,
        faceIdEnable: $faceIdEnable,
        faceSmartSearchEnable: $faceSmartSearchEnable,
        faceReIdEnable: $faceReIdEnable,
        ageGenderEnable: $ageGenderEnable,
        humanAttEnable: $humanAttEnable,
        humanSmartSearchEnable: $humanSmartSearchEnable,
        vehicleAttEnable: $vehicleAttEnable,
        vehicleSmartSearchEnable: $vehicleSmartSearchEnable,
        videoAbnormalEnable: $videoAbnormalEnable,
        s2tEnable: $s2tEnable
    ) {
        id,name,type,description, faceIdEnable, faceSmartSearchEnable, faceReIdEnable, ageGenderEnable, humanAttEnable, humanSmartSearchEnable, vehicleAttEnable, vehicleSmartSearchEnable, videoAbnormalEnable, s2tEnable
    }
}
`;

export const TOGGLE_VIDEO_ANALYTIC_IN_GROUP = gql`
mutation toggleVideoAnalyticInGroup(
    $groupId: String!,
    $videoIds: [String!]!,
    $isInGroup: Boolean!,
) {
    toggleVideoAnalyticInGroup(
        groupId : $groupId
        videoIds: $videoIds
        isInGroup : $isInGroup
    ) {
        id,name,type,description, faceIdEnable, faceSmartSearchEnable, faceReIdEnable, ageGenderEnable, humanAttEnable, humanSmartSearchEnable, vehicleAttEnable, vehicleSmartSearchEnable, videoAbnormalEnable
    }
}
`;

export const DELETE_VIDEO_ANALYTIC_GROUP = gql`
mutation deleteVideoAnalyticGroup(
    $groupIds: [String!]!,
) {
    deleteVideoAnalyticGroup(
        groupIds : $groupIds
    )
}
`;

export const GET_VIDEO_GROUP_ANALYTIC_SUMMARY = gql`
query getVideoGroupAnalyticSummary(
    $search: String!,
    $groupIds: [String!]!
) {
    getVideoGroupAnalyticSummary(
        search: $search,
        groupIds: $groupIds
    ) {
        videoId, fileName, groupId,
        reId, reIdLabel,
        countSeen, firstSeen, lastSeen,
        bitRate, duration, fileSize, frameHeight, frameRate, frameWidth,
        processDeviceId, processStatus, processingFrame, totalFrame,
        count
    }
}
`;

export const DELETE_UPLOAD_VIDEO_ANALYTIC = gql`
mutation deleteUploadVideoAnalytics(
    $videoId: String!,
) {
    deleteUploadVideoAnalytics(
        videoId: $videoId
    )
}
`;

export const SEARCH_S2T_RESULT = gql`
query searchS2tResult (
    $videoId: String!,
    $startDec: Int!,
    $endSec: Int!,
){
    searchS2tResult(
        videoId: $videoId,
        startDec: $startDec,
        endSec: $endSec
    ){
        uploadAudioId, 
        sectionNumber, 
        start, end, listStart, listEnd,
        humanReadableStart, humanReadableEnd,
        content,
        speakerId,
        confidentScore,
        meanVolume, emotion, count
    }
}
`;