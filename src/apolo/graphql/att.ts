import { gql } from "@apollo/client";

export const SEARCH_ATT_INFO = gql`
query searchAttInfo(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $startTime: Date!,
    $endTime: Date!,
    $deviceIds: [String!],
){
    searchAttInfo(
        search: $search,
        page: $page,
        perPage: $perPage,
        startTime: $startTime,
        endTime: $endTime,
        deviceIds: $deviceIds,
    ){
       id,name,startTime,endTime,count
    }
}
`;

export const SEARCH_ATT_DEVICE = gql`
query searchAttDevice(
    $attInfoId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
){searchAttDevice
    (
        attInfoId: $attInfoId,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
       id,deviceId,Device{id,deviceName,type},Camera{id,name},count
    }
}
`;

export const GET_TOTAL_ATT_DEVICE = gql`
query getTotalAttDevice(
    $attInfoId: String!,
){
    getTotalAttDevice(
        attInfoId: $attInfoId,
    )
}
`;

export const SEARCH_ATT_USER = gql`
query searchAttUser(
    $attInfoId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $isCheck: Boolean,
){searchAttUser
    (
        attInfoId: $attInfoId,
        search: $search,
        page: $page,
        perPage: $perPage,
        isCheck: $isCheck,
    ){
        userId,User{id,email,name,avatar,Company{id,name},Department{id,name}},FaceResult{id,time},isCheck,count
    }
}
`;

export const GET_TOTAL_ATT_USER = gql`
query getTotalAttUser(
    $attInfoId: String!,
){
    getTotalAttUser(
        attInfoId: $attInfoId,
    )
}
`;
export const SEARCH_ATT_GUEST = gql`
query searchAttGuest(
    $attInfoId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $isCheck: Boolean,
){
    searchAttGuest(
        attInfoId: $attInfoId,
        search: $search,
        page: $page,
        perPage: $perPage,
        isCheck: $isCheck,
    ){
        guestId,Guest{id,name,avatar},FaceResult{id,time},isCheck,count
    }
}
`;

export const GET_UN_ATT_USER = gql`
query getUnattUser(
    $attInfoId: String!,
    $page: Int!,
    $perPage: Int!,
){getUnattUser
    (
        attInfoId: $attInfoId,
        page: $page,
        perPage: $perPage,
    ){
        FaceResult{time},User{id,email,name,avatar},count
    }
}
`;

export const GET_UN_ATT_GUEST = gql`
query getUnattGuest(
    $attInfoId: String!,
    $page: Int!,
    $perPage: Int!,
){getUnattGuest
    (
        attInfoId: $attInfoId,
        page: $page,
        perPage: $perPage,
    ){
        FaceResult{time},Guest{id,name,avatar},count
    }
}
`;

export const GET_UN_ATT_UNKNOWN = gql`
query getUnattUnknown(
    $attInfoId: String!,
    $page: Int!,
    $perPage: Int!,
){
    getUnattUnknown(
        attInfoId: $attInfoId,
        page: $page,
        perPage: $perPage,
    ){
        cameraIp,
        time,
        trackingId,
        deviceId,
        SrcDevice{name},
        count
    }
}
`;

export const UPSERT_ATT_INFO = gql`
mutation upsertAttInfo(
    $attInfoId: String,
    $name: String!,
    $startTimeString: String!,
    $endTimeString: String!,
){upsertAttInfo
    (
        attInfoId: $attInfoId,
        name: $name,
        startTimeString: $startTimeString,
        endTimeString: $endTimeString,
    ){
       id,name,startTime,endTime,count
    }
}
`;

export const DELETE_ATT_INFO = gql`
mutation deleteAttInfo(
    $attInfoId: String,
){deleteAttInfo
    (
        attInfoId: $attInfoId,
    )
}
`;

export const UPSERT_ATT_DEVICE = gql`
mutation upsertAttDevice(
    $attInfoId: String!,
    $deviceId: String!,
    $cameraId: String,
){upsertAttDevice
    (
        attInfoId: $attInfoId,
        deviceId: $deviceId,
        cameraId: $cameraId,
    ){id}
}
`;

export const DELETE_ATT_DEVICE = gql`
mutation deleteAttDevice(
    $id: String!,
){deleteAttDevice
    (
        id: $id,
    )
}
`;

export const UPSERT_ATT_USER = gql`
mutation upsertAttUsers(
    $attInfoId: String!,
    $userIds: [String!]!,
    $isActive: Boolean,
){upsertAttUsers
    (
        attInfoId: $attInfoId,
        userIds: $userIds,
        isActive: $isActive,
    )
}
`;

export const UPSERT_ATT_GUEST = gql`
mutation upsertAttGuests(
    $attInfoId: String!,
    $guestIds: [String!]!,
    $isActive: Boolean,
){
    upsertAttGuests(
        attInfoId: $attInfoId,
        guestIds: $guestIds,
        isActive: $isActive,
    )
}
`;