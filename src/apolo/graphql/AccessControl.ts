import { gql } from "@apollo/client";

export const SEARCH_AC_LOCATION = gql`
query GetACLocations(
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {acLocations (
    search: $search,
    page: $page,
    perPage: $perPage
){
    id,
    name,
    status,
    count,
    isAlwayOpen
}}
`;

export const GET_AC_LOCATION_BY_ID = gql`
query GetACLocationById(
    $locationId: String!
) {acLocationById (
    locationId: $locationId
){
    id,
    status,
    name
}}
`;

export const COUNT_AC_DEVICE_IN_LOCAITON = gql`
query CountACDevice(
    $locationId: String!
) {countACDeviceInLocation (
    locationId: $locationId
)}
`;

export const COUNT_AC_DEP_IN_LOCAITON = gql`
query CountACDep(
    $locationId: String!
) {countACDepInLocation (
    locationId: $locationId
)}
`;

export const COUNT_AC_USER_IN_LOCAITON = gql`
query CountACUser(
    $locationId: String!
) {countACUserInLocation (
    locationId: $locationId
)}
`;

export const COUNT_AC_CAMERA_IN_LOCAITON = gql`
query CountACCamera(
    $acLocationId: String!
) {countAcCameraByLocation (
    acLocationId: $acLocationId
)}
`;

export const COUNT_AC_PATIENT_IN_LOCAITON = gql`
query CountACPatient(
    $locationId: String!
) {countACPatientInLocation (
    locationId: $locationId
)}
`;

export const ADD_NEW_AC_LOCATION = gql`
mutation AddACLocation(
    $deviceIds: [String!]!,
    $locationName: String!
) {addACLocation (
    deviceIds: $deviceIds
    locationName: $locationName
){
    id,
    name
}}
`;

export const GET_AC_DEVICE_IN_LOCATION = gql`
query GetACDeviceInLocation(
    $locationId: String!,
) {acDevicesInLocation (
    locationId: $locationId
){
    deviceId,
    deviceName,
    deviceType
}}
`;

export const GET_AC_DEVICE_IN_LOCATION_PAGE = gql`
query GetACDeviceInLocationPage(
    $locationId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {acDevicesInLocationPage (
    locationId: $locationId,
    search: $search,
    page: $page,
    perPage: $perPage
){
    deviceId,
    deviceName,
    deviceType,
    count
}}
`;

export const GET_ALL_AC_BY_DEVICE = gql`
query GetAllACByDevice(
    $deviceId: String!
) {getAccessControlByDevice (
    deviceId: $deviceId
){
        userId,
        groupId,
        deviceId,
        startTime,
        duration
    }
}
`;

export const SEARCH_GROUP_AC_BY_DEVICE = gql`
query GetAllACByDevice(
    $deviceId: String!,
    $page: Float!,
    $perPage: Float!,
    $search: String!,
) {searchGroupACByDevice (
    deviceId: $deviceId
    page: $page
    perPage: $perPage
    search: $search
){
        groupId,
        groupName,
        deviceId,
        startTime,
        duration
    }
}
`;

export const SEARCH_USER_AC_BY_DEVICE = gql`
query SearchUserAC(
    $deviceId: String!,
    $page: Float!,
    $perPage: Float!,
    $search: String!,
) {searchUserACByDevice (
    deviceId: $deviceId
    page: $page
    perPage: $perPage
    search: $search
){
        userId,
        userName,
        userAvt,
        userEmail,
        userCompany,
        userDepartment,
        deviceId,
        startTime,
        duration
    }
}
`;

export const GET_AC_DEPARTMENTS = gql`
query GetACDepsByLocations(
    $locationId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {acDeps (
    locationId: $locationId,
    search: $search
    page: $page,
    perPage: $perPage
){
    departmentId,
    departmentName,
    Company{id,name},
    count
}}
`;

export const GET_USERS_IN_DEPARTMENT = gql`
query GetUserInDepartment(
    $input: UserSearchInput!,
){search
    (
        input: $input
    ){
        avatar,
        fullName,
        email,
        roles{id},
        department{id,name},
        birthDay
        gender,
        phoneNumber,
        company{id,name},
        faceImages,
        id,
        dateCreated,
        dateModified,
        status,
        count
    }
}
`;

export const IN_OUT_BY_DEPARTMENT = gql`
query GetTimekeepingPageSearch(
    $startTime: Date!, 
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $type: Float!,
    $departmentIds: [String!]!,
    $textSearch: String!
){timekeepingPageSearch
    (
        startTime:$startTime,
        endTime:$endTime,
        page:$page,
        perPage:$perPage,
        type:$type,
        departmentIds: $departmentIds
        textSearch:$textSearch
    ){
        id,
        cameraIp,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        count
    }
}
`;

export const GET_DEPARTMENT_AC_HOUR = gql`
query GetACDepTime(
    $locationId: String!,
    $departmentId: String!
) {acDepTimes (
    locationId: $locationId,
    departmentId: $departmentId,
){
    locationId,
    departmentId,
    acTimes{
        id,name,startDate,endDate,startTime,endTime,weekDays, status
    }
}
}
`;

export const GET_AC_USERS = gql`
query GetACUsersByLocations(
    $locationId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {acUsers (
    locationId: $locationId,
    search: $search
    page: $page,
    perPage: $perPage
){
    userId,
    userName,
    count,
    avatarBase64 {
        data
        path
      }
}}
`;

export const GET_AC_CAMERA = gql`
query GetACCameraByLocations(
    $acLocationId: String!,
    $search: String!,
    $page: Int!,
    $perPage: Int!
){
    searchAcCamera (
        acLocationId: $acLocationId,
        search: $search
        page: $page,
        perPage: $perPage
    ){
        acLocationId,Camera{id,name},count
    }
}
`;

export const UPSERT_AC_CAMERA = gql`
mutation UpsertAcCamera(
    $acLocationId: String!,
    $cameraIds: [String!]!,
    $isActive: Boolean!
){
    upsertAcCamera (
        acLocationId: $acLocationId,
        cameraIds: $cameraIds,
        isActive: $isActive
    )
}
`;

export const GET_AC_PATIENTS = gql`
query acPatientsByLocation(
    $locationId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {acPatientsByLocation (
    locationId: $locationId,
    search: $search
    page: $page,
    perPage: $perPage
){
    patientId,
    patientName,
    count
}}
`;

export const GET_ALL_USERS = gql`
query GetFilteredUser(
    $companyId: String!,
    $departmentId: String!,
    $userStatus: Float!
){
    userNamesFiltered(
        companyId:$companyId,
        departmentId:$departmentId,
        userStatus:$userStatus,
    ){
        id,
        fullName
    }
}
`;

export const GET_AC_HOURS = gql`
query GetAllACTimes {allACTimes {
    id,
    name,
    startDate, 
    endDate, 
    startTime, 
    endTime, 
    weekDays
    }
}
`;

export const ADD_AC_HOUR = gql`
mutation AddACTime(
    $name: String!, 
    $startDate: Date!,
    $endDate: Date!,
    $startTime: TimeOfDay!, 
    $endTime: TimeOfDay!, 
    $weekDays: [Float!]!
    ) {
  addACTime(
    name: $name,
    startDate: $startDate,
    endDate : $endDate,
    startTime: $startTime,
    endTime: $endTime,
    weekDays: $weekDays,
  ) {
      id,name,startDate,endDate,startTime,endTime,weekDays
  }
}
`;

export const UPDATE_AC_HOUR = gql`
mutation UpdateACTime(
    $acTimeId: String!
    $name: String!, 
    $startDate: Date!,
    $endDate: Date!,
    $startTime: TimeOfDay!, 
    $endTime: TimeOfDay!, 
    $weekDays: [Float!]!
    ) {
        updateACTime(
    acTimeId: $acTimeId,
    name: $name,
    startDate: $startDate,
    endDate : $endDate,
    startTime: $startTime,
    endTime: $endTime,
    weekDays: $weekDays,
  ) {
      id,name,startDate,endDate,startTime,endTime,weekDays
  }
}
`;

export const ADD_AC_DEP_TIMES = gql`
mutation AddACDepTimes(
    $locationIds: [String!]!,
    $departmentIds: [String!]!,
    $acTimeIds: [String!]!
) {addAcDepTimes (
    locationIds: $locationIds,
    departmentIds: $departmentIds,
    acTimeIds: $acTimeIds
){
    locationId,
    locationName,
    departmentId,
    departmentName,
    acTimes{
        id,
        name,
    }
}
}
`;

export const ADD_AC_USER_TIMES = gql`
mutation AddACUserTimes(
    $locationIds: [String!]!,
    $userIds: [String!]!,
    $acTimeIds: [String!]!
) {addAcUserTimes (
    locationIds: $locationIds,
    userIds: $userIds,
    acTimeIds: $acTimeIds
){
    userId,
    locationId,
    acTimes{
        id,name,startDate,endDate,startTime,endTime,weekDays
    }
}}
`;

export const REMOVE_AC_DEP_TIMES = gql`
mutation DeleteACDepTimes(
    $locationIds: [String!]!,
    $departmentIds: [String!]!,
    $acTimeIds: [String!]!
) {deleteAcDepTimes (
    locationIds: $locationIds,
    departmentIds: $departmentIds,
    acTimeIds: $acTimeIds
)}
`;

export const REMOVE_AC_DEP_TIMES_AT_LOCATION = gql`
mutation DeleteAllAcDepTimesAtLocation(
    $locationIds: [String!]!,
    $departmentIds: [String!]!,
) {deleteAllAcDepTimesAtLocation (
    locationIds: $locationIds,
    departmentIds: $departmentIds,
)}
`;

export const CHANGE_STATUS_AC_DEP_TIME = gql`
mutation changeAcDepTimeStatus(
    $locationId: String!,
    $departmentId: String!,
    $acTimeId: String!,
    $status: String!
){ changeAcDepTimeStatus (
    locationId: $locationId,
    departmentId: $departmentId,
    acTimeId: $acTimeId,
    status:$status
){
    locationId,
    departmentId,
    departmentName,
    status
}}
`;


export const REMOVE_AC_USER_TIMES = gql`
mutation DeleteACUserTimes(
    $locationIds: [String!]!,
    $userIds: [String!]!,
    $acTimeIds: [String!]! 
    ) {deleteAcUserTimes (  
    locationIds: $locationIds,  
    userIds: $userIds,  
    acTimeIds: $acTimeIds  
    )}
`;

export const REMOVE_AC_USER_TIMES_AT_LOCATION = gql`
mutation DeleteACUserTimes(
    $locationIds: [String!]!,
    $userIds: [String!]!,
) {deleteAllAcUserTimes (
    locationIds: $locationIds,
    userIds: $userIds,
)}
`;


export const REMOVE_AC_PATIENT_TIMES_AT_LOCATION = gql`
mutation deleteAllAcPatientTimes(
    $locationIds: [String!]!,
    $patientIds: [String!]!,
) {deleteAllAcPatientTimes (
    locationIds: $locationIds,
    patientIds: $patientIds,
)}
`;


export const COUNT_AC_LOCATION_USING_TIME = gql`
query CountACLocationUsingTime(
    $acTimeId: String!,
) {acLocationssUsingTime (
    acTimeId: $acTimeId,
){
    id, name
}
}`;

export const COUNT_AC_DEP_USING_TIME = gql`
query CountACDepUsingTime(
    $acTimeId: String!,
) {acDepsUsingTime (
    acTimeId: $acTimeId,
){
    id, name
}
}`;

export const COUNT_AC_USER_USING_TIME = gql`
query CountACUserUsingTime(
    $acTimeId: String!,
) {acUsersUsingTime (
    acTimeId: $acTimeId,
){
    id, email, fullName, avatar, gender
}
}`;


export const GET_AC_HOUR_IN_LOCATION = gql`
query getAcTimeInLocation(
    $locationIds: [String!]!,
) {getAcTimeInLocation (
    locationIds: $locationIds,
){
    id,
    name,
    startDate, 
    endDate, 
    startTime, 
    endTime, 
    weekDays
}
}`;

export const DEL_AC_HOUR_FROM_LOCATION = gql`
mutation delAcTimeInLocation(
    $locationIds: [String!]!,
    $acTimeIds: [String!]! 
){delAcTimeInLocation
    (
        locationIds: $locationIds,
        acTimeIds: $acTimeIds,
    )
}
`;

export const ADD_AC_HOUR_TO_LOCATION = gql`
mutation addAcTimeToLocation(
    $locationId: String!,
    $acTimeId: String! 
){addAcTimeToLocation
    (
        locationId: $locationId,
        acTimeId: $acTimeId,
    ){
        id,
        name,
        startDate, 
        endDate, 
        startTime, 
        endTime, 
        weekDays
    }
}
`;

export const UPDATE_LOCATION_NAME = gql`
mutation  updateACLocationName(
    $locationId: String!,
    $locationName: String! 
){updateACLocationName
    (
        locationId: $locationId,
        locationName: $locationName,
    ){
        id,
        name
    }
}
`;

export const DELETE_LOCATION = gql`
mutation  deleteACLocation(
    $locationId: String!,
){deleteACLocation
    (
        locationId: $locationId,
    )
}
`;

export const CHANGE_TIME_STATUS = gql`
mutation  changeTimeStatusAcLocation(
    $locationId: String!,
    $isAlwayOpen: Boolean!,
){changeTimeStatusAcLocation
    (
        locationId: $locationId,
        isAlwayOpen: $isAlwayOpen
    ){
        name,
        id,
        status
    }
}
`;

export const ADD_AC_GUEST_TIMES = gql`
mutation addAcGuestTimes(
    $access: [ACGuestInput!]!
){
    addAcGuestTimes(
        access: $access
    )
}
`;

export const GET_AC_GUEST_TIMES_BY_GUEST_ID = gql`
query getAcGuestTimesByGuestId(
    $guestId: String!,
    $locationIds: [String!]!,
    $timeIds: [String!]!,
    $page: Float!,
    $perPage: Float!
) {getAcGuestTimesByGuestId (
    guestId: $guestId,
    locationIds: $locationIds,
    timeIds: $timeIds,
    page: $page,
    perPage: $perPage
){
    # id,
    locationId,
    locationName, 
    guestId, 
    count,
    acTime{
        id,
        name,
        startDate,
        endDate,
        startTime,
        endTime,
        weekDays
    }
}
}
`;

export const DEL_AC_GUEST_TIME = gql`
mutation delAcGuestTime(
    $id: String!
){
    delAcGuestTime(
        id: $id
    ){
        locationId, locationName, guestId, guestName
    }
}
`;

export const SEARCH_AC_GUEST_LOCATION = gql`
query searchGuestACLocations(
    $search: String!,
    $page: Float!,
    $perPage: Float!
) {searchGuestACLocations (
    search: $search,
    page: $page,
    perPage: $perPage
){
    id,
    name,
    status,
    count,
    isAlwayOpen
}}
`;

export const GET_AC_GUEST_TIMES_BY_LOCATION_ID = gql`
query getAcGuestTimesByLocationId(
    $locationId: String!,
    $guestIds: [String!]!,
    $timeIds: [String!]!,
    $page: Float!,
    $perPage: Float!
) {getAcGuestTimesByLocationId (
    locationId: $locationId,
    guestIds: $guestIds,
    timeIds: $timeIds,
    page: $page,
    perPage: $perPage
){
    locationId,
    locationName, 
    guestId,
    guestName, 
    count,
    acTime{
        id,
        name,
        startDate,
        endDate,
        startTime,
        endTime,
        weekDays
    }
}
}
`;

export const GET_AC_PATIENT_TIMES_BY_GUEST_ID = gql`
query getAcPatientTimesByPatientId(
    $patientId: String!,
    $locationIds: [String!]!,
    $timeIds: [String!]!,
    $page: Float!,
    $perPage: Float!
){getAcPatientTimesByPatientId
    (
    patientId: $patientId,
    locationIds: $locationIds,
    timeIds: $timeIds,
    page: $page,
    perPage: $perPage
    ){
        id,
        locationId,
        locationName, 
        patientId, 
        count,
        acTime{
            id,
            name,
            startDate,
            endDate,
            startTime,
            endTime,
            weekDays
        }
    }
}
`;

export const GET_AC_PATIENT_TIMES_BY_LOCATION_ID = gql`
query getAcPatientTimesByLocationId(
    $locationId: String!,
    $patientIds: [String!]!,
    $timeIds: [String!]!,
    $page: Float!,
    $perPage: Float!
){getAcPatientTimesByLocationId
    (
        locationId: $locationId,
        patientIds: $patientIds,
        timeIds: $timeIds,
        page: $page,
        perPage: $perPage
        ){
        id,
        locationId,
        locationName, 
        patientId, 
        patientName,
        count,
        acTime{
            id,
            name,
            startDate,
            endDate,
            startTime,
            endTime,
            weekDays
        }
    }
}
`;

export const ADD_AC_PATIENT_TIMES = gql`
mutation addAcPatientTimes(
    $access: [ACPatientInput!]!
){
    addAcPatientTimes(
        access: $access
    )
}
`;

export const DEL_AC_PATIENT_TIME = gql`
mutation delAcPatientTime(
    $id: String!
){
    delAcPatientTime(
        id: $id
    ){
        id
    }
}
`;

export const SEARCH_PATIENT_AC_LOCATIONS = gql`
query searchPatientACLocations(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
) {searchPatientACLocations (
    page: $page
    perPage: $perPage
    search: $search
){
    id,
    name,
    status,
    count,
    isAlwayOpen
    }
}
`;