import { gql } from "@apollo/client";

export const SEARCH_LOCATION = gql`
query searchLocation(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchLocation(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        description,
        parentId,
        count
    }
}
`;

export const UPSERT_LOCATION = gql`
mutation upsertLocation(
    $locationId: String,
    $locationName: String!,
    $description: String,
    $parentId: String,
){
    upsertLocation(
        locationId: $locationId
        locationName: $locationName
        description: $description
        parentId: $parentId
    ){
        id,
        name,
        description,
        parentId
    }
}
`;

export const DELETE_LOCATION = gql`
mutation removeLocation(
    $locationId: String!,
){
    removeLocation(
        locationId: $locationId,
    )
}
`;

export const ADD_DEVICE_INTO_LOCATION = gql`
mutation addDeviceIntoLocation(
    $locationId: String!,
    $deviceIds: [String!]!,
){
    addDeviceIntoLocation(
        locationId: $locationId,
        deviceIds: $deviceIds
    )
}
`;

export const REMOVE_DEVICE_FROM_LOCATION = gql`
mutation removeDeviceFromLocation(
    $locationId: String!,
    $deviceIds: [String!]!,
){
    removeDeviceFromLocation(
        locationId: $locationId,
        deviceIds: $deviceIds
    )
}
`;

export const COUNT_DEVICE_IN_LOCATION = gql`
query countDeviceInLocation(
    $locationId: String!,
){
    countDeviceInLocation(
        locationId: $locationId
    )
}
`;

export const GET_DEVICES_IN_LOCATION = gql`
query getDeviceInLocation(
    $locationId: String!,
){
    getDeviceInLocation(
        locationId: $locationId,
    ){
        id,
        name,
        type,
        count
    }
}
`;

export const COUNT_FACE_RES_UNIQUE_BY_LOCATION = gql`
query countFaceResUniqueByLocation(
    $locationId: String!,
    $startTime: Date!,
    $endTime: Date!,
){
    countFaceResUniqueByLocation(
        locationId: $locationId,
        startTime: $startTime,
        endTime: $endTime,
    )
}
`;

export const ADD_GUEST_INTO_LOCATION = gql`
mutation addGuestIntoLocation(
    $locationId: String!,
    $guestIds: [String!]!,
){
    addGuestIntoLocation(
        locationId: $locationId,
        guestIds: $guestIds
    )
}
`;

export const REMOVE_GUEST_FROM_LOCATION = gql`
mutation removeGuestFromLocation(
    $locationId: String!,
    $guestIds: [String!]!,
){
    removeGuestFromLocation(
        locationId: $locationId,
        guestIds: $guestIds
    )
}
`;

export const COUNT_GUEST_IN_LOCATION = gql`
query countGuestInLocation(
    $locationId: String!,
){
    countGuestInLocation(
        locationId: $locationId
    )
}
`;

export const GET_GUEST_IN_LOCATION = gql`
query getGuestInLocation(
    $locationId: String!,
){
    getGuestInLocation(
        locationId: $locationId,
    ){
        id,
        name,
        avatar,
        cardId,
        email,
        phone,
        birthday,
        placeOfBirth,
        address,
        gender,
        nationality,
        companyId,
        Company{
            id,
            name
        },
        departmentId,
        Department{
            id,
            name
        }
        expiredTime
    }
}
`;