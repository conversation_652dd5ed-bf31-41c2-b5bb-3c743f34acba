import { gql } from "@apollo/client";

export const CALCULATE_WORK_MONTH = gql`
mutation calculateWorkMonth(
    $month: Float!
    $year: Float!
    $userIds: [String!]
    $companyIds: [String!]
){
    calculateWorkMonth(
        userIds: $userIds
        companyIds: $companyIds
        month: $month
        year: $year
    ){
        userId,
        days,
        congPhep,
        tongCong
        month,
        year, 
        cycleDate,
        congThucTe,
        phatTien,
        congCheDo
    }
}
`;