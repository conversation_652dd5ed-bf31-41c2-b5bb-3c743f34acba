import { gql } from "@apollo/client";

export const GET_DEVICE_CAMERAS = gql`
query getDeviceCameras(
    $deviceId: String!,
    $search: String!,
){
    getDeviceCameras(
        deviceId:$deviceId,
        search: $search
    ){
      cameraId,cameraName,cameraIp
    }
}
`;

export const GET_DEVICE_CAMERAS_NOT_IN_DEVICE = gql`
query camerasNotInDevice(
    $deviceId: String!,
){camerasNotInDevice
    (
        deviceId:$deviceId,
    ){
      id,name
    }
}
`;

export const ADD_CAMERA_TO_DEVICE = gql`
mutation addCamerasToDevice(
  $deviceId: String!,
  $cameraIds: [String!]!,
  $isDisplay: [Boolean!]!,
)
{addCamerasToDevice
  (
      deviceId:$deviceId,
      cameraIds:$cameraIds,
      isDisplay:$isDisplay,
  ){cameraId,deviceId,isDisplay}
}
`;

export const REMOVE_CAMERAS_FROM_DEVICE = gql`
mutation removeCamerasFromDevice(
  $deviceId: String!,
  $cameraIds: [String!]!
  ) {
removeCamerasFromDevice(
    deviceId: $deviceId,
    cameraIds: $cameraIds
) }
`;

export const SYNC_DEVICE_CAMERA_MANNUALY = gql`
mutation sendDeviceCameraConfigRequest(
  $deviceId: String!,
)
{sendDeviceCameraConfigRequest
  (
      deviceId:$deviceId,
  )
}
`;


export const GET_DEVICE_ROI_CONFIG = gql`
query getDeviceRoiConfig(
    $deviceRoiId: String,
    $deviceId: String!,
){
    getDeviceRoiConfig(
        deviceRoiId:$deviceRoiId,
        deviceId:$deviceId,
    ){
        id,
        name,
        xs,
        ys,
        deviceId,
        AiServices{
            id,
            name,
        },
        count
    }
}
`;

export const UPSERT_DEVICE_ROI = gql`
mutation upsertDeviceRoi(
    $id: String,
    $deviceId: String!,
    $name: String!,
    $xs: [String!]!,
    $ys: [String!]!,
    $aiServiceIds: [String!],
){
    upsertDeviceRoi(
        id:$id,
        deviceId:$deviceId,
        name:$name,
        xs:$xs,
        ys:$ys,
        aiServiceIds:$aiServiceIds,
    ){
        id,
        deviceId,
        name,
        xs,
        ys,
    }
}
`;

export const TOGGLE_DEVICE_ROI = gql`
mutation toggleDeviceRoi(
    $id: String!,
    $isEnable: Boolean!,
){
    toggleDeviceRoi(
        id:$id,
        isEnable:$isEnable,
    ){
        id,
        isEnable,
    }
}
`;

export const DELETE_DEVICE_ROI = gql`
mutation deleteDeviceRoi(
    $id: String!,
){
    deleteDeviceRoi(
        id:$id,
    )
}
`;

export const GET_DEVICE_SNAPSHOT = gql`
query getDeviceSnapshot(
    $deviceId: String!,
    $cameraId: String,
){
    getDeviceSnapshot(
        deviceId: $deviceId,
        cameraId: $cameraId
    )
}
`;