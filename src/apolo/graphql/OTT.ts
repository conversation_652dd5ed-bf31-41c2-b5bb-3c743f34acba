import { gql } from "@apollo/client";

export const GET_ACTIVE_TELEGRAM_BOT = gql`
query getActiveTelegramBot{
    getActiveTelegramBot{
        id,userName,token,firstName,isEnable
    }
}
`;

export const SEARCH_TELEGRAM_GROUP = gql`
query searchTelegramGroup(
    $page: Float!,
    $perPage: Float!,
    $search: String!,
){
    searchTelegramGroup(
        page: $page,
        perPage: $perPage,
        search: $search,
    ){
        id,name,type,isEnable,count,deviceCount,companyCount,departmentCount,userCount,receiverCount,notiTimesCount
    }
}
`;

export const GET_TELEGRAM_GROUP_BY_ID = gql`
query getTelegramGroupById(
    $groupId: String!,
){
    getTelegramGroupById(
        groupId: $groupId,
    ){
        id,name,type,isEnable,
        Devices{id,deviceName},
        Companies{id,name},
        Departments{id,name,company{id,name}},
        Users{id,name,email,avatar},
        Receivers{id,first_name,last_name},
        NotiTimes{id,name,startDate,endDate,startHour,startMin,startSec,endHour,endMin,endSec}
    }
}
`;

export const UPSERT_TELEGRAM_GROUP = gql`
mutation upsertTelegramGroup(
    $groupId: String!,
    $name: String!,
    $isEnable: Boolean!,
    $type: Float!,
) {
    upsertTelegramGroup (
        groupId: $groupId,
        name: $name,
        isEnable: $isEnable,
        type: $type,
    ){
        id,name,isEnable,type
    }
}
`;

export const SEARCH_TELEGRAM_RECEIVER_INFO = gql`
query getAllTelegramReceiverInfo(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    getAllTelegramReceiverInfo(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,is_bot,first_name,last_name,username,language_code,count
    }
}
`;

export const SET_RECEIVER_TO_TELEGRAM_GROUP = gql`
mutation setReceiverToTelegramGroup(
    $groupId: String!,
    $telegramIds: [String!]!,
    $isEnable: Boolean!,
){
    setReceiverToTelegramGroup(
        groupId: $groupId,
        telegramIds: $telegramIds,
        isEnable: $isEnable,
    )
}
`;

export const SET_USERS_TO_TELEGRAM_GROUP = gql`
mutation setUsersToTelegramGroup(
    $groupId: String!,
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $isEnable: Boolean!,
) {
    setUsersToTelegramGroup (
        groupId: $groupId,
        userIds: $userIds,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        isEnable: $isEnable,
    )
}
`;

export const SET_DEVICES_TO_TELEGRAM_GROUP = gql`
mutation setDeviceToTelegramGroup(
    $groupId: String!,
    $deviceIds: [String!]!,
    $isEnable: Boolean!,
) {
    setDeviceToTelegramGroup (
        groupId: $groupId,
        deviceIds: $deviceIds,
        isEnable: $isEnable,
    )
}
`;

export const GET_NOTI_TIME = gql`
query searchNotificationTime(
    $name: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchNotificationTime(
        name: $name
        page: $page
        perPage: $perPage
    ){
        id
        name
        startDate
        endDate
        startHour
        startMin
        startSec
        endHour
        endMin
        endSec
        count
    }
}
`;

export const UPSERT_NOTI_TIME = gql`
mutation upsertNotiTime(
    $id: String,
    $name: String!,
    $startDate: Date!,
    $endDate: Date!,
    $startHour: Float!,
    $startMin: Float!,
    $startSec: Float!,
    $endHour: Float!,
    $endMin: Float!,
    $endSec: Float!,
    $isClusterSync: Boolean!,
){
    upsertNotiTime(
        id:$id
        name: $name
        startDate: $startDate
        endDate: $endDate
        startHour: $startHour
        startMin: $startMin
        startSec: $startSec
        endHour: $endHour
        endMin: $endMin
        endSec: $endSec
        isClusterSync: $isClusterSync
    ){
        name
        startDate
        endDate
        startHour
        startMin
        startSec
        endHour
        endMin
        endSec
    }
}
`;

export const DELETE_NOTI_TIME = gql`
mutation deleteNotificationTime(
    $id:String!
){
    deleteNotificationTime(
        id: $id
    )
}
`;

export const UPSERT_NOTI_TIME_TO_GROUP = gql`
mutation setTimeToTelegramGroup(
    $groupId: String!,
    $timeIds: [String!]!,
    $isEnable: Boolean!,
){
    setTimeToTelegramGroup(
        groupId: $groupId,
        timeIds: $timeIds,
        isEnable: $isEnable,
    )
}
`;