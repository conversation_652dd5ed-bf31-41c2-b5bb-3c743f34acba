import { gql } from '@apollo/client';

export const SEARCH_FACE_INTERGRATION_CONFIG = gql`
query searchFaceIntegrationConfig(
    $search: String!,
    $page: Int!,
    $perPage: Int!,
    $types: [Int!]!,
){
    searchFaceIntegrationConfig(
        search: $search,
        page: $page,
        perPage: $perPage,
        types: $types
    ){
        endpointType,
        description,
        isActive,
        jsonConfig,
        count,
    }
}
`;

export const UPSERT_FACE_INTERGRATION_CONFIG = gql`
mutation upsertFaceIntegrationConfig(
    $endpointType: Int!,
    $description: String,
    $isActive: Boolean,
){
    upsertFaceIntegrationConfig(
        endpointType: $endpointType,
        description: $description,
        isActive: $isActive,
    )
} 
`;

export const UPSERT_SELF_CUSTOME_WEBHOOK = gql`
mutation upsertSelfCustomeService(
    $url: String!,
    $headerKeys: [String!]!,
    $headerVals: [String!]!,
    $dataKeys: [String!]!,
    $dataVals: [String!]!,
){
    upsertSelfCustomeService(
        url: $url,
        headerKeys: $headerKeys,
        headerVals: $headerVals,
        dataKeys: $dataKeys,
        dataVals: $dataVals,
    )
} 
`;

export const CONFIG_INTEGRATION_UNKNOWN = gql`
mutation configIntegrationUnknown(
    $isAlarmReIdOnly: Boolean!,
    $alarmDelayTime: Int!,
    $isDeviceFilter: Boolean!,
    $deviceFilter: [String!]!,
){
    configIntegrationUnknown(
        isAlarmReIdOnly: $isAlarmReIdOnly,
        alarmDelayTime: $alarmDelayTime,
        isDeviceFilter: $isDeviceFilter,
        deviceFilter: $deviceFilter
    ){
        endpointType, description, jsonConfig, isActive
    }
}
`;

export const ADD_DEVICE_FILTER_INTO_UNKNOWN_ALARM = gql`
mutation addDeviceFilterIntoUnknownAlarm(
    $deviceIds: [String!]!
){
    addDeviceFilterIntoUnknownAlarm(
        deviceIds: $deviceIds
    ){
        endpointType, description, jsonConfig, isActive
    }
}
`;

export const REMOVE_DEVICE_FILTER_INTO_UNKNOWN_ALARM = gql`
mutation removeDeviceFilterIntoUnknownAlarm(
    $deviceIds: [String!]!
){
    removeDeviceFilterIntoUnknownAlarm(
        deviceIds: $deviceIds
    ){
        endpointType, description, jsonConfig, isActive
    }
}
`;