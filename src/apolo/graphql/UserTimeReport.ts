import { gql } from "@apollo/client";

export const QUERY_USER_TIME_REPORT = gql`
query getUserTimeReports(
    $userIds: [String!]!, 
    $departmentIds: [String!]!, 
    $companyIds: [String!]!, 
    $startDate: String!,
    $endDate: String!,
    $page: Float!, 
    $perPage: Float!,
){
    getUserTimeReports(
        userIds: $userIds,
        departmentIds:$departmentIds,
        companyIds: $companyIds,
        startDate: $startDate,
        endDate: $endDate,
        page: $page,
        perPage: $perPage,
    ){
        count, userKey, userName, email, date, checkInTimes, checkOutTimes, unknownTimes, firstInString, lastOutString
    }
}
`;

export const QUERY_USER_TIME_REPORT_BY_RANGE = gql`
query queryUserTimeReportByRange(
    $startDate: String!,
    $endDate: String!,
    $userIds: [String!],
    $companyIds: [String!],
    $departmentIds: [String!],
    $deviceIds: [String!],
    $isSelectFullDay: Boolean,
) {
    queryUserTimeReportByRange(
        startDate : $startDate
        endDate : $endDate
        userIds : $userIds
        departmentIds : $departmentIds
        companyIds : $companyIds
        deviceIds : $deviceIds
        isSelectFullDay : $isSelectFullDay
    ) {
        userKey, 
        userName,
        email,
        Company{name}, Department{name},
        date, 
        checkInTimes, 
        checkOutTimes, 
        unknownTimes, 
        firstInString, 
        lastOutString
    }
}
`;

export const CREATE_USER_TIME_REPORT_MANNUALY = gql`
mutation createUserTimeReportMannualy(
    $startDate: String!,
    $endDate: String!
){
    createUserTimeReportMannualy(
        startDate: $startDate,
        endDate: $endDate
    )
}
`;

export const GET_REPORT_EMPTY_RESULT = gql`
query reportEmptyResultReportOnly(
    $startDate: String!,
    $endDate: String!,
    $userIds: [String!],
    $companyIds: [String!],
    $departmentIds: [String!],
    $deviceIds: [String!],
) {
    reportEmptyResultReportOnly(
        startDate : $startDate
        endDate : $endDate
        userIds : $userIds
        departmentIds : $departmentIds
        companyIds : $companyIds
        deviceIds : $deviceIds
    ) {
        userKey, userName, email, Company{name}, Department{name}, date, checkInTimes, checkOutTimes, unknownTimes, firstInString, lastOutString
    }
}
`;

export const GET_REPORT_FULL_RESULT = gql`
query reportFullDayUserTimeReport(
    $startDate: String!,
    $endDate: String!,
    $userIds: [String!],
    $companyIds: [String!],
    $departmentIds: [String!],
    $deviceIds: [String!],
) {
    reportFullDayUserTimeReport(
        startDate : $startDate
        endDate : $endDate
        userIds : $userIds
        departmentIds : $departmentIds
        companyIds : $companyIds
        deviceIds : $deviceIds
    ) {
        userKey, userName, email, Company{name}, Department{name}, date, checkInTimes, checkOutTimes, unknownTimes, firstInString, lastOutString
    }
}
`;