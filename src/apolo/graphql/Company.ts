import { gql } from "@apollo/client";

export const ADD_COMPANY = gql`
mutation AddCompany(
    $name: String!,
){createCompany
    (
        name: $name
    ){
        id,
        name
    }
}
`;


export const GET_ALL_COMPANY = gql`
query GetAllCompany(
    $name: String!,
){companies(
    name: $name,
)
    {
        id,
        name
    }
}
`;

export const SEARCH_COMPANY_PAGE = gql`
query searchCompanyPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){searchCompanyPage
    (
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        companyType,
        count
    }
}
`;

export const CURRENT_USER_COMPANY = gql`
query currentUserCom{currentUserCom{
        id,
        name
    }
}
`;

export const GET_COMPANY_BY_DEPARTMENT = gql`
query GetCompanyByDepartmentId(
    $departmentId: String!,
){companyByDepartment
    (
        departmentId: $departmentId
    ){
        id,
        name
    }
}
`;

export const GET_ALL_DEPARTMENT_IN_COMPANY = gql`
query GetDepartmentInCompany(
    $companyId: String!
){departmentsInCompany
    (
        companyId: $companyId
    ){
        id,
        parentId,
        name,
        company{
            id,
            name
        },
        numEmployees
    }
}
`;

export const GET_ALL_DEPARTMENT_IN_COMPANIES = gql`
query GetDepartmentInCompanies(
    $companyIds: [String!]!
){departmentsInCompanies
    (
        companyIds: $companyIds
    ){
        id,
        parentId,
        name
    }
}
`;

export const FIND_DEPARTMENTS = gql`
query findDepartments(
    $query: String!
    $companyIds: [String!]!
){findDepartments(
    query:$query
    companyIds: $companyIds
){
    id,
    name,
    parentId,
    company
    {
        id,
        name
    }
}
}
`;

export const SEARCH_ORGANIZATION_USER = gql`
query searchOrganizationUser(
    $organizationId: String!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $sort: Float,
    $startDate: String,
    $endDate: String,
){searchOrganizationUser
    (
        organizationId: $organizationId,
        search: $search,
        page: $page,
        perPage: $perPage,
        sort: $sort,
        startDate: $startDate,
        endDate: $endDate,
    ){
        User{
            id,
            name,
            avatar,
            email
        },
        totalInvalid,
        totalLateSecs,
        totalEarlySecs,
        totalWorkTime,
        totalWorkDay,
        minimalWorkTime
        count
    }
}
`;

export const GET_FIRST_ORDER_ORGANIZATION = gql`
query getFirstOrderOrganization
{
    getFirstOrderOrganization{
        id,
        name,
        parentId
    }
}
`;

export const GET_ORGANIZATION_BY_TEMPLATE = gql`
query getAllOrgTemplateOrgId(
    $templateId: String!,
)
{
    getAllOrgTemplateOrgId(
        templateId: $templateId,
    )
}
`;

export const GET_CHILD_ORGANIZATION_BY_ID = gql`
query getChilOrganiztionById(
    $organizationId: String!,
){
    getChilOrganiztionById(
        organizationId: $organizationId,
    ){
        id,
        name,
        parentId
    }
}
`;

export const GET_ALL_CHILD_ORGANIZATION_BY_ID = gql`
query getAllChildOrgById(
    $organizationId: String!,
){getAllChildOrgById
    (
        organizationId: $organizationId,
    ){
       id,name,totalUser
    }
}
`;

export const UPSERT_ORGANIZATION = gql`
mutation upsertOrganization(
    $organizationId: String,
    $organizationName: String!,
    $type: Float!,
    $parentId: String,
){upsertOrganization
    (
        organizationId: $organizationId,
        organizationName: $organizationName,
        type: $type,
        parentId: $parentId,
    ){
        id,name,type,parentId
    }
}
`;

export const UPSERT_ORGANIZATION_USER = gql`
mutation upsertOrganizationUsers(
    $userIds: [String!]!,
    $departmentIds: [String!]!,
    $companyIds: [String!]!,
    $organizationId: String!,
    $isEnable: Boolean!,
){upsertOrganizationUsers
    (
        userIds: $userIds,
        departmentIds: $departmentIds,
        companyIds: $companyIds,
        organizationId: $organizationId,
        isEnable: $isEnable,
    )
}
`;

export const GET_ORG_MASTER = gql`
query searchOrgMaster(
    $orgIds: [String!]!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){searchOrgMaster
    (
        orgIds: $orgIds,
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        User{id,name,email},
        Organization{id,name},
        count
    }
}
`;

export const UPSERT_ORGANIZATION_MASTERS = gql`
mutation upsertOrganizationMasters(
    $userIds: [String!]!,
    $organizationId: String!,
    $isEnable: Boolean!,
){upsertOrganizationMasters
    (
        userIds: $userIds,
        organizationId: $organizationId,
        isEnable: $isEnable,
    )
}
`;

export const DELETE_ORGANIZATION = gql`
mutation deleteOrganization(
    $organizationId: String!,
){
    deleteOrganization(
        organizationId: $organizationId,
    )
}
`;

export const EDIT_COMPANY_TYPE = gql`
mutation editCompany(
    $id: String!,
    $name: String!,
    $companyType: Int,
){
    editCompany(
        id: $id,
        name: $name,
        companyType: $companyType,
    ){
        id,name,companyType
    }
}
`;