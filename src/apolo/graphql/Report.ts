import { gql } from "@apollo/client";

export const GET_IN_OUT_BY_DAY = gql`
query getUserByDay(
    $startTime: Date!,
    $endTime: Date!,
    $timeZoneOffset: String!,
    ){
        getUserByDay(
            startTime: $startTime,
            endTime: $endTime,
            timeZoneOffset: $timeZoneOffset
        ){
            reportByDay{
                day,
                totalCheckInTimes,
                totalCheckOutTimes,
                totalUndefineTimes,
                userIds
            }
        }
}
`;

export const GET_IN_OUT_BY_HOUR = gql`
query getUserByHour(
    $day: Date!,
    $timeZoneOffset: String!,
    ){
        getUserByHour(
            day: $day,
            timeZoneOffset: $timeZoneOffset
        ){
            reportByDay{
                day,
                userIds,
                hoursData{
                    hour,
                    totalCheckInTimes,
                    totalCheckOutTimes,
                    totalUndefineTimes
                }
            }
        }
}
`;

export const GET_IN_OUT_BY_POSITION_BY_DAY = gql`
query getUserByPositionByDay(
    $userId: String!,
    $day: String!,
    $timeZoneOffset: String!,
    ){
        getUserByPositionByDay(
            userId: $userId,
            day: $day,
            timeZoneOffset: $timeZoneOffset
        ){
            reportByDay{
                day,
                positionData{
                    deviceId,
                    deviceName,
                    cameraIp,
                    checkInTimes,
                    checkOutTimes,
                    undefinedTimes
                }
            }
        }
}
`;


export const GET_DAILY_REPORT = gql`
query getUserReportByDay(
    $month: Date!,
    $timeZoneOffset: String!
){getUserReportByDay
    (
        month: $month,
        timeZoneOffset: $timeZoneOffset,
    ){
        id,
        timeZoneOffset,
        day,
        totalCheckIn,
        totalCheckOut,
        totalUndefine
    }
}
`;

export const GET_HOURLY_REPORT = gql`
query getUserReportByHour(
    $date: Date!,
    $timeZoneOffset: String!
){getUserReportByHour
    (
        date: $date,
        timeZoneOffset: $timeZoneOffset,
    ){
        id,
        timeZoneOffset,
        day,
        hour
        totalCheckIn,
        totalCheckOut,
        totalUndefine
    }
}
`;

export const GET_TOTAL_CURRENT_USER = gql`
query GetTotalCurrentUser {
    getTotalCurrentUser {
        in
        out
    }
}
`;

export const COUNT_ACTIVE_CAMERA = gql`
query countActiveCamera {countActiveCamera}
`;

export const COUNT_ACTIVE_ROI = gql`
query countActiveROI {countActiveROI}
`;

export const COUNT_ACTIVE_AISERVICE = gql`
query countActiveAiService {countActiveAiService}
`;

export const SUMMARY_ALARM_EVENT = gql`
query summaryAlarmEvent (
    $startDate: Date!,
    $endDate: Date!,
) {
    summaryAlarmEvent (
        startDate: $startDate,
        endDate: $endDate,
    ){
        type, count
    }
}
`;