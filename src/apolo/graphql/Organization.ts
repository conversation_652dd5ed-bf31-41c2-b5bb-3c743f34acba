import { gql } from "@apollo/client";

export const SEARCH_ORG_TEMPLATE = gql`
query findOrganizationTemplate(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){findOrganizationTemplate
    (
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        description,
        count,
}
}
`;

export const GET_ALL_ORG_ID_BY_ORG_TEMPLATE = gql`
query getAllOrgTemplateOrgId(
    $templateId: String!,
){getAllOrgTemplateOrgId
    (
        templateId: $templateId,
    ){
        id,
        name,
        description,
        count,
}
}
`;

export const REPORT_BY_ORG_TEMPLATE = gql`
query reportAllOrgTemplateOrgId(
    $templateId: String!,
    $startDate: Date!,
    $endDate: Date!,
) {reportAllOrgTemplateOrgId (
    templateId: $templateId,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,maxInvalid,totalInvalid,totalLateSecs,totalEarlySecs,minWorkTime,totalWorkTime
}}
`;

export const REPORT_WORK_TIME_BY_ORG_TEMPLATE = gql`
query reportAllOrgTemplateOrgIdWorkTime(
    $templateId: String!,
    $startDate: Date!,
    $endDate: Date!,
) {reportAllOrgTemplateOrgIdWorkTime (
    templateId: $templateId,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,maxInvalid,totalInvalid,totalLateSecs,totalEarlySecs,minWorkTime,totalWorkTime
}}
`;

export const UPSERT_ORG_TEMPLATE = gql`
mutation upsertOrgTemplate(
    $templateId: String,
    $name: String!,
    $description: String,
    $orgIds: [String!]!,
    $isAdd: Boolean!,
){upsertOrgTemplate
    (
        templateId: $templateId,
        name: $name,
        description: $description,
        orgIds: $orgIds,
        isAdd: $isAdd,
    )
}
`;

export const REMOVE_OGANIZATION_TEMPLATE = gql`
mutation deleteOrgTemplate(
    $templateIds: [String!]!,
) {
    deleteOrgTemplate (
        templateIds: $templateIds,
    )
}
`;

export const GET_ALL_ORG_USER = gql`
query getAllOrgByUserId(
    $userId: String!,
    $page: Float!,
    $perPage: Float!,
){
    getAllOrgByUserId(
        userId: $userId,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        parentId,
        type,
        count
    }
}
`;

export const GET_ORG_USER_INFOS = gql`
query getOrgUserInfos(
    $organizationId: String!,
){getOrgUserInfos
    (
        organizationId: $organizationId,
    ){
          id,totalUser,Masters{id,avatar,name,email},Approvers{id,avatar,name,email}
    }
}
`;

export const ADD_COMPANY_TO_ORG = gql`
mutation addCompanyToOrg(
    $organizationId: String!,
    $companyId: [String!]
) {
    addCompanyToOrg(
        organizationId: $organizationId,
        companyId: $companyId
    )
}
`;

export const ADD_DEPARTMENT_TO_ORG = gql`
mutation addDepartmentToOrg(
    $organizationId: String!,
    $departmentId: [String!]
) {
    addDepartmentToOrg(
        organizationId: $organizationId,
        departmentId: $departmentId
    )
}
`;