import { gql } from "@apollo/client";

export const REPORT_ORG_BY_ID = gql`
query reportOrganiztionById(
    $organizationId: String!,
    $startDate: Date!,
    $endDate: Date!,
) {reportOrganiztionById (
    organizationId: $organizationId,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,maxInvalid,totalInvalid,totalLateSecs,totalEarlySecs,minWorkTime,totalWorkTime
}}
`;

export const REPORT_ORG_BY_MULTIPLE_ID = gql`
query reportOrganiztionByMultipleId(
    $organizationIds: [String!]!,
    $startDate: Date!,
    $endDate: Date!,
) {reportOrganiztionByMultipleId (
    organizationIds: $organizationIds,
    startDate: $startDate,
    endDate: $endDate,
){
    id,name,totalUser,maxInvalid,totalInvalid,totalLateSecs,totalEarlySecs,minWorkTime,totalWorkTime
}}
`;

export const SEARCH_RANK_USER = gql`
query searchRankUser(
    $userRank: Float!,
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $sort: Float,
    $startDate: String,
    $endDate: String,
    $organizationIds: [String!]!,
){searchRankUser
    (
        userRank: $userRank,
        search: $search,
        page: $page,
        perPage: $perPage,
        sort: $sort,
        startDate: $startDate,
        endDate: $endDate,
        organizationIds: $organizationIds,
    ){
        User{id,name,email},
        maxInvalid,
        totalInvalid,
        totalLateSecs,
        totalEarlySecs,
        totalWorkTime,
        count
    }
}
`;

export const ALL_SHIFT_USER_REPORT = gql`
query allShiftUserReport(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $startDate: String,
    $endDate: String,
){allShiftUserReport
    (
        search: $search,
        page: $page,
        perPage: $perPage,
        startDate: $startDate,
        endDate: $endDate,
    ){
        User{id,name,email},
        maxInvalid,
        totalInvalid,
        totalLateSecs,
        totalEarlySecs,
        totalWorkTime,
        count
    }
}
`;

export const GET_ORGANIZATION_PAGE = gql`
query getOrganizationPage(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){getOrganizationPage
    (
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        id,
        name,
        parentId,
        type,
        count
    }
}
`;

export const GET_REPORT_ORGANIZATION_BY_TYPE = gql`
query reportOrganiztionByType(
    $search: String!,
    $orgLevel: Float!,
    $startDate: Date!,
    $endDate: Date!,
    $orgIds: [String!],
) {reportOrganiztionByType (
    search: $search,
    orgLevel: $orgLevel,
    startDate: $startDate,
    endDate: $endDate,
    orgIds: $orgIds,
    ){
        id,
        name,
        totalUser,
        maxInvalid,
        totalInvalid,
        totalLateSecs,
        totalEarlySecs,
        minWorkTime,
        totalWorkTime
    }
}
`;