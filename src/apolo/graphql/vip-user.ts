import { gql } from "@apollo/client";

export const SEARCH_VIP_USER = gql`
query searchVipUser(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
    $companyIds: [String!]!,
    $departmentIds: [String!]!,
    $status: Float,
){
    searchVipUser(
        search: $search,
        page: $page,
        perPage: $perPage,
        companyIds: $companyIds,
        departmentIds: $departmentIds,
        status: $status
    ){
        id,
        email,
        fullName,
        integrationKey,
        avatar,
        Company{name, id},
        Department{name, id},
        status,
        count
    }
}
`;

export const ADD_VIP_USER = gql`
mutation toggleVipUser(   
	$userIds: [String!]!,
    $isVip: Boolean!
){
	toggleVipUser(        
		userIds: $userIds
		isVip: $isVip
    )
}
`;

export const DELETE_VIP_USER = gql`
mutation toggleVipUser(
    $userIds: [String!]!,
    $isVip: Boolean!
){
    toggleVipUser(
        userIds: $userIds,
        isVip: $isVip
    )
}
`;