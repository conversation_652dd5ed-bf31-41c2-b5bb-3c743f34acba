import { gql } from "@apollo/client";

export const GET_FACE_TRACK_PAGE = gql`
query getFaceTrackPage(
    $startTime: Date!,
    $endTime: Date!,
    $page: Float!,
    $perPage: Float!,
    $type: Float!,
    $deviceIds: [String!]!,
    $cameraIds: [String!]!,
    $textSearch: String!,
    $isShowUnknownResult: Boolean!,
){
    getFaceTrackPage(
        startTime: $startTime,
        endTime: $endTime,
        page: $page,
        perPage: $perPage,
        type: $type,
        deviceIds: $deviceIds,
        cameraIds: $cameraIds,
        textSearch: $textSearch,
        isShowUnknownResult: $isShowUnknownResult,
    ){
        trackId,
        dateCreated,
        Device{
            deviceName,
            id
        },
        FaceTrackAtt{
            avgEmotion,
            avgEmotionConfident,
            gender,
            genderConfident,
            ageFrom,
            ageTo,
            ageConfident,
            FaceAttributeResults{
                emotion,
                emotionConfident
            },User{
                name,companyName,departmentName,avatarBase64{data}
            }
        }
        count
    }
}
`;

export const GET_ALL_FACE_RESULT_IN_TRACK = gql`
query getAllFaceResultInTrack(
    $startTime: Date,
    $endTime: Date,
    $deviceId: String!, 
    $trackId: String!,
    $page: Int!,
    $perPage: Int!,
){
    getAllFaceResultInTrack(
        startTime:$startTime,
        endTime:$endTime,
        deviceId:$deviceId,
        trackId:$trackId,
        page:$page,
        perPage:$perPage,
    ){
        id,
        trackId,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        SrcDevice{id,name},
        DebugInfos{
            detectDuration,
            vectorDuration,
            headposePitch,
            headposeRoll,
            headposeYaw,
            faceQualityScore,
            recogConfident,
            reqTimePoint,
            reqExDuration,
            searchDuration,
            reIdConf,
            maskConfident,
            maskDuration,
            headposeDuration,
            qualityDuration,
            detectTimepoint,
        },
        count
    }
}
`;

export const GET_ALL_FACE_RESULT_IN_REID = gql`
query getAllFaceResultInReId(
    $startTime: Date,
    $endTime: Date,
    $deviceId: String!, 
    $trackId: String!,
    $page: Int!,
    $perPage: Int!,
){
    getAllFaceResultInReId(
        startTime:$startTime,
        endTime:$endTime,
        deviceId:$deviceId,
        trackId:$trackId,
        page:$page,
        perPage:$perPage,
    ){
        id,
        reId,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        SrcDevice{id,name},
        DebugInfos{
            detectDuration,
            vectorDuration,
            headposePitch,
            headposeRoll,
            headposeYaw,
            faceQualityScore,
            recogConfident,
            reqTimePoint,
            reqExDuration,
            searchDuration,
            reIdConf,
            maskConfident,
            maskDuration,
            headposeDuration,
            qualityDuration,
            detectTimepoint,
        },
        count
    }
}
`;

export const GET_ALL_FACE_RESULT_IN_CACHED = gql`
query getAllFaceResultInCached(
    $time: Date,
    $deviceId: String!, 
    $trackId: String!,
    $page: Int!,
    $perPage: Int!,
){
    getAllFaceResultInCached(
        time:$time,
        deviceId:$deviceId,
        trackId:$trackId,
        page:$page,
        perPage:$perPage,
    ){
        cameraId,
        time,
        image,
        trackingId,
        deviceId,
        count
    }
}
`;

export const GET_FACE_RESULT_IN_TRACK = gql`
query getFaceResultInTrack(
    $deviceId: String!, 
    $trackId: String!,
){
    getFaceResultInTrack(
        deviceId:$deviceId,
        trackId:$trackId,
    ){
        id,
        cameraIp,
        integrationKey,
        deviceId,
        image,
        fullName,
        company,
        department,
        time,
        type,
        count,
        SrcDevice{id,name}
    }
}
`;

export const GET_ORPHAN_RESULT_IN_TRACK = gql`
query getOrphanResultInTrack(
    $deviceId: String!, 
    $trackId: String!,
    $page: Float!,
    $perPage: Float!,
){
    getOrphanResultInTrack(
        deviceId:$deviceId,
        trackId:$trackId,
        page:$page,
        perPage:$perPage,
    ){
        image,count
    }
}
`;

export const REPORT_ALL_EMOTION = gql`
query reportAllEmotion(
    $startTime: Date!,
    $endTime: Date!,
    $deviceIds: [String!],
){
    reportAllEmotion(
        startTime:$startTime,
        endTime:$endTime,
        deviceIds:$deviceIds,
    ){
        startTime,
        endTime,
        deviceId,
        avgPositive,
        positiveGt80,
        positiveGt60Le80,
        positiveGt40Le60,
        positiveGt20Le40,
        positiveLe20,
        avgNeutral,
        neutralGt80,
        neutralGt60Le80,
        neutralGt40Le60,
        neutralGt20Le40,
        neutralLe20,
        avgNegative,
        negativeGt80,
        negativeGt60Le80,
        negativeGt40Le60,
        negativeGt20Le40,
        negativeLe20,
        count
    }
}
`;

export const REPORT_ALL_AGE = gql`
query reportAllAge(
    $startTime: Date!,
    $endTime: Date!,
    $deviceIds: [String!],
){
    reportAllAge(
        startTime:$startTime,
        endTime:$endTime,
        deviceIds:$deviceIds,
    ){
        startTime,
        endTime,
        deviceId,
        step,
        countSameAge,
        count
    }
}
`;

export const SEARCH_REID_LABEL = gql`
query searchReIdLabel(
    $search: String!,
    $page: Float!,
    $perPage: Float!,
){
    searchReIdLabel(
        search: $search,
        page: $page,
        perPage: $perPage,
    ){
        reid, label, 
        UserGql{
            id,name,email,integrationKey,avatar,Company{id,name},Department{id,name}
        } count
    }
}
`;

export const SET_REID_LABEL = gql`
mutation setReIdLabel(
    $reId: String!,
    $label: String,
    $userId: String
){
    setReIdLabel(
        reId: $reId,
        label: $label,
        userId: $userId
    ){
        reid, label, userId
    }
}
`;

export const REMOVE_REID_LABEL = gql`
mutation removeReIdLabel(
    $reIds: [String!]!,
){
    removeReIdLabel(
        reIds: $reIds,
    )
}
`;