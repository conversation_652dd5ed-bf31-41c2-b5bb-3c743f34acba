import { gql } from "@apollo/client";

export const RESCAN_USER = gql`
mutation ReScanUserFaceImages(
    $companyIds: [String!]!, 
    $departmentIds: [String!]!, 
    $userIds: [String!]!
){
    reScanUserFaceImages(
      companyIds: $companyIds, 
      departmentIds: $departmentIds,  
      userIds: $userIds
    )
}
`;

export const GET_ALL_SCAN = gql`
query GetAllScanJob(
  $page: Float!, 
  $perPage: Float!
){
  getAllScanJob(
    page: $page, 
    perPage: $perPage
    ){
      id
      totalImage
      dateCreated
      count
      totalUser
      isFinished
      error
  }
}
`;

export const GET_ID_CARD_BY_USER_ID = gql`
query getIdCardByUserId(
    $user_id: String!

){
    getIdCardByUserId(
        user_id: $user_id
    ){
        id
        serialNumber
        type
        userId
        isActive
        expiredDate
        count
    }
}
`;

export const GET_FINGERPRINT_BY_USER_ID = gql`
query getFingerPrintByUserId(
    $id: String!
){
    getFingerPrintByUserId(
        id: $id
    ){
        id
        data
        type
        userId
        studentId
        image
        isActive
        expiredDate
        isClusterSync
        count
    }
  }
`;

export const CHANGE_CARD_STATUS = gql`
mutation changeCardStatus(
    $ids: [String!],
    $sns: [String!],
    $isActive: Boolean!,
){
    changeCardStatus(
        ids: $ids,
        sns: $sns,
        isActive: $isActive,
    )
}
`;

export const CHANGE_FINGERPRINT_STATUS = gql`
mutation changeFingerStatus(
    $ids: [String!],
    $isActive: Boolean!,
){
    changeFingerStatus(
        ids: $ids,
        isActive: $isActive,
    )
}
`;