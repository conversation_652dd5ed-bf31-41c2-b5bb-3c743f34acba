import { gql } from "@apollo/client";

export const SEARCH_AC_ADMINS = gql`
query AcAdmins(
    $locationId: String!
) {acAdmins (
    locationId: $locationId
){
    userId, locationId, userName, avatar
}}
`;

export const ADD_AC_ADMINS = gql`
mutation AddAcAdmins(
    $locationIds: [String!]!,
    $userIds: [String!]!,
) {addACAdmins (
    locationIds: $locationIds,
    userIds: $userIds
){
    userId, locationId, userName, avatar
}}
`;

export const DELETE_AC_ADMINS = gql`
mutation DeleteAcAdmin(
    $locationIds: [String!]!,
    $userIds: [String!]!,
) {deleteACAdmins (
    locationIds: $locationIds,
    userIds: $userIds
)}
`;