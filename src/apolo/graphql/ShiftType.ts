import { gql } from "@apollo/client";

export const SEARCH_DEFAULT_SHIFT_TYPE = gql`
query searchDefaultShiftType(
    $search: String!,
    $page: Float!,
    $perPage: Float!
){searchDefaultShiftType
    (
        search: $search,
        page: $page,
        perPage: $perPage,
    ){id,Shift{code,startHour,endHour},count}
}
`;

export const GET_DEFAULT_SHIFT_TYPE = gql`
query SearchDefaultShiftType(
    $page: Float!, 
    $perPage: Float!, 
    $search: String!,
    $viewMode: Float,
    $shiftId: [String!],
) {
    searchDefaultShiftType(
        page: $page, 
        perPage: $perPage, 
        search: $search,
        viewMode: $viewMode,
        shiftId: $shiftId,
    ){
        id,
        shiftId,
        userId,
        companyId,
        departmentId,
        Shift {
            code,
            startHour,
            endHour
        },
        Company {
            id,
            name
        },
        Department {
            id,
            name
        },
        User {
            id,
            name,
            avatar,
            email
        },
        count
    }
  }
`;

export const GET_ALL_TABLES = gql`
query Query {
    getAllBackupTables
}
`;

export const UPDATE_DEFAULT_SHIFT_TYPE = gql`
mutation UpsertDefaultShiftType(
    $shiftId: String!, 
    $userId: String, 
    $departmentId: String, 
    $defaultShiftId: String, 
    $companyId: String
){
    upsertDefaultShiftType(
        shiftId: $shiftId, 
        userId: $userId, 
        departmentId: $departmentId, 
        defaultShiftId: $defaultShiftId, 
        companyId: $companyId
    ){
        id
    }
}
`;

export const DELETE_DEFAULT_SHIFT_TYPE = gql`
mutation DeleteDefaultShiftType(
    $defaultShiftId: String!
){
    deleteDefaultShiftType(
        defaultShiftId: $defaultShiftId
    )
}
`;