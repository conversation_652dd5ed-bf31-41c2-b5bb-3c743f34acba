import { gql } from "@apollo/client";

export const CREATE_FACE_SEARCH_EVENT = gql`
mutation createfaceMatchingEvent(
    $faceImage: String!,
    $startDate: Date!,
    $endDate: Date!,
    $eventId: String!,
    $delay: Float!
)
{createfaceMatchingEvent
    (
        faceImage:$faceImage,
        startDate:$startDate,
        endDate:$endDate,
        eventId: $eventId,
        delay: $delay
    )
}
`;

export const FACE_SEARCH_EVENT_PAGE = gql`
query faceMatchingEventPage(
    $page: Float!,
    $perPage: Float!
){faceMatchingEventPage
    (
        page:$page,
        perPage:$perPage
    ){
        id,srcFaceImageBase64,totalImageSearch,imageSearched,isFinished,startSearchDate,endSearchDate,topResult{confident,unknownEvent{image,time}}
    }
}
`;

export const FACE_SEARCH_RESULT_PAGE = gql`
query checkFaceMatchingResult(
    $eventId: String!,
    $limit: Float!
)
{checkFaceMatchingResult
    (
        eventId: $eventId,
        limit: $limit
    ){confident,unknownEvent{time, image}}
}
`;
