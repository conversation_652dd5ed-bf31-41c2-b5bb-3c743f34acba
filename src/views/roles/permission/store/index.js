import { createSlice } from '@reduxjs/toolkit';

const initState = () => {

    return {
        search: "",
        page: 1,
        perPage: 10,
        count: 0,
        loading: false,
    }
};

export const permissionSlice = createSlice({
    name: 'permission',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleSearch: (state, action) => {
            state.search = action.payload
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleCount: (state, action) => {
            state.count = action.payload
        },
        handleLoading: (state, action) => {
            state.loading = action.payload
        },
    },
})

export const {
    handleInit,
    handleSearch,
    handlePage,
    handlePerPage,
    handleCount,
    handleLoading
} = permissionSlice.actions

export default permissionSlice.reducer;