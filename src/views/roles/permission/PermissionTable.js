import { Fragment, useState, useEffect } from 'react';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import ReactPaginate from 'react-paginate';
import { Trash2, Check, X } from 'react-feather';
import { Card, Input, Label, Button, ButtonGroup } from 'reactstrap';
import Breadcrumbs from '@components/breadcrumbs';
import Select from "react-select";
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import Spinner from '@components/spinner/Loading-spinner';
import { useQuery, useMutation } from '@apollo/client';
import { LIST_PERMISSIONS, EDIT_PERMISSION, DEL_PERMISSION } from '../../../apolo/graphql/Permission';
import { toast } from 'react-hot-toast';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { handleCount, handlePage } from './store';
import UserRoleTypeConstants from '@src/constants/UserRoleTypeConstants';
import PermissionTableFilter from './PermissionTableFilter';

const PermissionTable = () => {

    //#region states
    const UserRoleType = UserRoleTypeConstants.UserRoleType;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.permission.search);
    let page = useSelector(state => state.permission.page);
    let perPage = useSelector(state => state.permission.perPage);
    let count = useSelector(state => state.permission.count);
    let loading = useSelector(state => state.permission.loading);
    const [edit, setEdit] = useState(undefined);
    const [change, setChange] = useState(undefined);
    const MySwal = withReactContent(Swal);
    //#endregion

    //#region query permission
    const [permissionList, setPermissions] = useState([]);
    const [filteredPermissions, setFilteredPermissions] = useState([]);
    const getPermissions = useQuery(LIST_PERMISSIONS, {
        fetchPolicy: 'cache-and-network',
        nextFetchPolicy: 'cache-and-network'
    });

    useEffect(() => {
        if (getPermissions.loading) return;
        if (!getPermissions.data) return;
        setPermissions(getPermissions?.data?.listPermissions);
        setFilteredPermissions(getPermissions?.data?.listPermissions);
        dispatch(handleCount(getPermissions?.data?.listPermissions?.length));
    }, [getPermissions]);

    useEffect(() => {
        if (!permissionList.length) return;
        const results = permissionList.filter(item => 
            item.functionId.toLowerCase().includes(search.toLowerCase())
        );
        setFilteredPermissions(results);
        dispatch(handleCount(results.length));
    }, [search, permissionList]);
    //#endregion

    //#region column
    const columm = [
        {
            name: t('Function'),
            sortable: true,
            center: true,
            sortField: 'functionId',
            minWidth: '250px',
            selector: row => row['functionId'],
            cell: row => <p className="fw-bolder">{row.functionId}</p>
        },
        {
            name: t('Role'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '200px',
            selector: row => row['id'],
            cell: row => (
                <Select
                    id={`roleId-${row.id}`}
                    instanceId={`roleId-${row.id}`}
                    className='react-select w-100'
                    classNamePrefix='select'
                    theme={selectThemeColors}
                    isMulti={false}
                    isClearable={false}
                    options={Object.keys(UserRoleType).slice(1).map((key, index) => {
                        return {
                            value: UserRoleType[key],
                            label: t(UserRoleType[key])
                        }
                    })}
                    value={JSON.stringify(edit) !== JSON.stringify(row) || change === undefined ?
                        {
                            value: row.roleId,
                            label: t(UserRoleType[row.roleId])
                        } :
                        {
                            value: change.newRoleId,
                            label: t(UserRoleType[change.newRoleId])
                        }
                    }
                    onChange={(event) => {
                        setChange({
                            ...change,
                            newRoleId: event.value
                        })
                    }}
                    isDisabled={JSON.stringify(edit) !== JSON.stringify(row)}
                />
            )
        },
        {
            name: t('Create'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row['id'],
            cell: row => (
                <input
                    type="checkbox"
                    checked={JSON.stringify(edit) !== JSON.stringify(row) || change === undefined ? row.canCread : change.canCread}
                    style={{ transform: "scale(1.5)" }}
                    disabled={JSON.stringify(edit) !== JSON.stringify(row)}
                    onChange={(event) => {
                        setChange({
                            ...change,
                            canCread: !change.canCread
                        })
                    }}
                />
            )
        },
        {
            name: t('Read'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row['id'],
            cell: row => (
                <input
                    type="checkbox"
                    checked={JSON.stringify(edit) !== JSON.stringify(row) || change === undefined ? row.canRead : change.canRead}
                    style={{ transform: "scale(1.5)" }}
                    disabled={JSON.stringify(edit) !== JSON.stringify(row)}
                    onChange={(event) => {
                        setChange({
                            ...change,
                            canRead: !change.canRead
                        })
                    }}
                />
            )
        },
        {
            name: t('Update'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row['id'],
            cell: row => (
                <input
                    type="checkbox"
                    checked={JSON.stringify(edit) !== JSON.stringify(row) || change === undefined ? row.canUpdate : change.canUpdate}
                    style={{ transform: "scale(1.5)" }}
                    disabled={JSON.stringify(edit) !== JSON.stringify(row)}
                    onChange={(event) => {
                        setChange({
                            ...change,
                            canUpdate: !change.canUpdate
                        })
                    }}
                />
            )
        },
        {
            name: t('Delete'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row['id'],
            cell: row => (
                <input
                    type="checkbox"
                    checked={JSON.stringify(edit) !== JSON.stringify(row) || change === undefined ? row.canDelete : change.canDelete}
                    style={{ transform: "scale(1.5)" }}
                    disabled={JSON.stringify(edit) !== JSON.stringify(row)}
                    onChange={(event) => {
                        setChange({
                            ...change,
                            canDelete: !change.canDelete
                        })
                    }}
                />
            )
        },
        {
            name: t('Edit'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row['id'],
            cell: row => {
                if (edit === undefined || JSON.stringify(edit) !== JSON.stringify(row)) {
                    return <div className='form-switch'>
                        <Input
                            type='switch'
                            id={`edit-switch-${row.id}`}
                            name={`edit-switch-${row.id}`}
                            checked={JSON.stringify(edit) === JSON.stringify(row)}
                            onChange={() => {
                                if (edit === undefined) {
                                    setEdit(row)
                                    setChange({
                                        ...row,
                                        newRoleId: row.roleId
                                    })
                                } else if (edit === row) {
                                    setEdit(undefined)
                                    setChange(undefined)
                                } else {
                                    setEdit(row)
                                    setChange({
                                        ...row,
                                        newRoleId: row.roleId
                                    })
                                }
                            }}
                        />
                        <Label className='form-check-label' htmlFor={`edit-switch-${row.id}`}>
                            <span className='switch-icon-left'>
                                <Check size={14} />
                            </span>
                            <span className='switch-icon-right'>
                                <X size={14} />
                            </span>
                        </Label>
                    </div>
                } else {
                    return <div className='d-flex align-items-center'>
                        <ButtonGroup>
                            <Button size='sm' color='primary' onClick={() => handleSubmit()}>
                                <Check size={17} />
                            </Button>
                            <Button size='sm' color='danger' onClick={() => {
                                setEdit(undefined)
                                setChange(undefined)
                            }}>
                                <X size={17} />
                            </Button>
                        </ButtonGroup>
                    </div>
                }
            },
        },
        {
            name: t('Remove'),
            sortable: true,
            center: true,
            sortField: 'id',
            minWidth: '100px',
            selector: row => row.id,
            cell: row => <Trash2 className='cursor-pointer' size={17} onClick={() => handleDelete(row.functionId, row.roleId)} />
        },
    ];
    //#endregion

    //#region handle submit
    const [editPermission, { }] = useMutation(EDIT_PERMISSION);
    const [delPermission, { }] = useMutation(DEL_PERMISSION);
    const handleSubmit = async () => {
        try {
            const req = await editPermission({
                variables: {
                    functionId: change.functionId,
                    oldRoleId: change.roleId,
                    newRoleId: change.newRoleId,
                    create: change.canCread,
                    read: change.canRead,
                    update: change.canUpdate,
                    del: change.canDelete
                }
            })
            setEdit(undefined)
            setChange(undefined)
            toast.success(t('Updated Successfully'));
            getPermissions.refetch();
        }
        catch (e) {
            toast.error(e.graphQLErrors[0]?.message || e.message);
        }
    };
    //#endregion

    //#region handle delete
    const handleDelete = async (functionId, roleId) => {
        const result = await MySwal.fire({
            title: t('Remove this permission?'),
            text: t("You won't be able to revert this!"),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: t('Yes, delete it!'),
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            await delPermission({
                variables: {
                    functionId: functionId,
                    roleId: roleId,
                }
            }).then(() => {
                let newPage = page;
                if ((page - 1) * perPage >= count - 1) {
                    newPage = page - 1;
                }
                if (newPage < 1) newPage = 1;
                dispatch(handlePage(newPage));
                getPermissions.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: t('Deleted!'),
                    text: t('Your permission has been deleted.'),
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message || e.message);
            });
        }
    };
    //#endregion

    //#region custom pagination
    const indexOfLastItem = page * perPage;
    const indexOfFirstItem = indexOfLastItem - perPage;
    const finalPermissionList = filteredPermissions?.slice(indexOfFirstItem, indexOfLastItem);
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage)}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <Fragment>
            <Breadcrumbs title={t('Permission')} data={[{ title: t('Admin') }, { title: t('Permission') }]} />
            <Card>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columm}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={finalPermissionList}
                        subHeaderComponent={<PermissionTableFilter />}
                        highlightOnHover
                        pointerOnHover
                        progressPending={loading}
                        progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
                    />
                </div>
            </Card>
        </Fragment>
    )
};

export default PermissionTable;
