import { Fragment, useEffect, useState } from "react";
import { useQuery, useLazyQuery } from "@apollo/client";
import { GET_ALL_ATTENDANT_TIME, GET_ALL_DEVICE_GROUP_INFO, QUERY_USER_ATTENDANT_TIME_HISTORY } from "../../../apolo/graphql/Attendant";
import { SEARCH_ALL_DEVICE } from "../../../apolo/graphql/Device";
import { SEARCH_USER_NEW } from "../../../apolo/graphql/User";
import { useTranslation } from "react-i18next";
import { Card, CardBody, CardHeader, CardTitle, Col, Label, Row } from "reactstrap";
import moment from "moment-timezone";
import { toast } from "react-hot-toast";
import axios from "axios";
import DataTable from "react-data-table-component";
import '@styles/react/libs/tables/react-dataTable-component.scss';
import ReactPaginate from "react-paginate";
import Breadcrumbs from '@components/breadcrumbs';
import Select from "react-select";
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import CustomHeader from "./HistoryTableFilter";
import UserStatusConstants from "@src/constants/UserStatusConstants";
import DevicesConstants from "@src/constants/DevicesConstants";

const HistoryTable = () => {

    const UserStatus = UserStatusConstants.UserStatus;
    const DeviceStatus = DevicesConstants.DeviceStatus;
    //User Select
    const [page, setPage] = useState(1);
    const [perPageSearch, setPerPageSearch] = useState(10);
    const [textSearch, setTextSearch] = useState("");
    const [userList, setUser] = useState([]);
    const [count, setCount] = useState(0);
    const [userIds, setUserIds] = useState([]);

    //Device Select
    const [deviceTypes, setDeviceType] = useState([]);
    const [devices, setDevices] = useState([]);

    //Shift
    const [shifts, setShifts] = useState([]);

    const today = new Date();
    const [startSearchDate, onStartSearchChange] = useState(new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0));
    const [endSearchDate, onEndSearchChange] = useState(new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59));

    const allDevices = useQuery(SEARCH_ALL_DEVICE, {
        variables: {
            deviceTypes: deviceTypes,
            deviceStatus: DeviceStatus.Active,
            deviceUpdateStatus: DeviceStatus.All,
            search: ""
        }
    });

    const deviceGroups = useQuery(GET_ALL_DEVICE_GROUP_INFO, {
        variables: {
        }
    });

    const [query, userQuery] = useLazyQuery(SEARCH_USER_NEW, {
        variables: {
            "input": {
                "query": textSearch,
                "pageSize": perPageSearch,
                "pageIndex": page,
                "departmentIds": [],
                "companyIds": [],
                "status": UserStatus.Active,

            }
        },
    });

    useEffect(() => {
        query({
            variables: {
                input: {
                    "query": textSearch,
                    "pageSize": perPageSearch,
                    "pageIndex": page,
                    "departmentIds": [],
                    "companyIds": [],
                    "status": UserStatus.Active
                }
            }
        });
    }, [page, perPageSearch, textSearch]);

    useEffect(() => {
        if (userQuery.loading) return;
        if (!userQuery.data) return;
        setUser(userQuery?.data?.search)
        setCount(userQuery?.data?.search[0]?.count)
    }, [userQuery]);

    const getAllAttendantTime = useQuery(GET_ALL_ATTENDANT_TIME);

    const [tablePage, setTablePage] = useState(1);
    const [tablePerPageSearch, setTablePerPageSearch] = useState(10);
    const [userAttData, setUserAttData] = useState([]);
    const [userAttCount, setUserAttCount] = useState(0);

    const userAttendantTime = useQuery(QUERY_USER_ATTENDANT_TIME_HISTORY, {
        variables: {
            attendantIds: shifts.map(el => el.value),
            deviceGroupIds: devices.filter(el => !el.device).map(de => de.value),
            deviceIds: devices.filter(el => el.device).map(de => de.value),
            page: tablePage,
            perPage: tablePerPageSearch,
            userIds: userIds,
            startDate: moment(startSearchDate).format(`YYYY-MM-DD`),
            endDate: moment(endSearchDate).format(`YYYY-MM-DD`),
        },
    });

    useEffect(() => {
        if (userAttendantTime.loading) return;
        if (!userAttendantTime.data) return;
        setUserAttData(userAttendantTime?.data?.queryUserAttendantTimeByPageRealTime);
        setUserAttCount(userAttendantTime?.data?.queryUserAttendantTimeByPageRealTime[0]?.count);
    }, [userAttendantTime]);

    const exportReport = async () => {
        let params = {
            attendantIds: shifts.map(el => el.value),
            deviceGroupIds: devices.filter(el => !el.device).map(de => de.value),
            deviceIds: devices.filter(el => el.device).map(de => de.value),
            userIds: userIds,
            startDate: moment(new Date(startSearchDate)).format(`YYYY-MM-DDT00:00:00`).toString(),
            endDate: moment(new Date(endSearchDate)).format(`YYYY-MM-DDT23:59:59`).toString(),
        }
        try {
            let result = await axios({
                method: 'get',
                headers: {
                    // Authorization: cookies.getAccessToken(),
                    'Content-Type': 'multipart/form-data'
                },
                url: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/attandant?userIds=${params.userIds}&deviceIds=${params.deviceIds}&deviceGroupIds=${params.deviceGroupIds}&attendantIds=${params.attendantIds}&startDate=${params.startDate}&endDate=${params.endDate}`,
            })
            const linkSource = `data:${"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};base64,${result.data}`;
            const downloadLink = document.createElement("a");
            downloadLink.href = linkSource;
            downloadLink.download = "report.xlsx";
            downloadLink.click();
        }
        catch (e) {
            toast.error(e?.response?.data?.message);
        }
    }

    const exportByDeps = async () => {
        let params = {
            attendantIds: shifts.map(el => el.value),
            startDate: moment(new Date(startSearchDate)).format(`YYYY-MM-DDT00:00:00`).toString(),
            endDate: moment(new Date(endSearchDate)).format(`YYYY-MM-DDT23:59:59`).toString(),
        }
        try {
            let result = await axios({
                method: 'get',
                headers: {
                    // Authorization: cookies.getAccessToken(),
                    'Content-Type': 'multipart/form-data'
                },
                url: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/attandant/countAttandantByDay?attendantIds=${params.attendantIds}&startDate=${params.startDate}&endDate=${params.endDate}`,
            })
            const linkSource = `data:${"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};base64,${result.data}`;
            const downloadLink = document.createElement("a");
            downloadLink.href = linkSource;
            downloadLink.download = "report.xlsx";
            downloadLink.click();
        }
        catch (e) {
            toast.error(e?.response?.data?.message);
        }
    }

    const columns = [
        {
            name: 'User ID',
            selector: row => row['userId'],
            minWidth: '200px',
            maxWidth: '200px',
            center: false,
            cell: row => row.integrationKey
        },
        {
            name: 'Name',
            selector: row => row['name'],
            minWidth: '250px',
            maxWidth: '250px',
            center: false,
            cell: row => row.userName
        },
        {
            name: 'Department',
            selector: row => row['dept'],
            minWidth: '250px',
            maxWidth: '250px',
            center: false,
            cell: row => row.department
            // row.company + " - " + row.department
        },
        {
            name: 'Shift',
            selector: row => row['shift'],
            minWidth: '125px',
            maxWidth: '125px',
            center: true,
            cell: row => row.AttandantTime.name
        },
        {
            name: 'Date',
            selector: row => row['date'],
            minWidth: '200px',
            maxWidth: '200px',
            center: true,
            cell: row => row.startDate !== null ? `${moment(row.startDate).local().format('YYYY-MM-DD')}` : ""
            //- ${moment(row.endDate).local().format('YYYY-MM-DD')}
        },
        {
            name: 'Start Time',
            selector: row => row['start'],
            minWidth: '100px',
            maxWidth: '100px',
            center: true,
            cell: row => row.AttandantTime.startHour
        },
        {
            name: 'End Time',
            selector: row => row['end'],
            minWidth: '100px',
            maxWidth: '100px',
            center: true,
            cell: row => row.AttandantTime.endHour
        },
        {
            name: 'Is Checked',
            selector: row => row['checked'],
            minWidth: '100px',
            maxWidth: '100px',
            center: true,
            cell: row => row.isChecked.toString().toUpperCase()
        },
        {
            name: 'Check In Time',
            selector: row => row['checked'],
            minWidth: '125px',
            maxWidth: '125px',
            center: true,
            cell: row => row.checkTime !== null ? `${moment(row.checkTime).local().format('HH:mm:ss')}` : ""
        },
        // {
        //     name: 'Is Checkout',
        //     selector: row => row['isCheckedOut'],
        //     minWidth: '100px',
        //     maxWidth: '100px',
        //     center: true,
        //     cell: row => row.isCheckedOut.toString().toUpperCase()
        // },
        // {
        //     name: 'Check Out Time',
        //     selector: row => row['checkOutTime'],
        //     minWidth: '125px',
        //     maxWidth: '125px',
        //     center: true,
        //     cell: row => row.checkTime !== null ? `${moment(row.checkOutTime).local().format('HH:mm:ss')}` : ""
        // },
    ];

    // ** Custom Pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(userAttCount / tablePerPageSearch)}
                activeClassName='active'
                forcePage={tablePage !== 0 ? tablePage - 1 : 0}
                onPageChange={e => { setTablePage(e.selected + 1) }}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };

    // ** Function in get data on rows per page
    const handlePerPage = e => {
        const value = parseInt(e.currentTarget.value)
        setTablePerPageSearch(value)
        setTablePage(1)
    }

    const handleExportReport = () => exportReport();

    const handleExportByDeparment = () => exportByDeps();

    const { t } = useTranslation('');

    return (
        <Fragment>
            <Breadcrumbs title={t('History')} data={[{ title: t('Attendant') }, { title: t('History') }]} />
            <Card>
                <CardHeader>
                    <CardTitle tag='h4'>{t("Filters")}</CardTitle>
                </CardHeader>
                <CardBody>
                    <Row>
                        <Col className='my-md-0 my-1' md='3'>
                            <Label className="form-label" for='search'>{t("Search")}</Label>
                            <Select
                                id="search"
                                className='react-select'
                                classNamePrefix='select'
                                theme={selectThemeColors}
                                isClearable={true}
                                isMulti={true}
                                options={userList.map(el => {
                                    return {
                                        value: JSON.stringify({
                                            id: el.id,
                                            email: el.email,
                                            integrationKey: el.integrationKey,
                                            department: el.department.name,
                                            company: el.company.name
                                        }),
                                        label: el.fullName + " - " + el.company.name
                                    }
                                })}
                                isLoading={userQuery.loading}
                                onInputChange={(input) => {
                                    setTextSearch(input)
                                    setPerPageSearch(10)
                                }}
                                onMenuScrollToBottom={() => {
                                    if (perPageSearch < count && userQuery.loading === false) {
                                        setPerPageSearch(perPageSearch + 10)
                                    }
                                }}
                                onMenuClose={() => {
                                    setPerPageSearch(10)
                                }}
                                onChange={(users) => {
                                    if (users !== undefined && users !== null) {
                                        let ids = users.map((el) => {
                                            let value = JSON.parse(el.value)
                                            return value.id
                                        })
                                        setUserIds(ids)
                                        setPage(1)
                                    }
                                }}
                                placeholder={"Name/Email/ID"}
                            />
                        </Col>

                        <Col md='3'>
                            <Label for='time-range'>{t("Time")}</Label>
                            <Flatpickr
                                value={[startSearchDate, endSearchDate]}
                                id='time-range'
                                className='form-control text-center'
                                data-enable-time
                                onChange={date => {
                                    setPage(1)
                                    onStartSearchChange(date[0])
                                    onEndSearchChange(date[1])
                                }}
                                options={{
                                    mode: 'range',
                                    dateFormat: 'd/m/Y',
                                }}
                            />
                        </Col>

                        <Col className='my-md-0 my-1' md='3'>
                            <Label className="form-label" for='shift'>{t("Shift")}</Label>
                            <Select
                                id="shift"
                                className='react-select'
                                classNamePrefix='select'
                                theme={selectThemeColors}
                                isClearable={false}
                                isMulti={true}
                                options={getAllAttendantTime?.data?.getAllAttandantTime.map(el => {
                                    return {
                                        value: el.id,
                                        label: `${el.name} (${el.startHour} - ${el.endHour})`

                                    }
                                })}
                                onChange={(shifts) => {
                                    if (shifts !== undefined && shifts !== null) {
                                        setShifts(shifts)
                                        setTablePage(1)
                                    }
                                }}
                            />
                        </Col>

                        <Col className='my-md-0 my-1' md='3'>
                            <Label className="form-label" for='device'>{t("Device(s)")}</Label>
                            <Select
                                id="device"
                                className='react-select'
                                classNamePrefix='select'
                                theme={selectThemeColors}
                                isClearable={false}
                                isMulti={true}
                                options={deviceGroups?.data?.queryAllDeviceGroupInfo.map(el => {
                                    return {
                                        value: el.id,
                                        label: el.name,
                                        device: false,
                                    }
                                }).concat(allDevices?.data?.searchAllDevice.map(el => {
                                    return {
                                        value: el.id,
                                        label: el.deviceName,
                                        device: true,
                                    }
                                }))}
                                onChange={(device) => {
                                    setDevices(device)
                                    setTablePage(1)
                                }}
                            />
                        </Col>
                    </Row>
                </CardBody>
            </Card>

            <Card className=''>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columns}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={userAttData ? userAttData : []}
                        subHeaderComponent={
                            <CustomHeader
                                rowsPerPage={tablePerPageSearch}
                                handlePerPage={handlePerPage}
                                handleExportByDeparment={handleExportByDeparment}
                                handleExportReport={handleExportReport}
                            />
                        }
                        pointerOnHover
                        highlightOnHover
                    />
                </div>
            </Card>
        </Fragment>
    )
};

export default HistoryTable;