import { Fragment, useState } from "react"
import { Button, Col, Input, Row } from "reactstrap"
import { useTranslation } from 'react-i18next'

const CustomHeader = ({ handlePerPage, rowsPerPage, handleExportReport, handleExportByDeparment }) => {
    const { t } = useTranslation();

    return (
        <Fragment>
            <div className='invoice-list-table-header w-100 me-1 ms-50 mt-2 mb-75'>
                <Row>
                    <Col xl='6' className='d-flex align-items-center p-0'>
                        <div className='d-flex align-items-center w-100'>
                            <label htmlFor='rows-per-page'>{t("Show")}</label>
                            <Input
                                className='mx-50'
                                type='select'
                                id='rows-per-page'
                                value={rowsPerPage}
                                onChange={handlePerPage}
                                style={{ width: '5rem' }}
                            >
                                <option value='10'>10</option>
                                <option value='15'>15</option>
                                <option value='20'>20</option>
                            </Input>
                            <label htmlFor='rows-per-page'>{t("Entries")}</label>
                        </div>
                    </Col>
                    <Col
                        xl='6'
                        className='d-flex align-items-sm-center justify-content-xl-end justify-content-start flex-xl-nowrap flex-wrap flex-sm-row flex-column pe-xl-1 p-0 mt-xl-0 mt-1'
                    >
                        <div className='d-flex align-items-center table-header-actions'>
                            <Button className='add-new-user me-1' color='primary' onClick={handleExportReport}>
                                {t("Export")}
                            </Button>

                            <Button className='add-new-user' color='primary' onClick={handleExportByDeparment}>
                                {t("Export By Department")}
                            </Button>
                        </div>
                    </Col>
                </Row>
            </div>
        </Fragment >
    )
}

export default CustomHeader