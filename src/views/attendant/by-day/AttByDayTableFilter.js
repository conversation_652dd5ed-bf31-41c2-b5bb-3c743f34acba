import { Fragment, useEffect, useState } from "react";
import { Col, Input, Row, Label, Tooltip } from "reactstrap";
import { useQuery } from "@apollo/client";
import { FIND_DEPARTMENTS, GET_ALL_COMPANY } from "../../../apolo/graphql/Company";
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import { handleSearch, handlePage, handlePerPage, handleComIds, handleDepIds, handleDay, handleLoading } from './store';

const AttByDayTableFilter = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.attByDay.search);
    let perPage = useSelector(state => state.attByDay.perPage);
    let day = useSelector(state => state.attByDay.day);
    let companyIds = useSelector(state => state.attByDay.comIds);
    //#endregion

    //#region query company
    const [comList, setCompany] = useState([]);
    const getCompany = useQuery(GET_ALL_COMPANY, {
        variables: {
            name: ""
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (getCompany.loading) return;
        if (!getCompany.data) return;
        setCompany(getCompany?.data?.companies);
    }, [getCompany]);
    //#endregion

    //#region query department
    const [depList, setDepartment] = useState([]);
    const getDepartment = useQuery(FIND_DEPARTMENTS, {
        variables: {
            query: "",
            companyIds: companyIds
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (getDepartment.loading) return;
        if (!getDepartment.data) return;
        setDepartment(getDepartment?.data?.findDepartments);
    }, [getDepartment]);
    //#endregion

    //#region handle search
    const [searchValue, setSearchValue] = useState('');
    const [warning, setWarning] = useState('');
    const [openTooltip, setOpenTooltip] = useState(false);
    const [timeoutId, setTimeoutId] = useState(null);

    const handleChange = (e) => {
        const value = e.target.value;
        setSearchValue(value);

        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        if (value.length < 3 && value.length > 0) {
            setWarning(t('Input must be at least 3 characters long.'));
            setOpenTooltip(true);
            dispatch(handleLoading(false));
            dispatch(handlePage(1));
        } else {
            dispatch(handleLoading(true));
            setWarning('');
            const newTimeoutId = setTimeout(() => {
                setOpenTooltip(false);
                if (value.length === 0) {
                    dispatch(handleSearch(''));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                } else {
                    dispatch(handleSearch(value));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                }
            }, 1000);
            setTimeoutId(newTimeoutId);
        }
    };
    //#endregion

    return (
        <Fragment>
            <div className='invoice-list-table-header w-100 mt-1'>
                <Row>
                    <Col md='4'>
                        <Label className='form-label' for='time'>{t("Time")}</Label>
                        <Flatpickr
                            id='time'
                            className='form-control text-center'
                            value={day}
                            // data-enable-time
                            onChange={date => {
                                dispatch(handleDay(date[0]));
                                dispatch(handlePage(1));
                            }}
                            options={{
                                mode: 'single',
                                dateFormat: 'd/m/Y',
                            }}
                        />
                    </Col>
                    <Col md='4'>
                        <Label className='form-label' for='multiSelectCom'>
                            {t("Company")}
                        </Label>
                        <Select
                            id="multiSelectCom"
                            instanceId="multiSelectCom"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={true}
                            options={comList.map(el => {
                                return {
                                    value: el.id,
                                    label: el.name
                                }
                            })}
                            onChange={(company) => dispatch(handleComIds(company.map(el => el.value)))}
                        />
                    </Col>
                    <Col md='4'>
                        <Label className='form-label' for='multiSelectDept'>
                            {t("Department")}
                        </Label>
                        <Select
                            id="multiSelectDept"
                            instanceId="multiSelectDept"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={true}
                            options={depList.map(el => {
                                return {
                                    // value: el.id,
                                    value: JSON.stringify({ id: el.id, company: el.company.name }),
                                    companyName: el.company.name,
                                    companyId: el.company.id,
                                    label: el.name + " - " + el?.company?.name
                                }
                            })}
                            onChange={(departments) => {
                                let ids = departments.map((el) => {
                                    let value = JSON.parse(el.value)
                                    return value.id
                                });
                                dispatch(handleDepIds(ids));
                            }}
                        />
                    </Col>
                </Row>
            </div>

            <div className='invoice-list-table-header w-100 mt-2 mb-75'>
                <Row>
                    <Col xl='6' className='d-flex align-items-center'>
                        <div className='d-flex align-items-center w-100'>
                            <label htmlFor='rows-per-page'>{t("Show")}</label>
                            <Input
                                className='mx-50'
                                type='select'
                                id='rows-per-page'
                                value={perPage}
                                onChange={(e) => {
                                    dispatch(handlePerPage(parseFloat(e.target.value)));
                                    dispatch(handlePage(1));
                                }}
                                style={{ width: '5rem' }}
                            >
                                <option value='10'>10</option>
                                <option value='15'>15</option>
                                <option value='20'>20</option>
                            </Input>
                            <label htmlFor='rows-per-page'>{t("Entries")}</label>
                        </div>
                    </Col>
                    <Col xl='6' className='d-flex align-items-sm-center justify-content-xl-end justify-content-start'>
                        <div className='d-flex align-items-center mb-sm-0 mb-1'>
                            <label className='mb-0' htmlFor='search-invoice'>
                                {t("Search")}:
                            </label>
                            <Input
                                id='search-invoice'
                                className='ms-50 w-100'
                                type='text'
                                value={searchValue}
                                onChange={e => handleChange(e)}
                                placeholder={t("Search")}
                                invalid={warning !== ''}
                            />
                            {warning && (
                                <Tooltip
                                    placement='top'
                                    isOpen={openTooltip}
                                    target='search-invoice'
                                    toggle={() => setOpenTooltip(!openTooltip)}
                                >
                                    {warning}
                                </Tooltip>
                            )}
                        </div>
                    </Col>
                </Row>
            </div>
        </Fragment>
    )
};

export default AttByDayTableFilter;