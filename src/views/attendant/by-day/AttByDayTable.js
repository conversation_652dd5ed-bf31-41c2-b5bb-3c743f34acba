import { Fragment, useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { SEARCH_ABSENT_USER } from '../../../apolo/graphql/Attendant';
import { Card, CardText } from 'reactstrap';
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import Avatar from '@components/avatar';
import Breadcrumbs from '@components/breadcrumbs';
import UILoader from '@components/ui-loader';
import Spinner from '@components/spinner/Loading-spinner';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import { handleCount, handleInit, handleLoading, handlePage } from './store';
import ApiUrlUserAvatarImage from '../../urs/all-users/ApiUrlUserAvatarImage';
import AttByDayTableFilter from './AttByDayTableFilter';

const AttByDayTable = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.attByDay.search);
    let page = useSelector(state => state.attByDay.page);
    let perPage = useSelector(state => state.attByDay.perPage);
    let day = useSelector(state => state.attByDay.day);
    let count = useSelector(state => state.attByDay.count);
    let comIds = useSelector(state => state.attByDay.comIds);
    let depIds = useSelector(state => state.attByDay.depIds);
    let isAtt = useSelector(state => state.attByDay.isAtt);
    let loading = useSelector(state => state.attByDay.loading);

    useEffect(() => {
        dispatch(handleInit());
        return () => dispatch(handleLoading(false));
    }, []);
    //#endregion

    //#region loader
    const Loader = () => {
        return (
            <Fragment>
                <Spinner />
                <CardText className='mb-0 mt-1 text-white'>{t("Searching")}...</CardText>
            </Fragment>
        )
    };
    //#endregion

    //#region get attendant user in day
    const [attUserInDayList, setAttUserInDayList] = useState([]);
    const queryAttUserInDay = useQuery(SEARCH_ABSENT_USER, {
        variables: {
            search: search,
            page: page,
            perPage: perPage,
            companyIds: comIds,
            departmentIds: depIds,
            day: day,
            isAtt: isAtt,
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (queryAttUserInDay.loading) {
            dispatch(handleLoading(true));
            return;
        };
        dispatch(handleLoading(false));
        if (!queryAttUserInDay.data) return;
        setAttUserInDayList(queryAttUserInDay?.data?.searchAttUserInDay);
        dispatch(handleCount(queryAttUserInDay?.data?.searchAttUserInDay[0]?.count));
    }, [queryAttUserInDay]);
    //#endregion

    //#region column
    const renderClient = row => {
        if (row.avatar) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${row.avatar}`} />
        }
        else {
            return <Avatar className='me-1' />
        }
    };

    const columm = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '360px',
            sortField: 'fullName',
            selector: row => row.fullName,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center cursor-pointer'>
                    {renderClient(row)}
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.fullName}</span>
                    </div>
                </div>
            )
        },
        {
            name: t('Company'),
            minWidth: '360px',
            sortable: true,
            sortField: 'company',
            selector: row => row.Company.name,
            cell: row => <span className='text-capitalize'>{row.Company.name}</span>
        },
        {
            name: t('Department'),
            sortable: true,
            minWidth: '360px',
            sortField: 'department',
            selector: row => row.Department.name,
            cell: row => <span className='text-capitalize'>{row.Department.name}</span>
        },
    ];
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={(e) => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <Fragment>
            <Breadcrumbs title={t('By Day')} data={[{ title: t('Attendant') }, { title: t('By Day') }]} />

            <Card>
                <UILoader blocking={loading} loader={<Loader />}>
                    <div className='react-dataTable'>
                        <DataTable
                            noHeader
                            subHeader
                            pagination
                            responsive
                            paginationServer
                            columns={columm}
                            className='react-dataTable'
                            paginationComponent={CustomPagination}
                            data={attUserInDayList}
                            pointerOnHover
                            highlightOnHover
                            subHeaderComponent={<AttByDayTableFilter />}
                        />
                    </div>
                </UILoader>
            </Card>
        </Fragment>
    )
};

export default AttByDayTable;