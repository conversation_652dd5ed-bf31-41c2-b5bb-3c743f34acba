import { createSlice } from '@reduxjs/toolkit';

const initState = () => {
    return {
        search: '',
        page: 1,
        perPage: 10,
        day: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0),
        count: 0,
        isAtt: true,
        comIds: [],
        depIds: [],
        loading: false,
    }
};

export const attSlice = createSlice({
    name: 'attByDay',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleSearch: (state, action) => {
            state.search = action.payload
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleDay: (state, action) => {
            state.day = action.payload
        },
        handleCount: (state, action) => {
            state.count = action.payload
        },
        handleAttMode: (state, action) => {
            state.isAtt = action.payload
        },
        handleComIds: (state, action) => {
            state.comIds = action.payload
        },
        handleDepIds: (state, action) => {
            state.depIds = action.payload
        },
        handleLoading: (state, action) => {
            state.loading = action.payload
        },
    },
});

export const {
    handleInit,
    handleSearch,
    handlePage,
    handlePerPage,
    handleDay,
    handleCount,
    handleAttMode,
    handleComIds,
    handleDepIds,
    handleLoading,
} = attSlice.actions;

export default attSlice.reducer;