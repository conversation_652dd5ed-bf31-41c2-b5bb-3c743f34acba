import { useState, useEffect, Fragment } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { GET_ALL_ATTENDANT_TIME, UPDATE_USER_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import { <PERSON><PERSON>, <PERSON>dal<PERSON>eader, ModalBody, ModalFooter, Button, Label, Input, Col, FormText } from "reactstrap";
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { useTranslation } from "react-i18next";
import { ChevronsRight } from 'react-feather';
import { toast } from "react-hot-toast";

const EditUserAttendantTypeModal = ({ open, showModal, afterClose, userAttendantTime, handleDelete }) => {

    const [record, setRec] = useState(undefined);

    const [query, getAllAttendantTime] = useLazyQuery(GET_ALL_ATTENDANT_TIME);

    useEffect(() => {
        if (getAllAttendantTime.data) {
            let find = getAllAttendantTime?.data?.getAllAttandantTime.find(el => el.id === userAttendantTime?.attandantId)
            setRec(find)
        }
    }, [userAttendantTime, getAllAttendantTime])

    useEffect(() => {
        if (open) {
            query();
        }
    }, [open])

    const [update, setUpdate] = useState(undefined)
    const [error, setError] = useState("")

    const [updateUserAttendantTime, { }] = useMutation(UPDATE_USER_ATTENDANT_TIME);

    const submit = async () => {
        try {
            let validate = true;
            if (update === undefined) {
                validate = false;
                setError("Time is required")
            }
            if (validate) {
                await updateUserAttendantTime(
                    {
                        variables: {
                            userId: userAttendantTime.userId,
                            oldAttandantId: userAttendantTime.attandantId,
                            newAttandantId: update.value,
                            startDate: userAttendantTime.startDate,
                            endDate: userAttendantTime.endDate,
                        }
                    }
                )
                toast.success("Updated Successfully");
                showModal(!open);
            }
        }
        catch (e) {
            toast.error(e?.graphQLErrors[0]?.message);
        }
    }

    function closeModal() {
        // setRec(undefined);
        setUpdate(undefined);
        setError("");
        afterClose();
    }

    const { t } = useTranslation('');

    return (
        <Fragment>
            <Modal
                isOpen={open}
                toggle={() => showModal(!open)}
                onClosed={() => closeModal()}
                className='modal-dialog-centered modal-lg'
            >
                <ModalHeader className='bg-transparent' toggle={() => showModal(!open)}>
                    {t("Edit Attendant Time")}
                </ModalHeader>
                <ModalBody>
                    <div className='d-flex align-items-center mb-1'>
                        <Col className='mt-md-0 mt-3' md='5' xs='12'>
                            <h3 className='mb-0'>{t('Initial')}</h3>
                            <div className='mb-1'>
                                <Label for='name' className='form-label'>
                                    {t("Name")}
                                </Label>
                                <Input
                                    type='text'
                                    className='ms-50'
                                    id='name'
                                    value={record?.name ?? ""}
                                    onChange={(e) => { }}
                                    disabled={true}
                                />
                            </div>

                            <div className='mb-1'>
                                <Label for='time' className='form-label'>
                                    {t("Time")}
                                </Label>
                                <Input
                                    type='text'
                                    className='ms-50'
                                    id='time'
                                    value={`${record?.startHour ?? "##:##"} - ${record?.endHour ?? "##:##"}`}
                                    onChange={(e) => { }}
                                    disabled={true}
                                />
                            </div>

                            <div className='mb-1'>
                                <Label for='deviceName' className='form-label'>
                                    {t("Device(s)")}
                                </Label>
                                <Input
                                    type='text'
                                    className='ms-50'
                                    id='deviceName'
                                    value={record?.device?.name ?? (record?.deviceGroup?.name ?? "")}
                                    onChange={(e) => { }}
                                    disabled={true}
                                />
                            </div>
                        </Col>

                        <Col md='2' xs='12'>
                            <div className='d-flex justify-content-center'>
                                <ChevronsRight Right size={50} />
                            </div>
                        </Col>

                        <Col className='mt-md-0 mt-3' md='5' xs='12'>
                            <h3 className='mb-0'>{t('Update')}</h3>
                            <div className='mb-1'>
                                <Label for='multiSelectShift' className='form-label'>
                                    &nbsp;
                                </Label>
                                {error && <FormText color='danger'>{error}</FormText>}
                                <Select
                                    id="multiSelectShift"
                                    instanceId="multiSelectShift"
                                    classNamePrefix="select"
                                    className="react-select ms-50"
                                    options={getAllAttendantTime?.data?.getAllAttandantTime.map(el => {
                                        return {
                                            ...el,
                                            value: el.id,
                                            label: `${el.name} (${el.device?.name ?? (el.deviceGroup?.name ?? "")} ${el.startHour} - ${el.endHour})`
                                        }
                                    })}
                                    onChange={(shift) => {
                                        if (shift !== undefined && shift !== null) {
                                            setUpdate(shift)
                                            setError("")
                                        }
                                    }}
                                    maxMenuHeight={150}
                                    onBlur={() => {
                                        if (update === undefined) {
                                            setError("Time is required")
                                        }
                                        else {
                                            setError("")
                                        }
                                    }}
                                />
                            </div>

                            <div className='mb-1'>
                                <Label for='timeUpdate' className='form-label'>
                                    {t("Time")}
                                </Label>
                                <Input
                                    type='text'
                                    className='ms-50'
                                    id='timeUpdate'
                                    value={`${update?.startHour ?? "##:##"} - ${update?.endHour ?? "##:##"}`}
                                    onChange={(e) => { }}
                                    disabled={true}
                                />
                            </div>

                            <div className='mb-1'>
                                <Label for='deviceUpdate' className='form-label'>
                                    {t("Device(s)")}
                                </Label>
                                <Input
                                    type='text'
                                    className='ms-50'
                                    id='deviceUpdate'
                                    value={update?.device?.name ?? (update?.deviceGroup?.name ?? "")}
                                    onChange={(e) => { }}
                                    disabled={true}
                                />
                            </div>
                        </Col>
                    </div>
                </ModalBody>
                <ModalFooter className='bg-transparent d-flex justify-content-between'>
                    <div>
                        <Button color='danger' onClick={() => handleDelete(userAttendantTime.userId, userAttendantTime.attandantId, userAttendantTime.startDate, userAttendantTime.endDate)}>
                            {t("Delete")}
                        </Button>
                    </div>
                    <div>
                        <Button type='submit' color='primary' onClick={() => submit()}>
                            {t("Submit")}
                        </Button>

                        <Button type='reset' color='secondary' outline onClick={() => { showModal(!open) }}>
                            {t("Discard")}
                        </Button>
                    </div>

                </ModalFooter>
            </Modal>
        </Fragment>
    )
};

export default EditUserAttendantTypeModal;