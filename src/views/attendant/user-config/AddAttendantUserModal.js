import { useState, useEffect, Fragment } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { FIND_DEPARTMENTS, GET_ALL_COMPANY } from "../../../apolo/graphql/Company";
import { SEARCH_USER_NEW } from "../../../apolo/graphql/User";
import moment from "moment";
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { toast } from "react-hot-toast";
import Repeater from '@components/repeater';
import { X, Plus } from 'react-feather';
import { SlideDown } from 'react-slidedown';
import { Row, Col, Form, Label, Input, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FormText } from 'reactstrap';
import { useTranslation } from 'react-i18next';
import { ADD_ORG_ATTENDANTS, ADD_USER_ATTENDANTS, GET_ALL_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import UserStatusConstants from "@src/constants/UserStatusConstants";

const AddAttendantUserModal = ({ open, showAddAttModal, afterClose }) => {

    //#region State
    const UserStatus = UserStatusConstants.UserStatus;
    const [perPageSearch, setPerPageSearch] = useState(10);
    const [textSearch, setTextSearch] = useState("");
    const [userList, setUserList] = useState([]);
    const [count, setCount] = useState(0);
    const [page, setPage] = useState(1);
    const [mode, setMode] = useState('org');
    const today = new Date();
    const [shifts, setShifts] = useState(
        [
            {
                userId: undefined,
                organizationId: undefined,
                shiftId: undefined,
                startDate: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0),
                endDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0),
                error: {
                    userIdError: "",
                    organizationIdError: "",
                    attendantIdError: "",
                    startDateError: "",
                    endDateError: ""
                }

            }
        ]
    )
    const [countRepeate, setCountRepeate] = useState(1);

    const increaseCount = () => {
        // setCountRepeate(countRepeate + 1)
        let shiftsCopy = shifts.slice()
        shiftsCopy.push(
            {
                userId: undefined,
                organizationId: undefined,
                shiftId: undefined,
                startDate: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0),
                endDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0),
                error: {
                    userIdError: "",
                    organizationIdError: "",
                    attendantIdError: "",
                    startDateError: "",
                    endDateError: ""
                }
            }
        )
        setShifts(shiftsCopy)
    }
    //#endregion

    //#region query
    const [query, userQuery] = useLazyQuery(SEARCH_USER_NEW,
        {
            variables: {
                "input": {
                    "query": textSearch,
                    "pageSize": perPageSearch,
                    "pageIndex": page,
                    "departmentIds": [],
                    "companyIds": [],
                    "status": UserStatus.Active,

                }
            },
            fetchPolicy: "cache-and-network",
            nextFetchPolicy: "cache-and-network"
        });

    useEffect(() => {
        query({
            variables: {
                input: {
                    "query": textSearch,
                    "pageSize": perPageSearch,
                    "pageIndex": page,
                    "departmentIds": [],
                    "companyIds": [],
                    "status": UserStatus.Active
                }
            }
        });
    }, [perPageSearch, textSearch]);

    useEffect(() => {
        if (userQuery.loading) return;
        if (!userQuery.data) return;
        setUserList(userQuery?.data?.search)
        setCount(userQuery?.data?.search[0]?.count)
    }, [userQuery]);

    const [queryCom, companyQuery] = useLazyQuery(GET_ALL_COMPANY, {
        variables: {
            name: ""
        }
    });

    const [queryDept, departmentQuery] = useLazyQuery(FIND_DEPARTMENTS, {
        variables: {
            query: "",
            companyIds: []
        }
    });

    const [queryAttTime, getAllAttendantTime] = useLazyQuery(GET_ALL_ATTENDANT_TIME, {
        variables: {

        }
    });

    useEffect(() => {
        if (open === true) {
            queryCom({
                variables: {
                    name: ""
                }
            });
            queryDept({
                variables: {
                    query: "",
                    companyIds: []
                }
            });
            queryAttTime();
        }
    }, [open]);

    useEffect(() => {
        if (!getAllAttendantTime.loading) {
            let shiftsCopy = shifts.slice()
            for (let i = 0; i < shiftsCopy.length; i++) {
                if (getAllAttendantTime?.data?.getAllAttandantTime.find(el => el.name === shiftsCopy[i].attendantId?.value) !== undefined) {
                    let edited = getAllAttendantTime?.data?.getAllAttandantTime.find(el => el.name === shiftsCopy[i].attendantId?.value)
                    shiftsCopy.splice(i, 1, {
                        ...shiftsCopy[i],
                        attendantId: {
                            "value": edited.name,
                            "label": `${edited.name} (${edited.startHour} - ${edited.endHour})`
                        }
                    })
                }
            }
            setShifts(shiftsCopy)
        }
    }, [getAllAttendantTime]);
    //#endregion

    //#region Handle submit
    const [addUserAttendants, { }] = useMutation(ADD_USER_ATTENDANTS);
    const [addOrgAttendants, { }] = useMutation(ADD_ORG_ATTENDANTS);

    const submit = () => {
        let params;
        let shiftsCopy = shifts.slice()
        try {
            if (mode === "usr") {
                let error = false;
                for (let i = 0; i < shifts.length; i++) {
                    if (shifts[i].userId === undefined) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["userIdError"]: "User is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].attendantId === undefined) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["attendantIdError"]: "Time is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].startDate === null) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["startDateError"]: "Start date is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].endDate === null) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["endDateError"]: "End date is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                }
                setShifts(shiftsCopy)
                if (!error) {
                    params = shiftsCopy.map(el => {
                        let id = JSON.parse(el.userId.value).id
                        return (
                            {
                                userId: id,
                                attendantId: el.attendantId.value,
                                startDate: moment(el.startDate).format(`YYYY-MM-DD 00:00:00`),
                                endDate: moment(el.endDate).format(`YYYY-MM-DD 00:00:00`),
                                isActive: el.isActive,
                            }

                        )
                    })
                    addUserAttendants({
                        variables: {
                            attendants: params
                        }
                    }).then((res) => {
                        toast.success("Added Successfully");
                        showAddAttModal(!open);
                    })
                        .catch(e => {
                            toast.error(e?.graphQLErrors[0]?.message);
                        })
                }

            }
            else {
                let error = false;
                for (let i = 0; i < shifts.length; i++) {
                    if (shifts[i].organizationId === undefined) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["organizationIdError"]: "Organization is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].attendantId === undefined) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["attendantIdError"]: "Time is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].startDate === null) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["startDateError"]: "Start date is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                    if (shifts[i].endDate === null) {
                        error = true;
                        let newInput = shiftsCopy[i]
                        newInput = {
                            ...newInput,
                            error: {
                                ...newInput.error,
                                ["endDateError"]: "End date is required"
                            }
                        }
                        shiftsCopy.splice(i, 1, newInput)
                    }
                }
                setShifts(shiftsCopy)
                if (!error) {
                    params = shiftsCopy.map(el => {
                        return (
                            {
                                organizationId: el.organizationId.value,
                                isCompany: el.organizationId.company,
                                attendantId: el.attendantId.value,
                                startDate: moment(el.startDate).format(`YYYY-MM-DD 00:00:00`),
                                endDate: moment(el.endDate).format(`YYYY-MM-DD 00:00:00`),
                                isActive: el.isActive,
                            }

                        )
                    })
                    addOrgAttendants({
                        variables: {
                            attendants: params
                        }
                    })
                    showAddAttModal(!open);
                }
            }
        }
        catch (e) {
            toast.error(e?.graphQLErrors[0]?.message);
        }
    }
    //#endregion

    //#region handle change
    const changeMode = async (mode) => {
        setMode(mode)
        let shiftsCopy;
        if (mode === "org") {
            await companyQuery.refetch()
            await departmentQuery.refetch()
        }
        else {
            await query({
                variables: {
                    input: {
                        "query": textSearch,
                        "pageSize": perPageSearch,
                        "pageIndex": page,
                        "departmentIds": [],
                        "companyIds": [],
                        "status": UserStatus.Active
                    }
                }
            });
        }
        setShifts([{
            userId: undefined,
            organizationId: undefined,
            shiftId: undefined,
            startDate: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0),
            endDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0),
            error: {
                userIdError: "",
                organizationIdError: "",
                shiftIdError: "",
                startDateError: "",
                endDateError: ""
            }
        }])
    }

    const handleInputChange = (value, type, index) => {
        let shiftsCopy = shifts.slice()
        let newInput = shifts[index]
        if (type !== "range") {
            newInput = {
                ...newInput,
                [type]: value,
                error: {
                    ...newInput.error,
                    [type + "Error"]: ""
                }
            }
            shiftsCopy.splice(index, 1, newInput)
        }
        else {
            newInput = {
                ...newInput,
                startDate: value[0],
                endDate: value[1]
            }
            shiftsCopy.splice(index, 1, newInput)
        }
        setShifts(shiftsCopy)
    }

    const handleError = (value, type, index) => {
        let shiftsCopy = shifts.slice()
        let newInput = shifts[index]
        newInput = {
            ...newInput,
            error: {
                ...newInput.error,
                [type]: value
            }
        }
        shiftsCopy.splice(index, 1, newInput)

        setShifts(shiftsCopy)
    }
    //#endregion

    const deleteForm = (index) => {
        let shiftsCopy = shifts.slice()
        shiftsCopy.splice(index, 1)
        setShifts(shiftsCopy)
    }

    const { t } = useTranslation();

    return (
        <Fragment>
            <Modal
                isOpen={open}
                toggle={() => showAddAttModal(!open)}
                onClosed={afterClose}
                className='modal-dialog-centered modal-lg'
                style={{ maxWidth: "70%" }}
            >
                <ModalHeader className='bg-transparent' toggle={() => showAddAttModal(!open)}>
                    {t("Add Attendant")}
                </ModalHeader>
                <ModalBody>
                    <div className="d-flex mb-1">
                        <Label className="form-label">
                            {t("Mode")}:
                        </Label>
                        <div className='form-check ms-1'>
                            <Input
                                type="radio"
                                id="org"
                                name="org"
                                value="org"
                                checked={mode === "org"}
                                onChange={(e) => {
                                    changeMode(e.target.value)
                                }}
                            />
                            <Label htmlFor="org" className="form-label">
                                {t("By organization(s)")}
                            </Label>
                        </div>

                        <div className='form-check ms-1'>
                            <Input
                                type="radio"
                                id="usr"
                                name="usr"
                                value="usr"
                                checked={mode === "usr"}
                                onChange={(e) => {
                                    changeMode(e.target.value)
                                }}
                            />
                            <Label htmlFor="usr" className="form-label">
                                {t("By user(s)")}
                            </Label>
                        </div>
                    </div>

                    <Repeater count={countRepeate}>
                        {i => {
                            const Tag = i === 0 ? 'div' : SlideDown
                            return (
                                <Tag key={i}>
                                    <Form>
                                        {shifts.map((el, index) => {
                                            return (
                                                <Row className='justify-content-between align-items-center'>

                                                    <Col md={4} className='mb-md-0 mb-1'>
                                                        <Label className='form-label' for={`animation-item-name-${i}`}>
                                                            {mode === "org" ? "Organization:" : "User:"} &nbsp;
                                                        </Label>
                                                        {el.error.organizationIdError && <FormText color='danger'>{el.error.organizationIdError}</FormText>}
                                                        {el.error.userIdError && <FormText color="danger">{el.error.userIdError}</FormText>}
                                                        {mode === "org" ?
                                                            (
                                                                <Select
                                                                    id="multiSelectComAndDept"
                                                                    instanceId="multiSelectComAndDept"
                                                                    className="react-select"
                                                                    classNamePrefix="select"
                                                                    value={el.organizationId}
                                                                    placeholder={el?.organizationId?.label !== undefined ? el?.organizationId?.label : ""}
                                                                    options={companyQuery?.data?.companies.map(el => {
                                                                        return {
                                                                            value: el.id,
                                                                            label: el.name,
                                                                            company: true
                                                                        }
                                                                    }).concat(departmentQuery?.data?.findDepartments.map(el => {
                                                                        return {
                                                                            // value: el.id,
                                                                            value: el.id,
                                                                            label: el.name + " - " + el.company.name,
                                                                            company: false,
                                                                            companyId: el.company.id
                                                                        }
                                                                    }))}
                                                                    onChange={(el) => {
                                                                        if (el !== undefined && el !== null) {
                                                                            handleInputChange(el, "organizationId", index)
                                                                        }
                                                                    }}
                                                                    maxMenuHeight={150}
                                                                    onBlur={() => {
                                                                        if (el.organizationId === undefined) {
                                                                            handleError("Organization is required", "organizationIdError", index)
                                                                        }
                                                                        else {
                                                                            handleError("", "organizationIdError", index)
                                                                        }
                                                                    }}
                                                                />
                                                            ) :
                                                            (
                                                                <Select
                                                                    id="multiSelectUser"
                                                                    instanceId="multiSelectUser"
                                                                    className="react-select"
                                                                    classNamePrefix="select"
                                                                    options={userList.map(el => {
                                                                        return {
                                                                            value: JSON.stringify({
                                                                                id: el.id,
                                                                                email: el.email,
                                                                                integrationKey: el.integrationKey,
                                                                                department: el.department.name,
                                                                                company: el.company.name
                                                                            }),
                                                                            label: el.fullName + " - " + el.company.name
                                                                        }
                                                                    })}
                                                                    value={el.userId}
                                                                    placeholder={el?.userId?.label !== undefined ? el?.userId?.label : ""}
                                                                    loadingMessage={() => "Loading"}
                                                                    onInputChange={(input) => {
                                                                        setTextSearch(input)
                                                                        setPerPageSearch(10)
                                                                    }}
                                                                    isLoading={userQuery.loading}
                                                                    onMenuScrollToBottom={() => {
                                                                        if (perPageSearch < count && userQuery.loading === false) {
                                                                            setPerPageSearch(perPageSearch + 10)
                                                                        }
                                                                    }}
                                                                    onMenuClose={() => {
                                                                        setPerPageSearch(10)
                                                                    }}
                                                                    onChange={(user) => {
                                                                        if (user !== undefined && user !== null) {
                                                                            handleInputChange(user, "userId", index)
                                                                            setPage(1)
                                                                        }
                                                                    }}
                                                                    openMenuOnFocus={userQuery.loading === false}
                                                                    maxMenuHeight={150}
                                                                    onBlur={() => {
                                                                        if (el.userId === undefined) {
                                                                            handleError("User is required", "userIdError", index)
                                                                        }
                                                                        else {
                                                                            handleError("", "userIdError", index)
                                                                        }
                                                                    }}
                                                                />
                                                            )}
                                                    </Col>

                                                    <Col md={3} className='mb-md-0 mb-1'>
                                                        <Label className='form-label' for={`animation-cost-${i}`}>
                                                            {t("Time")}: &nbsp;
                                                        </Label>
                                                        {el.error.attendantIdError && <FormText color="danger">{el.error.attendantIdError}</FormText>}
                                                        <Select
                                                            id="multiSelectShift"
                                                            instanceId="multiSelectShift"
                                                            className="react-select"
                                                            classNamePrefix="select"
                                                            value={el.attendantId}
                                                            placeholder={el?.attendantId?.label}
                                                            options={getAllAttendantTime?.data?.getAllAttandantTime.map(el => {
                                                                return {
                                                                    value: el.id,
                                                                    label: `${el.name} (${el.startHour} - ${el.endHour})`
                                                                }
                                                            })}
                                                            onChange={(shift) => {
                                                                if (shift !== undefined && shift !== null) {
                                                                    handleInputChange(shift, "attendantId", i)
                                                                }

                                                            }}
                                                            maxMenuHeight={150}
                                                            onBlur={() => {
                                                                if (el.attendantId === undefined) {
                                                                    handleError("Time is required", "attendantIdError", i)
                                                                }
                                                                else {
                                                                    handleError("", "attendantIdError", i)
                                                                }
                                                            }}
                                                        />

                                                    </Col>

                                                    <Col md={3} className='mb-md-0 mb-1'
                                                        onFocus={() => {
                                                            if (el.startDate === null) {
                                                                handleError("Start date is required", "startDateError", index)
                                                            }
                                                            if (el.startDate !== null) {
                                                                {
                                                                    handleError("", "startDateError", index)
                                                                }
                                                                if (el.endDate === null) {
                                                                    handleError("End date is required", "endDateError", index)
                                                                }
                                                                if (el.endDate !== null) {
                                                                    handleError("", "endDateError", index)
                                                                }
                                                            }
                                                        }}
                                                        onBlur={() => {
                                                            if (el.startDate === null) {
                                                                handleError("Start date is required", "startDateError", index)
                                                            }
                                                            if (el.startDate !== null) {
                                                                {
                                                                    handleError("", "startDateError", index)
                                                                }
                                                                if (el.endDate === null) {
                                                                    handleError("End date is required", "endDateError", index)
                                                                }
                                                                if (el.endDate !== null) {
                                                                    handleError("", "endDateError", index)
                                                                }
                                                            }
                                                        }}
                                                    >
                                                        <Label
                                                            className='form-label me-1'
                                                            for='dateSelect'
                                                        >
                                                            {t("Date Range")}
                                                        </Label>
                                                        {el.error.startDateError && <FormText color='danger'>{el.error.startDateError}</FormText>}
                                                        {el.error.endDateError && <FormText color="danger">{el.error.endDateError}</FormText>}
                                                        <Flatpickr
                                                            id='dateSelect'
                                                            name='dateSelect'
                                                            className='form-control text-center'
                                                            value={el.startDate !== null ? [el.startDate, el.endDate] : null}
                                                            onChange={(date) => {
                                                                handleInputChange(date, "range", index)
                                                            }}
                                                            options={{
                                                                mode: "range",
                                                                dateFormat: "d-m-Y",
                                                            }}
                                                        />
                                                    </Col>

                                                    <Col md={2} className='mb-md-0 pt-2' >
                                                        <Button color='danger' className='text-nowrap px-1' onClick={() => { deleteForm(index) }} outline>
                                                            <X size={14} className='me-50' />
                                                            <span>{t("Delete")}</span>
                                                        </Button>
                                                    </Col>

                                                    <Col sm={12}>
                                                        <hr />
                                                    </Col>
                                                </Row>
                                            )
                                        })
                                        }
                                    </Form>
                                </Tag>
                            )
                        }}
                    </Repeater>
                    <Button className='btn-icon' color='primary' onClick={increaseCount}>
                        <Plus size={14} />
                        <span className='align-middle ms-25'>{t("Add New")}</span>
                    </Button>
                </ModalBody>
                <ModalFooter className='bg-transparent'>
                    <Button type="submit" color='primary' onClick={() => submit()}>
                        {t('Submit')}
                    </Button>
                    <Button type='reset' color='secondary' outline onClick={() => showAddAttModal(!open)}>
                        {t('Discard')}
                    </Button>
                </ModalFooter>
            </Modal>
        </Fragment>
    )
}

export default AddAttendantUserModal;