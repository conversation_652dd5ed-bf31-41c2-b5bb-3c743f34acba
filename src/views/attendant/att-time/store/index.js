// ** Redux Imports
import { createSlice } from '@reduxjs/toolkit'

const initState = () => {
    return {
        search: '',
        page: 0,
        perPage: 10,
        showAddAttTimeModal: false,
        showAddDeviceAttTimeModal: false,
        showAddUserAttTimeModal: false,
        selectedAttTimeId: "",
    }
}

export const attTimeSlice = createSlice({
    name: 'attTime',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleSearch: (state, action) => {
            state.search = action.payload
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleShowAddAttTimeModal: (state, action) => {
            state.showAddAttTimeModal = action.payload
        },
        handleShowAddDeviceAttTimeModal: (state, action) => {
            state.showAddDeviceAttTimeModal = action.payload
        },
        handleShowAddUserAttTimeModal: (state, action) => {
            state.showAddUserAttTimeModal = action.payload
        },
        handleSelectedAttTime: (state, action) => {
            state.selectedAttTimeId = action.payload
        },
    },
})

export const {
    handleInit,
    handleSearch,
    handlePage,
    handlePerPage,
    handleShowAddAttTimeModal,
    handleShowAddDeviceAttTimeModal,
    handleShowAddUserAttTimeModal,
    handleSelectedAttTime,
} = attTimeSlice.actions

export default attTimeSlice.reducer
