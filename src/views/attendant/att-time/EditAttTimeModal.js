import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Label, Input, Row, Col } from "reactstrap";
import { useLazyQuery, useMutation } from "@apollo/client";
import { GET_ATTENDANT_TIME_PROFILE, REMOVE_DEVICE_FROM_ATTENDANT_TIME, UPSERT_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import moment from "moment";
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { Trash2 } from "react-feather";
import AddDeviceAttTimeModal from "./AddDeviceAttTimeModal";

const EditAttTimeModal = ({ open, showEditModal, afterClose, attTimeId }) => {

    const [addDeviceModal, setAddDeviceModal] = useState(false);

    const [query, attTimeProfile] = useLazyQuery(GET_ATTENDANT_TIME_PROFILE, {
        variables: { attendantTimeId: attTimeId }
    })

    useEffect(() => {
        if (open) {
            query({
                variables: { attendantTimeId: attTimeId }
            });
        }
    }, [open, attTimeId]);

    const [formState, setFormState] = useState({
        isValid: false,
        values: {
            id: "",
            name: "",
            startHour: new Date(),
            endHour: new Date(),
            isNoTimeLimit: false,
            isNoUserLimit: false,
            isActive: false,
            deviceCount: 0,
            devices: [],
        },
        errors: {
            "nameError": "",
        },
    });
    useEffect(() => {
        if (attTimeProfile.loading) return;
        if (!attTimeProfile.data) return;
        let attTime = attTimeProfile.data.getAttandantTimeProfile;
        let today = new Date();
        setFormState({
            ...formState,
            values: {
                id: attTimeId,
                name: attTime.name,
                startHour: new Date(new Date(today.getFullYear(), today.getMonth(), today.getDate(), attTime.startHour.split(':')[0], attTime.startHour.split(':')[1], 0)),
                endHour: new Date(new Date(today.getFullYear(), today.getMonth(), today.getDate(), attTime.endHour.split(':')[0], attTime.endHour.split(':')[1], 59)),
                isNoTimeLimit: attTime.isNoTimeLimit,
                isNoUserLimit: attTime.isNoUserLimit,
                isActive: attTime.isActive,
                deviceCount: attTime.deviceCount,
                devices: attTime.devices,
            }
        });
    }, [attTimeProfile]);

    function closeModal() {
        setFormState({
            isValid: false,
            values: {
                id: "",
                name: "",
                startHour: new Date(),
                endHour: new Date(),
                isNoTimeLimit: false,
                isNoUserLimit: false,
                isActive: false,
                deviceCount: 0,
                devices: [],
            },
            errors: {
                "nameError": "",
            },
        }),
            afterClose();
    };

    //#region Change state events
    const handleChange = (event) => {
        if (event.target.name === "name") {
            setFormState((formState) => ({
                ...formState,
                values: {
                    ...formState.values,
                    [event.target.name]: event.target.value,
                },
                errors: {
                    ...formState.errors,
                    nameError: ""
                }
            }));
        }
        else {
            setFormState((formState) => ({
                ...formState,
                values: {
                    ...formState.values,
                    [event.target.name]: event.target.value,
                },

            }));
        }
    };

    const handleChangeTime = (name, value) => {
        setFormState((formState) => ({
            ...formState,
            values: {
                ...formState.values,
                [name]: value,
            },
        }));
    }
    //#endregion

    const [createAttendantTime, { }] = useMutation(UPSERT_ATTENDANT_TIME);

    const submit = async () => {
        try {
            let validate = true;
            if (formState.values.name === "") {
                validate = false;
                setFormState({
                    ...formState,
                    errors: {
                        ...formState.errors,
                        nameError: "Name is required",
                    }
                })
            }

            if (validate) {
                const req = await createAttendantTime({
                    variables: {
                        attandantId: attTimeId,
                        name: formState.values.name,
                        startHour: moment(formState.values.startHour).format("HH:mm"),
                        endHour: moment(formState.values.endHour).format("HH:mm"),
                        isActive: true,
                        isNoTimeLimit: formState.values.isNoTimeLimit,
                        isNoUserLimit: formState.values.isNoUserLimit,
                    }
                })
                toast.success("Updated Successfully");
                showEditModal(!open);
            }

        }
        catch (e) {
            toast.error(e?.graphQLErrors[0]?.message);
        }
    }

    const [removeDeviceAttTime, { }] = useMutation(REMOVE_DEVICE_FROM_ATTENDANT_TIME);

    function onDeviceDeleteClick(deviceId) {
        removeDeviceAttTime({
            variables: {
                attandantId: attTimeId,
                deviceIds: [deviceId]
            }
        }).then(() => {
            toast.success("Removed Successfully");
            attTimeProfile.refetch();
        }).catch(e => {
            toast.error(e?.graphQLErrors[0]?.message);
        });
    }

    const { t } = useTranslation('');

    return (
        <Fragment>
            <Modal
                isOpen={open}
                toggle={() => showEditModal(!open)}
                onClosed={closeModal}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader className='bg-transparent' toggle={() => showEditModal(!open)}>
                    {t("Edit Attendant Time")}
                </ModalHeader>
                <ModalBody>
                    <div className="mb-1">
                        <Label className="form-label" for="name">
                            {t("Name")}
                        </Label>
                        <Input
                            type="text"
                            id="name"
                            name="name"
                            className="form-control"
                            placeholder={"Name"}
                            value={formState.values.name}
                            onChange={(event) => { handleChange(event) }}
                            invalid={formState.errors.nameError !== ""}
                        />
                        {formState.errors.name ? <FormFeedback>{formState.errors.nameError}</FormFeedback> : null}
                    </div>

                    <Row>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="startHour">
                                    {t("Start Hour")} *
                                </Label>
                                <Flatpickr
                                    className='form-control'
                                    value={formState.values.startHour}
                                    id='startHour'
                                    options={{
                                        enableTime: true,
                                        noCalendar: true,
                                        dateFormat: 'H:i',
                                        time_24hr: true
                                    }}
                                    onChange={date => { if (date instanceof Date) { handleChangeTime("startHour", date) } }}
                                />
                            </div>
                        </Col>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="endHour">
                                    {t("End Hour")} *
                                </Label>
                                <Flatpickr
                                    className='form-control'
                                    value={formState.values.endHour}
                                    id='endHour'
                                    options={{
                                        enableTime: true,
                                        noCalendar: true,
                                        dateFormat: 'H:i',
                                        time_24hr: true
                                    }}
                                    onChange={date => { if (date instanceof Date) { handleChangeTime("endHour", date) } }}
                                />
                            </div>
                        </Col>
                    </Row>

                    <Row>
                        <Col md="4">
                            <div className="mb-1">
                                <Label className="form-label" for="isActive">
                                    {t("Is active")}
                                </Label>
                                <div className="form-check form-switch form-check-primary ms-1">
                                    <Input
                                        type="switch"
                                        id="isActive"
                                        name="isActive"
                                        className="form-control"
                                        checked={formState.values.isActive}
                                        onChange={() => {
                                            setFormState((formState) => ({
                                                ...formState,
                                                values: {
                                                    ...formState.values,
                                                    isActive: !formState.values.isActive,
                                                },
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                        </Col>

                        <Col md="4">
                            <div className="mb-1">
                                <Label className="form-label" for="isNoTimeLimit">
                                    {t("Is no time limit")}
                                </Label>
                                <div className="form-check form-switch form-check-primary ms-1">
                                    <Input
                                        type="switch"
                                        id="isNoTimeLimit"
                                        name="isNoTimeLimit"
                                        className="form-control"
                                        checked={formState.values.isNoTimeLimit}
                                        onChange={() => {
                                            setFormState((formState) => ({
                                                ...formState,
                                                values: {
                                                    ...formState.values,
                                                    isNoTimeLimit: !formState.values.isNoTimeLimit,
                                                },
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                        </Col>

                        <Col md="4">
                            <div className="mb-1">
                                <Label className="form-label" for="isNoUserLimit">
                                    {t("Is no user limit")}
                                </Label>
                                <div className="form-check form-switch form-check-primary ms-1">
                                    <Input
                                        type="switch"
                                        id="isNoUserLimit"
                                        name="isNoUserLimit"
                                        className="form-control"
                                        checked={formState.values.isNoUserLimit}
                                        onChange={() => {
                                            setFormState((formState) => ({
                                                ...formState,
                                                values: {
                                                    ...formState.values,
                                                    isNoUserLimit: !formState.values.isNoUserLimit,
                                                },
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                        </Col>
                    </Row>

                    {formState.values.devices.length > 0 ? (
                        formState.values.devices.map((item, index) =>
                            <Row key={index}>
                                <Col md="11">
                                    <div className="mb-1">
                                        <Label className="form-label" for="deviceName">
                                            {t("Device Name")}
                                        </Label>
                                        <Input
                                            type="text"
                                            id="deviceName"
                                            name="deviceName"
                                            className="form-control"
                                            value={item.deviceName}
                                            onChange={() => { }}
                                            disabled
                                        />
                                    </div>
                                </Col>
                                <Col md="1">
                                    <Button
                                        color="danger"
                                        className="btn-icon btn-icon-only mt-2"
                                        onClick={() => {
                                            onDeviceDeleteClick(item.id)
                                        }}
                                    >
                                        <Trash2 size={20} />
                                    </Button>
                                </Col>
                            </Row>
                        )
                    ) : <div className="text-center d-flex justify-content-center">
                        <p>{t("There are no records to display")} {t("Please add new Device")}</p>
                    </div>
                    }
                </ModalBody>
                <ModalFooter className='d-flex justify-content-between'>
                    <div>
                        <Button color="info"
                            onClick={() => {
                                setAddDeviceModal(true)
                            }}
                        >
                            {t("Add Attendant Device")}
                        </Button>
                    </div>
                    <div>
                        <Button color="primary" className="me-1"
                            onClick={() => {
                                submit()
                            }}
                        >
                            {t("Submit")}
                        </Button>

                        <Button color="secondary" outline onClick={() => { showEditModal(!open) }}>
                            {t("Discard")}
                        </Button>
                    </div>
                </ModalFooter>
            </Modal>

            <AddDeviceAttTimeModal
                open={addDeviceModal}
                showAddDeviceModal={() => setAddDeviceModal(!addDeviceModal)}
                attandantId={attTimeId}
                afterClose={() => { attTimeProfile.refetch() }}
            />
        </Fragment>
    )
};

export default EditAttTimeModal;