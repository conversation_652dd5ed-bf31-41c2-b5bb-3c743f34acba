import { useEffect, useState, Fragment } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { SEARCH_ALL_DEVICE } from "../../../apolo/graphql/Device";
import { UPSERT_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import { selectThemeColors } from '@utils';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import moment from "moment";
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { Button, Input, Label, Modal, ModalBody, ModalFooter, ModalHeader, Row, Col, FormFeedback } from "reactstrap";
import DeviceCheckList from "../../security/access-control/DeviceCheckList";
import DevicesConstants from "@src/constants/DevicesConstants";
import { useDispatch, useSelector } from "react-redux";
import {
    handleSearch,
    handlePage,
    handlePerPage,
    handleShowAddAttTimeModal,
} from './store';

const AddAttTimeModal = ({ afterClose }) => {
    const { t } = useTranslation();
    const dispatch = useDispatch();


    let selectedAttTimeId = useSelector(state => state.attTime.selectedAttTimeId);
    let showAddAttTimeModal = useSelector(state => state.attTime.showAddAttTimeModal);

    const DeviceType = DevicesConstants.DeviceType;
    const DeviceStatus = DevicesConstants.DeviceStatus;
    const [deviceTypes, setDeviceType] = useState([DeviceType.FaceTerminal]);
    const [selectedDeviceStatus, setSelectedDeviceStatus] = useState(DeviceStatus.All);
    const [textSearch, setTextSearch] = useState("");
    const [query, deviceQuery] = useLazyQuery(SEARCH_ALL_DEVICE,
        {
            variables: {
                deviceTypes: deviceTypes,
                deviceStatus: selectedDeviceStatus,
                deviceUpdateStatus: DeviceStatus.All,
                search: textSearch,
            }
        });

    useEffect(() => {
        if (showAddAttTimeModal) {
            query({
                variables: {
                    deviceTypes: deviceTypes,
                    deviceStatus: selectedDeviceStatus,
                    deviceUpdateStatus: DeviceStatus.All,
                    search: textSearch,
                }
            });
        }
    }, [showAddAttTimeModal, deviceTypes, selectedDeviceStatus, textSearch]);

    //Init Device select list
    const [selectList, setSelectList] = useState([]);
    const [selectedList, setSelectedList] = useState([]);

    useEffect(() => {
        if (deviceQuery.loading) return;
        if (!deviceQuery.data) return;
        let devices = deviceQuery.data.searchAllDevice;
        let list = [];
        for (let index = 0; index < devices.length; index++) {
            const device = devices[index];
            list.push(device);
        }
        setSelectList(list);
    }, [deviceQuery]);
    //#endregion

    //#region Init state values
    const [formState, setFormState] = useState({
        isValid: false,
        values: {
            name: "",
            startHour: new Date(),
            endHour: new Date(),
            isNoTimeLimit: false,
            isNoUserLimit: false,
            deviceIds: [],
        },
        errors: {
            "nameError": "",
        },
    });

    function closeModal() {
        setFormState({
            isValid: false,
            values: {
                name: "",
                startHour: new Date(),
                endHour: new Date(),
                isNoTimeLimit: false,
                isNoUserLimit: false,
                deviceIds: [],
            },
            errors: {
                "nameError": "",
            },
        });
        afterClose();
    }
    //#endregion

    //#region Change state events
    const handleChange = (event) => {
        if (event.target.name === "name") {
            setFormState((formState) => ({
                ...formState,
                values: {
                    ...formState.values,
                    [event.target.name]: event.target.value,
                },
                errors: {
                    ...formState.errors,
                    nameError: ""
                }
            }));
        }
        else {
            setFormState((formState) => ({
                ...formState,
                values: {
                    ...formState.values,
                    [event.target.name]: event.target.value,
                },

            }));
        }
    };

    const handleChangeTime = (name, value) => {
        setFormState((formState) => ({
            ...formState,
            values: {
                ...formState.values,
                [name]: value,
            },
        }));
    }
    //#endregion

    const [createAttendantTime, { }] = useMutation(UPSERT_ATTENDANT_TIME);

    const submit = async () => {
        try {
            let validate = true;
            if (formState.values.name === "") {
                validate = false;
                setFormState({
                    ...formState,
                    errors: {
                        ...formState.errors,
                        nameError: "Name is required",
                    }
                })
            }

            if (validate) {
                const req = await createAttendantTime({
                    variables: {
                        attandantId: null,
                        name: formState.values.name,
                        startHour: moment(formState.values.startHour).format("HH:mm"),
                        endHour: moment(formState.values.endHour).format("HH:mm"),
                        isActive: true,
                        isNoTimeLimit: formState.values.isNoTimeLimit,
                        isNoUserLimit: formState.values.isNoUserLimit,
                        deviceIds: selectedList,
                    }
                })
                toast.success("Added Successfully");
                showAddAttTimeModal(!open);
            }

        }
        catch (e) {
            toast.error(e?.graphQLErrors[0]?.message);
        }
    }

    return (
        <Fragment>
            <Modal
                isOpen={showAddAttTimeModal}
                toggle={() => { dispatch(handleShowAddAttTimeModal(!showAddAttTimeModal)) }}
                onClosed={() => closeModal()}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader toggle={() => { dispatch(handleShowAddAttTimeModal(!showAddAttTimeModal)) }}>
                    {t("Add Attendant Time")}
                </ModalHeader>
                <ModalBody>
                    <div className="mb-1">
                        <Label className="form-label d-flex" for="name">
                            {t("Name")} * &nbsp;
                            <div className="text-danger">{formState.errors.nameError}</div>
                        </Label>
                        <Input
                            type="text"
                            id="name"
                            name="name"
                            placeholder={t("Name")}
                            value={formState.values.name}
                            onChange={(event) => { handleChange(event) }}
                            onBlur={() => {
                                if (formState.values.name === "") {
                                    setFormState({
                                        ...formState,
                                        errors: {
                                            ...formState.errors,
                                            nameError: "Name is required"
                                        }
                                    })
                                }
                                else {
                                    setFormState({
                                        ...formState,
                                        errors: {
                                            ...formState.errors,
                                            nameError: ""
                                        }
                                    })
                                }
                            }}
                        />
                    </div>
                    <Row>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="startHour">
                                    {t("Start Hour")} *
                                </Label>
                                <Flatpickr
                                    className='form-control'
                                    value={formState.values.startHour}
                                    id='startHour'
                                    options={{
                                        enableTime: true,
                                        noCalendar: true,
                                        dateFormat: 'H:i',
                                        time_24hr: true
                                    }}
                                    onChange={date => { if (date instanceof Date) { handleChangeTime("startHour", date) } }}
                                />
                            </div>
                        </Col>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="endHour">
                                    {t("End Hour")} *
                                </Label>
                                <Flatpickr
                                    className='form-control'
                                    value={formState.values.endHour}
                                    id='endHour'
                                    options={{
                                        enableTime: true,
                                        noCalendar: true,
                                        dateFormat: 'H:i',
                                        time_24hr: true
                                    }}
                                    onChange={(date) => { if (date instanceof Date) { handleChangeTime("endHour", date) } }}
                                />
                            </div>
                        </Col>
                    </Row>

                    <Row>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="isNoTimeLimit">
                                    {t("Is no time limit")}
                                </Label>
                                <div className="form-check form-switch form-check-primary ms-1">
                                    <Input
                                        type="switch"
                                        id="isNoTimeLimit"
                                        name="isNoTimeLimit"
                                        className="form-control"
                                        checked={formState.values.isNoTimeLimit}
                                        onChange={() => {
                                            setFormState((formState) => ({
                                                ...formState,
                                                values: {
                                                    ...formState.values,
                                                    isNoTimeLimit: !formState.values.isNoTimeLimit,
                                                },
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                        </Col>
                        <Col md="6">
                            <div className="mb-1">
                                <Label className="form-label" for="isNoUserLimit">
                                    {t("Is no user limit")}
                                </Label>
                                <div className="form-check form-switch form-check-primary ms-1">
                                    <Input
                                        type="switch"
                                        id="isNoUserLimit"
                                        name="isNoUserLimit"
                                        className="form-control"
                                        checked={formState.values.isNoUserLimit}
                                        onChange={() => {
                                            setFormState((formState) => ({
                                                ...formState,
                                                values: {
                                                    ...formState.values,
                                                    isNoUserLimit: !formState.values.isNoUserLimit,
                                                },
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                        </Col>
                    </Row>

                    <Row>
                        <Col md="4">
                            <div className="mb-1">
                                <Label className="mb-0" for="selsectDevice">{t("Device Type")}</Label>
                                <Select
                                    className="react-select"
                                    classNamePrefix="select"
                                    id="selsectDevice"
                                    instanceId="selsectDevice"
                                    isClearable={true}
                                    isMulti={true}
                                    theme={selectThemeColors}
                                    defaultValue={[
                                        {
                                            value: DeviceType.FaceTerminal,
                                            label: "Face Terminal"
                                        },
                                    ]}
                                    options={[
                                        {
                                            value: DeviceType.FaceTerminal,
                                            label: "Face Terminal"
                                        },
                                        {
                                            value: DeviceType.UpdaterAndMatcherOnly,
                                            label: "Stream Recognizer"
                                        },
                                        {
                                            value: DeviceType.FlowDetectOnly,
                                            label: "Stream Detector"
                                        },
                                        {
                                            value: DeviceType.DisplayOnly,
                                            label: "Screen Display"
                                        },
                                        {
                                            value: DeviceType.AccessControl,
                                            label: "Access Control"
                                        },
                                    ]}
                                    onChange={(status) => {
                                        setDeviceType(status.map(el => el.value) ?? [])
                                    }}
                                />
                            </div>
                        </Col>
                        <Col md="4">
                            <div className="mb-1">
                                <Label className="mb-0" for="deviceStatus">
                                    {t("Device Status")}
                                </Label>
                                <Input
                                    type="select"
                                    name="selectedDeviceStatus"
                                    id="deviceStatus"
                                    className="form-control"
                                    value={selectedDeviceStatus}
                                    onChange={(event) => { setSelectedDeviceStatus(parseInt(event.target.value)) }}
                                >
                                    <option value={DeviceStatus.All}>{t("All")}</option>
                                    <option value={DeviceStatus.Active}>{t("Active")}</option>
                                    <option value={DeviceStatus.InActive}>{t("Inactive")}</option>
                                </Input>
                            </div>
                        </Col>
                        <Col md="4">
                            <div className="mb-1">
                                <Label className="mb-0" for="search-device">
                                    {t("Search")}
                                </Label>
                                <Input
                                    type="text"
                                    name="search-device"
                                    id="search-device"
                                    className="form-control"
                                    value={textSearch}
                                    onChange={(event) => { setTextSearch(event.target.value) }}
                                    placeholder={"Name/Id"}
                                />
                            </div>
                        </Col>
                    </Row>

                    <div className="mb-1">
                        <DeviceCheckList
                            items={
                                selectList.map((device) => {
                                    return Object.assign({
                                        id: device.id,
                                        name: device.deviceName,
                                    });
                                })
                            }
                            handlerCheck={(selectedIds) => {
                                setSelectedList(selectedIds);
                            }}
                        />
                    </div>

                </ModalBody>
                <ModalFooter>
                    <Button color="primary" onClick={() => submit()}>
                        {t("Submit")}
                    </Button>

                    <Button color="secondary" outline onClick={() => showAddAttTimeModal(!open)}>
                        {t("Discard")}
                    </Button>
                </ModalFooter>
            </Modal>

        </Fragment>
    )
};

export default AddAttTimeModal;