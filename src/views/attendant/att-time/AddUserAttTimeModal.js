import { Fragment, useState, useEffect } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { toast } from "react-hot-toast"
import { useTranslation } from "react-i18next";
import { ADD_DEVICE_TO_ATTENDANT_TIME, GET_DEVICE_NOT_USING_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from "reactstrap";
import DeviceCheckList from "../../security/access-control/DeviceCheckList";
import { useDispatch, useSelector } from "react-redux";
import {
    handleSearch,
    handlePage,
    handlePerPage,
} from './store';

const AddUserAttTimeModal = ({ afterClose }) => {

    const { t } = useTranslation();
    const dispatch = useDispatch();

    let selectedAttTimeId = useSelector(state => state.attTime.selectedAttTimeId);
    let handleShowAddUserAttTimeModal = useSelector(state => state.attTime.handleShowAddUserAttTimeModal);

    //Get selected ACDevice in location
    const [deviceList, setDeviceList] = useState([]);
    const [selectedList, setSelectedList] = useState([]);
    const [query, notUsedDevices] = useLazyQuery(GET_DEVICE_NOT_USING_ATTENDANT_TIME, {
        variables: {
            attandantId: selectedAttTimeId
        }
    })
    useEffect(() => {
        if (notUsedDevices.loading) return;
        if (!notUsedDevices.data) return;
        setDeviceList(notUsedDevices?.data?.getDeviceNotUsingAttandantTime)
    }, [notUsedDevices]);

    useEffect(() => {
        if (showAddDeviceAttTimeModal && selectedAttTimeId) {
            query({
                variables: { attandantId: selectedAttTimeId }
            });
        }
    }, [showAddDeviceAttTimeModal, selectedAttTimeId]);

    function closeModal() {
        setSelectedList([]);
        afterClose();
    }

    //Submit button
    const [addDeviceAttTime, { }] = useMutation(ADD_DEVICE_TO_ATTENDANT_TIME);
    function handleSubmit() {
        addDeviceAttTime({
            variables: {
                attandantId: attandantId,
                deviceIds: selectedList
            }
        }).then(() => {
            toast.success("Added Successfully");
            showAddDeviceModal(!open);
        }).catch(e => {
            toast.error(e?.graphQLErrors[0]?.message);
        });
    }

    return (
        <Modal
            isOpen={showAddDeviceAttTimeModal}
            toggle={() => showAddDeviceModal(!open)}
            onClosed={() => closeModal()}
            className='modal-dialog-centered modal-lg'
        >
            <ModalHeader toggle={() => showAddDeviceModal(!open)}>
                {t("Add Device")}
            </ModalHeader>
            <ModalBody>
                <DeviceCheckList
                    items={
                        deviceList?.map(item => {
                            return Object.assign({
                                id: item.id,
                                name: item.deviceName,
                            });
                        })
                    }
                    handlerCheck={(selectedIds) => {
                        setSelectedList(selectedIds);
                    }}
                />
            </ModalBody>
            <ModalFooter>
                <Button
                    color='primary'
                    onClick={() => handleSubmit()}
                >
                    {t("Submit")}
                </Button>
                <Button
                    color='secondary'
                    outline
                    onClick={() => showAddDeviceModal(!open)}
                >
                    {t("Discard")}
                </Button>
            </ModalFooter>
        </Modal>
    )
};

export default AddUserAttTimeModal;