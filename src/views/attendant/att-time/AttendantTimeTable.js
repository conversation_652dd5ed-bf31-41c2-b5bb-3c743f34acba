import { Fragment, useEffect, useState } from "react";
import { Card } from "reactstrap";
import DataTable from "react-data-table-component";
import '@styles/react/libs/tables/react-dataTable-component.scss';
import ReactPaginate from "react-paginate";
import Breadcrumbs from '@components/breadcrumbs';
import { useMutation, useQuery } from "@apollo/client";
import { DELETE_ATTANDANT_TIME, SEARCH_ATTENDANT_TIME } from "../../../apolo/graphql/Attendant";
import CustomHeader from "./AttendantTimeTableFilter";
import EditAttTimeModal from "./EditAttTimeModal";
import AddAttTimeModal from "./AddAttTimeModal";
import { Trash2 } from "react-feather";
import toast from "react-hot-toast";
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
    handleSearch,
    handlePage,
    handlePerPage,
} from './store';

const AttendantTimeTable = () => {

    //#region  State
    const MySwal = withReactContent(Swal);
    const { t } = useTranslation();
    const dispatch = useDispatch();

    let search = useSelector(state => state.attTime.search);
    let page = useSelector(state => state.attTime.page);
    let perPage = useSelector(state => state.attTime.perPage);
    let showAddAttTimeModal = useSelector(state => state.attTime.showAddAttTimeModal);

    const [count, setCount] = useState(0);
    const [attendant, setAttendant] = useState([]);

    const [addAttTimeModal, setAddAttTimeModal] = useState(false);
    const [editAttTimeModal, setEditAttTimeModal] = useState(false);
    const [selectedRowId, setSelectedRowId] = useState("");
    //#endregion

    const attendantTimes = useQuery(SEARCH_ATTENDANT_TIME, {
        variables: {
            search: search,
            page: page,
            perPage: perPage
        },
    });

    useEffect(() => {
        if (attendantTimes.loading) return;
        if (!attendantTimes.data) return;
        setAttendant(attendantTimes?.data?.searchAttandantTime);
        setCount(attendantTimes?.data?.searchAttandantTime[0]?.count);
    }, [attendantTimes]);

    //#region delete
    const [deleteAttTime, { }] = useMutation(DELETE_ATTANDANT_TIME);
    const handleDeleteAttTime = async (attTimeId) => {
        deleteAttTime({
            variables: {
                attTimeId: attTimeId
            }
        }).then(() => {
            attendantTimes.refetch();
            MySwal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: 'Attandant has been deleted.',
                customClass: {
                    confirmButton: 'btn btn-success'
                }
            });
        }).catch(e => {
            toast.error(e?.graphQLErrors[0]?.message);
        });
    };
    //#endregion

    const column = [
        {
            name: t('Name'),
            selector: row => row['name'],
            minWidth: '200px',
            center: false,
            cell: row => row.name
        },
        {
            name: t('Start Time'),
            selector: row => row['startHour'],
            minWidth: '100px',
            center: true,
            cell: row => row.startHour
        },
        {
            name: t('End Time'),
            selector: row => row['endHour'],
            minWidth: '100px',
            center: true,
            cell: row => row.endHour
        },
        {
            name: t('Is Active'),
            selector: row => row['isActive'],
            minWidth: '100px',
            center: true,
            cell: row => row.isActive.toString().toUpperCase()
        },
        {
            name: t('No Time Limit'),
            selector: row => row['isNoTimeLimit'],
            minWidth: '100px',
            center: true,
            cell: row => row.isNoTimeLimit.toString().toUpperCase()
        },
        {
            name: t('No User Limit'),
            selector: row => row['isNoUserLimit'],
            minWidth: '100px',
            center: true,
            cell: row => row.isNoUserLimit.toString().toUpperCase()
        },
        {
            name: t('Device(s)'),
            selector: row => row['devices'],
            minWidth: '100px',
            center: true,
            cell: row => row.deviceCount
        },
        {
            name: t('Action'),
            sortable: true,
            minWidth: '100px',
            sortField: 'action',
            center: true,
            selector: row => row.action,
            cell: row => (
                <div className='column-action d-flex align-items-center'>
                    <Trash2 className='text-body cursor-pointer'
                        size={17}
                        onClick={() => {
                            handleDeleteAttTime(row.id)
                        }}
                    />
                </div>
            )
        },
    ];

    // ** Custom Pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage)}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => { dispatch(handlePage(e.selected + 1)) }}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };

    return (
        <Fragment>
            <Breadcrumbs title={t('Attendant Time')} data={[{ title: t('Attendant') }, { title: t('Attendant Time') }]} />
            <Card className=''>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={column}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={attendant ? attendant : []}
                        subHeaderComponent={
                            <CustomHeader />
                        }
                        onRowClicked={(row) => {
                            setSelectedRowId(row.id)
                            setEditAttTimeModal(true)
                        }}
                        pointerOnHover
                        highlightOnHover
                    />
                </div>
            </Card>

            <EditAttTimeModal
                open={editAttTimeModal}
                showEditModal={() => setEditAttTimeModal(!editAttTimeModal)}
                attTimeId={selectedRowId}
                afterClose={() => { attendantTimes.refetch() }}
            />

            <AddAttTimeModal
                open={showAddAttTimeModal}
                showAddAttTimeModal={() => setAddAttTimeModal(!addAttTimeModal)}
                afterClose={() => { attendantTimes.refetch() }}
            />
        </Fragment>
    )
};

export default AttendantTimeTable;