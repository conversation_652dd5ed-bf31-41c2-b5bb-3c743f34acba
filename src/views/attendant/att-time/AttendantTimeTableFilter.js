import { Fragment, useState } from "react"
import { Button, Col, Input, Row } from "reactstrap"
import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from "react-redux";
import {
    handleSearch,
    handlePage,
    handlePerPage,
    handleShowAddAttTimeModal,
} from './store';

const CustomHeader = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch();

    let search = useSelector(state => state.attTime.search);
    let perPage = useSelector(state => state.attTime.perPage);
    let showAddAttTimeModal = useSelector(state => state.attTime.showAddAttTimeModal);

    return (
        <Fragment>
            <div className='invoice-list-table-header w-100 me-1 ms-50 mt-2 mb-75'>
                <Row>
                    <Col xl='6' className='d-flex align-items-center p-0'>
                        <div className='d-flex align-items-center w-100'>
                            <label htmlFor='rows-per-page'>{t("Show")}</label>
                            <Input
                                className='mx-50'
                                type='select'
                                id='rows-per-page'
                                value={perPage}
                                onChange={(e) => {
                                    const value = parseInt(e.currentTarget.value);
                                    dispatch(handlePerPage(value));
                                    dispatch(handlePage(1));
                                }}
                                style={{ width: '5rem' }}
                            >
                                <option value='10'>10</option>
                                <option value='15'>15</option>
                                <option value='20'>20</option>
                            </Input>
                            <label htmlFor='rows-per-page'>{t("Entries")}</label>
                        </div>
                    </Col>
                    <Col
                        xl='6'
                        className='d-flex align-items-sm-center justify-content-xl-end justify-content-start flex-xl-nowrap flex-wrap flex-sm-row flex-column pe-xl-1 p-0 mt-xl-0 mt-1'
                    >
                        <div className='d-flex align-items-center mb-sm-0 mb-1 me-1'>
                            <label className='mb-0' htmlFor='search-invoice'>
                                {t("Search")}:
                            </label>
                            <Input
                                id='search-invoice'
                                className='ms-50 w-100'
                                type='text'
                                value={search}
                                onChange={e => {
                                    dispatch(handleSearch(e.target.value))
                                }}
                                placeholder={t("Search")}
                            />

                        </div>

                        <div className='d-flex align-items-center table-header-actions'>

                            <Button className='add-new-user' color='primary' onClick={
                                () => { dispatch(handleShowAddAttTimeModal(!showAddAttTimeModal)) }
                            }>
                                {t("Add Attendant Time")}
                            </Button>
                        </div>
                    </Col>
                </Row>
            </div>
        </Fragment >
    )
}

export default CustomHeader