import { createSlice } from '@reduxjs/toolkit';

const initState = () => {
    return {
        search: '',
        page: 1,
        perPage: 10,
        count: 0,
        loading: false,
        activeTab: '1',
        AttInfo: {
            id: "",
            name: "",
            startTime: new Date(),
            endTime: new Date(),
        },
        AttDevice: {
            search: '',
            page: 1,
            perPage: 10,
            count: 0,
        },
        AttUser: {
            search: '',
            page: 1,
            perPage: 10,
            count: 0,
            isCheck: undefined,
        },
        AttGuest: {
            search: '',
            page: 1,
            perPage: 10,
            count: 0,
            isCheck: undefined,
        },
        UnAttUser: {
            page: 1,
            perPage: 8,
            count: 0,
        },
        UnAttGuest: {
            page: 1,
            perPage: 8,
            count: 0,
        },
        UnAttUnknown: {
            page: 1,
            perPage: 8,
            count: 0,
        },
        showAttInfoModal: false,
        showAddAttInfoModal: false,
        showAddAttDeviceModal: false,
        showAddAttUserModal: false,
        showAddAttGuestModal: false,
    }
}

export const attSlice = createSlice({
    name: 'att',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleSearch: (state, action) => {
            state.search = action.payload
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleCount: (state, action) => {
            state.count = action.payload
        },
        handleLoading: (state, action) => {
            state.loading = action.payload
        },
        handleActiveTab: (state, action) => {
            state.activeTab = action.payload
        },
        handleShowAttInfoModal: (state, action) => {
            state.showAttInfoModal = action.payload
        },
        handleShowAddAttInfoModal: (state, action) => {
            state.showAddAttInfoModal = action.payload
        },
        handleShowAddAttDeviceModal: (state, action) => {
            state.showAddAttDeviceModal = action.payload
        },
        handleShowAddAttUserModal: (state, action) => {
            state.showAddAttUserModal = action.payload
        },
        handleShowAddAttGuestModal: (state, action) => {
            state.showAddAttGuestModal = action.payload
        },
        handleSelectedAtt: (state, action) => {
            state.AttInfo = action.payload
        },
        handleDeviceSearch: (state, action) => {
            state.AttDevice.search = action.payload
        },
        handleDevicePage: (state, action) => {
            state.AttDevice.page = action.payload
        },
        handleDevicePerPage: (state, action) => {
            state.AttDevice.perPage = action.payload
        },
        handleAttDeviceSearch: (state, action) => {
            state.AttDevice.search = action.payload
        },
        handleAttDevicePage: (state, action) => {
            state.AttDevice.page = action.payload
        },
        handleAttDevicePerPage: (state, action) => {
            state.AttDevice.perPage = action.payload
        },
        handleAttDeviceCount: (state, action) => {
            state.AttDevice.count = action.payload
        },
        handleAttUserSearch: (state, action) => {
            state.AttUser.search = action.payload
        },
        handleAttUserPage: (state, action) => {
            state.AttUser.page = action.payload
        },
        handleAttUserPerPage: (state, action) => {
            state.AttUser.perPage = action.payload
        },
        handleAttUserCount: (state, action) => {
            state.AttUser.count = action.payload
        },
        handleAttUserStatus: (state, action) => {
            state.AttUser.isCheck = action.payload
        },
        handleAttGuestSearch: (state, action) => {
            state.AttGuest.search = action.payload
        },
        handleAttGuestPage: (state, action) => {
            state.AttGuest.page = action.payload
        },
        handleAttGuestPerPage: (state, action) => {
            state.AttGuest.perPage = action.payload
        },
        handleAttGuestCount: (state, action) => {
            state.AttGuest.count = action.payload
        },
        handleAttGuestStatus: (state, action) => {
            state.AttGuest.isCheck = action.payload
        },
        handleUnAttUserPage: (state, action) => {
            state.UnAttUser.page = action.payload
        },
        handleUnAttUserPerPage: (state, action) => {
            state.UnAttUser.perPage = action.payload
        },
        handleUnAttUserCount: (state, action) => {
            state.UnAttUser.count = action.payload
        },
        handleUnAttGuestPage: (state, action) => {
            state.UnAttGuest.page = action.payload
        },
        handleUnAttGuestPerPage: (state, action) => {
            state.UnAttGuest.perPage = action.payload
        },
        handleUnAttGuestCount: (state, action) => {
            state.UnAttGuest.count = action.payload
        },
        handleUnAttUnknownPage: (state, action) => {
            state.UnAttUnknown.page = action.payload
        },
        handleUnAttUnknownPerPage: (state, action) => {
            state.UnAttUnknown.perPage = action.payload
        },
        handleUnAttUnknownCount: (state, action) => {
            state.UnAttUnknown.count = action.payload
        },
    },
});

export const {
    handleInit,
    handleSearch,
    handlePage,
    handlePerPage,
    handleCount,
    handleLoading,
    handleActiveTab,
    handleShowAttInfoModal,
    handleShowAddAttInfoModal,
    handleShowAddAttDeviceModal,
    handleShowAddAttUserModal,
    handleShowAddAttGuestModal,
    handleSelectedAtt,
    handleDeviceSearch,
    handleDevicePage,
    handleDevicePerPage,
    handleAttDeviceSearch,
    handleAttDevicePage,
    handleAttDevicePerPage,
    handleAttDeviceCount,
    handleAttUserSearch,
    handleAttUserPage,
    handleAttUserPerPage,
    handleAttUserCount,
    handleAttUserStatus,
    handleAttGuestSearch,
    handleAttGuestPage,
    handleAttGuestPerPage,
    handleAttGuestCount,
    handleAttGuestStatus,
    handleUnAttUserPage,
    handleUnAttUserPerPage,
    handleUnAttUserCount,
    handleUnAttUnknownPage,
    handleUnAttUnknownPerPage,
    handleUnAttUnknownCount,
    handleUnAttGuestPage,
    handleUnAttGuestPerPage,
    handleUnAttGuestCount
} = attSlice.actions;

export default attSlice.reducer;