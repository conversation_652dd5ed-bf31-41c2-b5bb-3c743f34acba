import { Fragment, useEffect, useState } from "react";
import { Card, CardText } from "reactstrap";
import DataTable from "react-data-table-component";
import '@styles/react/libs/tables/react-dataTable-component.scss';
import ReactPaginate from "react-paginate";
import Breadcrumbs from '@components/breadcrumbs';
import { useMutation, useQuery } from "@apollo/client";
import { DELETE_ATT_INFO, SEARCH_ATT_INFO } from "../../../apolo/graphql/att";
import { Trash2 } from "react-feather";
import toast from "react-hot-toast";
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import UILoader from '@components/ui-loader';
import Spinner from '@components/spinner/Loading-spinner';
import moment from 'moment-timezone';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handlePage, handleShowAttInfoModal, handleSelectedAtt, handleCount, handleLoading, handleInit } from './store';
import AddAttInfoModal from "../../faceid/attendant/AttByLocation/AddAttInfoModal";
import AttInfoModal from "../../faceid/attendant/AttByLocation/AttInfoModal";
import AttInfoTableHeader from "./AttInfoTableHeader";

const AttInfoTable = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.att.search);
    let page = useSelector(state => state.att.page);
    let perPage = useSelector(state => state.att.perPage);
    let loading = useSelector(state => state.att.loading);
    let showAttInfoModal = useSelector(state => state.att.showAttInfoModal);
    let count = useSelector(state => state.att.count);
    const MySwal = withReactContent(Swal);

    useEffect(() => {
        dispatch(handleInit());
        return () => dispatch(handleLoading(false));
    }, []);
    //#endregion

    //#region loader
    const Loader = () => {
        return (
            <Fragment>
                <Spinner />
                <CardText className='mb-0 mt-1 text-white'>{t("Searching")}...</CardText>
            </Fragment>
        )
    };
    //#endregion

    //#region query
    const [attInfos, setAtt] = useState([]);
    const searchAttInfo = useQuery(SEARCH_ATT_INFO, {
        variables: {
            search: search,
            page: page,
            perPage: perPage
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (searchAttInfo.loading) {
            dispatch(handleLoading(true));
            return;
        };
        dispatch(handleLoading(false));
        if (!searchAttInfo.data) return;
        setAtt(searchAttInfo?.data?.searchAttInfo);
        dispatch(handleCount(searchAttInfo?.data?.searchAttInfo[0]?.count));
    }, [searchAttInfo]);
    //#endregion

    //#region delete
    const [deleteAttInfo, { }] = useMutation(DELETE_ATT_INFO);
    const handleDeleteAttInfo = async (attInfoId) => {
        const result = await MySwal.fire({
            title: 'Delete this attandant?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            deleteAttInfo({
                variables: {
                    attInfoId: attInfoId
                }
            }).then(() => {
                searchAttInfo.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: 'Deleted!',
                    text: 'Your attandant has been deleted.',
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message || e?.message);
            });
        }
    };
    //#endregion

    //#region column
    const column = [
        {
            name: t('Name'),
            selector: row => row['name'],
            minWidth: '200px',
            center: false,
            cell: row => <span className="fw-bolder">{row.name}</span>
        },
        {
            name: t('Start Time'),
            selector: row => row['startTime'],
            minWidth: '100px',
            center: true,
            cell: row => row.startTime ? moment(row.startTime).format(`DD/MM/YYYY HH:mm:ss`) : null
        },
        {
            name: t('End Time'),
            selector: row => row['endTime'],
            minWidth: '100px',
            center: true,
            cell: row => row.endTime ? moment(row.endTime).format(`DD/MM/YYYY HH:mm:ss`) : null
        },
        {
            name: t('Device(s)'),
            selector: row => row['devices'],
            minWidth: '100px',
            center: true,
            cell: row => row.deviceCount
        },
        {
            name: t('User(s)'),
            selector: row => row['users'],
            minWidth: '100px',
            center: true,
            cell: row => row.deviceCount
        },
        {
            name: t('Action'),
            sortable: true,
            minWidth: '100px',
            sortField: 'action',
            center: true,
            selector: row => row.action,
            cell: row => <div className='column-action d-flex align-items-center'>
                <Trash2 className='text-body cursor-pointer' size={17}
                    onClick={() => handleDeleteAttInfo(row.id)}
                />
            </div>
        },
    ];
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <Fragment>
            <Breadcrumbs title={t('By Location')} data={[{ title: t('Attendant') }, { title: t('By Location') }]} />

            <Card>
                <UILoader blocking={loading} loader={<Loader />}>
                    <div className='react-dataTable'>
                        <DataTable
                            noHeader
                            subHeader
                            pagination
                            responsive
                            paginationServer
                            columns={column}
                            className='react-dataTable'
                            paginationComponent={CustomPagination}
                            data={attInfos ? attInfos : []}
                            subHeaderComponent={<AttInfoTableHeader />}
                            onRowClicked={(row) => {
                                dispatch(handleSelectedAtt(row))
                                dispatch(handleShowAttInfoModal(!showAttInfoModal))
                            }}
                            pointerOnHover
                            highlightOnHover
                        />
                    </div>
                </UILoader>
            </Card>

            <AddAttInfoModal
                afterClose={() => searchAttInfo.refetch()}
            />

            <AttInfoModal
                afterClose={() => searchAttInfo.refetch()}
            />
        </Fragment>
    )
};

export default AttInfoTable;