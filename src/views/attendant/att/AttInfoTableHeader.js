import { Fragment, useState } from "react";
import { Button, Col, Input, Row, Tooltip } from "reactstrap";
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import { handleSearch, handlePage, handlePerPage, handleShowAddAttInfoModal, handleLoading } from './store';

const AttInfoTableHeader = () => {

    //#region states
    const { t } = useTranslation("");
    const dispatch = useDispatch();
    let search = useSelector(state => state.att.search);
    let perPage = useSelector(state => state.att.perPage);
    let showAddAttInfoModal = useSelector(state => state.att.showAddAttInfoModal);
    //#endregion

    //#region handle search
    const [searchValue, setSearchValue] = useState('');
    const [warning, setWarning] = useState('');
    const [openTooltip, setOpenTooltip] = useState(false);
    const [timeoutId, setTimeoutId] = useState(null);

    const handleChange = (e) => {
        const value = e.target.value;
        setSearchValue(value);

        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        if (value.length < 3 && value.length > 0) {
            setWarning(t('Input must be at least 3 characters long.'));
            setOpenTooltip(true);
            dispatch(handleLoading(false));
            dispatch(handlePage(1));
        } else {
            dispatch(handleLoading(true));
            setWarning('');
            const newTimeoutId = setTimeout(() => {
                setOpenTooltip(false);
                if (value.length === 0) {
                    dispatch(handleSearch(''));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                } else {
                    dispatch(handleSearch(value));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                }
            }, 1000);
            setTimeoutId(newTimeoutId);
        }
    };
    //#endregion

    return (
        <Fragment>
            <div className='invoice-list-table-header w-100 mt-2 mb-75'>
                <Row>
                    <Col xl='6' className='d-flex align-items-center p-0'>
                        <div className='d-flex align-items-center w-100'>
                            <label htmlFor='rows-per-page'>{t("Show")}</label>
                            <Input
                                className='mx-50'
                                type='select'
                                id='rows-per-page'
                                value={perPage}
                                onChange={(e) => {
                                    const value = parseInt(e.currentTarget.value);
                                    dispatch(handlePerPage(value));
                                    dispatch(handlePage(1));
                                }}
                                style={{ width: '5rem' }}
                            >
                                <option value='10'>10</option>
                                <option value='15'>15</option>
                                <option value='20'>20</option>
                            </Input>
                            <label htmlFor='rows-per-page'>{t("Entries")}</label>
                        </div>
                    </Col>
                    <Col xl='6' className='d-flex align-items-sm-center justify-content-xl-end justify-content-start'>
                        <div className='d-flex align-items-center mb-sm-0 mb-1 me-1'>
                            <label className='mb-0' htmlFor='search-invoice'>
                                {t("Search")}:
                            </label>
                            <Input
                                id='search-invoice'
                                className='ms-50 w-100'
                                type='text'
                                value={searchValue}
                                onChange={e => handleChange(e)}
                                placeholder={t("Search")}
                                invalid={warning !== ''}
                            />
                            {warning && (
                                <Tooltip
                                    placement='top'
                                    isOpen={openTooltip}
                                    target='search-invoice'
                                    toggle={() => setOpenTooltip(!openTooltip)}
                                >
                                    {warning}
                                </Tooltip>
                            )}
                        </div>

                        <div className='d-flex align-items-center table-header-actions'>
                            <Button color='primary' onClick={() => dispatch(handleShowAddAttInfoModal(!showAddAttInfoModal))}>
                                {t("Add")}
                            </Button>
                        </div>
                    </Col>
                </Row>
            </div>
        </Fragment>
    )
};

export default AttInfoTableHeader;