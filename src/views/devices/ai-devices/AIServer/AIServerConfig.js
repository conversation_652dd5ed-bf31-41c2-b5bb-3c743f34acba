import { Col, Input, Label, Row } from 'reactstrap';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Fragment } from 'react';

const AIServerConfig = () => {

    //#region states
    const { t } = useTranslation('');
    let selectedDevice = useSelector(state => state.device.selectedDevice);
    //#endregion

    return (
        <Fragment>
            <Row>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Lived")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.lived || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Max Age")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.maxAge || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Vol")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.vol || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Current Vol")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.current_vol}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
            </Row>

            <Row>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Request Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.request_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Mask Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.mask_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Quality Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.quality_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Headpose Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.headpose_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Detect Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.detect_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Feature Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.feature_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
                <Col md={3}>
                    <div className="mb-1">
                        <Label className="form-label">{t("Search Queue")}</Label>
                        <Input
                            className="form-control"
                            value={selectedDevice?.runStatus[0]?.search_queue || ""}
                            readOnly
                            disabled
                        />
                    </div>
                </Col>
            </Row>
        </Fragment>
    )
};

export default AIServerConfig;