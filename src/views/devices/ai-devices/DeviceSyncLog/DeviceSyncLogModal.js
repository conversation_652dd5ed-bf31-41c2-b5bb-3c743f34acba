import { useEffect, useState } from "react";
import { useLazyQuery } from "@apollo/client";
import { SEARCH_DEVICE_SYNC_LOG } from "../../../../apolo/graphql/Device";
import { <PERSON><PERSON>, Modal, Modal<PERSON>ody, Modal<PERSON>eader, Spinner, Popover, PopoverBody, Button } from "reactstrap";
import DataTable from "react-data-table-component";
import '@styles/react/libs/tables/react-dataTable-component.scss';
import ReactPaginate from 'react-paginate';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleDeviceSyncLogCount, handleDeviceSyncLogPage, handleShowDeviceSyncLogModal } from "../store";
import DeviceSyncLogTableFilter from "./DeviceSyncLogTableFilter";

const DeviceSyncLogModal = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    const [activePopoverId, setActivePopoverId] = useState(null);
    let showDeviceSyncLogModal = useSelector(state => state.device.showDeviceSyncLogModal);
    let search = useSelector(state => state.device.DeviceSyncLog.search);
    let page = useSelector(state => state.device.DeviceSyncLog.page);
    let perPage = useSelector(state => state.device.DeviceSyncLog.perPage);
    let count = useSelector(state => state.device.DeviceSyncLog.count);
    let loading = useSelector(state => state.device.DeviceSyncLog.loading);
    //#endregion

    //#region query error list
    const [syncLogList, setSyncLogList] = useState([]);
    const [query, getDeviceSyncLog] = useLazyQuery(SEARCH_DEVICE_SYNC_LOG, {
        variables: {
            search: search,
            page: page,
            perPage: perPage,
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network",
    });

    useEffect(() => {
        if (getDeviceSyncLog.loading) return;
        if (!getDeviceSyncLog.data) return;
        setSyncLogList(getDeviceSyncLog?.data?.searchDeviceSyncLog);
        dispatch(handleDeviceSyncLogCount(getDeviceSyncLog?.data?.searchDeviceSyncLog[0]?.count));
    }, [getDeviceSyncLog]);

    useEffect(() => {
        if (showDeviceSyncLogModal) {
            query({
                variables: {
                    search: search,
                    page: page,
                    perPage: perPage,
                }
            });
        }
    }, [showDeviceSyncLogModal, search, page, perPage]);
    //#endregion

    //#region column
    const togglePopover = (id) => {
        setActivePopoverId(activePopoverId === id ? null : id);
    };

    const columm = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '250px',
            sortField: 'userName',
            selector: row => row.userName,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center'>
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.userName}</span>
                        <small className='text-muted'>{row.userType}</small>
                    </div>
                </div>
            )
        },
        // {
        //     name: t('Device ID'),
        //     sortable: true,
        //     minWidth: '200px',
        //     sortField: 'deviceId',
        //     selector: row => row.deviceId,
        //     cell: row => <span className='fw-bolder'>{row.deviceId}</span>
        // },
        // {
        //     name: t('Log Type'),
        //     sortable: true,
        //     minWidth: '150px',
        //     sortField: 'logType',
        //     selector: row => row.logType,
        //     cell: row => <span className='text-capitalize'>{row.logType}</span>
        // },
        // {
        //     name: t('Status'),
        //     sortable: true,
        //     minWidth: '250px',
        //     sortField: 'status',
        //     selector: row => row.status,
        //     cell: row => (
        //         <Badge color={row.isSuccess ? 'light-success' : 'light-danger'} pill>
        //             {row.status}
        //         </Badge>
        //     )
        // },
        {
            name: t('Sync Log Detail'),
            sortable: true,
            minWidth: '150px',
            sortField: 'syncLogDetail',
            selector: row => row.syncLogDetail,
            cell: row => {
                let summary = t('Invalid detail format');
                let tooltipContent = row.syncLogDetail;
                let isSuccess = true;

                try {
                    const details = JSON.parse(row.syncLogDetail);
                    if (Array.isArray(details) && details.length > 0) {
                        const successCount = details.filter(item => item.is_success).length;
                        const failedCount = details.length - successCount;
                        summary = `${t('Success')}: ${successCount}`;
                        if (failedCount > 0) {
                            summary += `, ${t('Failed')}: ${failedCount}`;
                        }
                        // Nếu có bất kỳ item nào is_success là false thì isSuccess = false
                        isSuccess = details.every(item => item.is_success);
                    } else if (Array.isArray(details) && details.length === 0) {
                        summary = row.status;
                        tooltipContent = row.status;
                        isSuccess = row.isSuccess !== undefined ? row.isSuccess : true;
                    } else {
                        isSuccess = row.isSuccess !== undefined ? row.isSuccess : true;
                    }
                } catch (e) {
                    tooltipContent = t('Could not parse details');
                    isSuccess = row.isSuccess !== undefined ? row.isSuccess : true;
                }

                const popoverTargetId = `sync-summary-${row.id}`;

                return (
                    <>
                        <Button
                            color="link"
                            size="sm"
                            className={`text-nowrap p-0${isSuccess ? '' : ' text-danger'}`}
                            id={popoverTargetId}
                            onClick={() => togglePopover(row.id)}
                        >
                            {summary}
                        </Button>
                        <Popover
                            placement="top"
                            isOpen={activePopoverId === row.id}
                            target={popoverTargetId}
                            toggle={() => togglePopover(row.id)}
                            className="sync-log-popover"
                            fade={false}
                        >
                            <PopoverBody className="p-1 text-wrap text-break overflow-auto" style={{ maxHeight: '300px' }}>
                                {(() => {
                                    try {
                                        const details = JSON.parse(row.syncLogDetail);
                                        if (Array.isArray(details) && details.length > 0) {
                                            return details.map((item, index) => (
                                                <div
                                                    key={index}
                                                    style={{
                                                        marginBottom: '0.5rem',
                                                        paddingBottom: '0.5rem',
                                                        borderBottom: index < details.length - 1 ? '1px solid #404656' : 'none'
                                                    }}
                                                >
                                                    {Object.entries(item).map(([key, value]) => (
                                                        <p
                                                            key={key}
                                                            style={{
                                                                margin: '0.1rem 0',
                                                                color: key === 'is_success' && value === false ? 'red' : undefined
                                                            }}
                                                        >
                                                            <strong style={{ textTransform: 'capitalize' }}>
                                                                {key.replace(/_/g, ' ')}:
                                                            </strong>{' '}
                                                            {String(value)}
                                                        </p>
                                                    ))}
                                                </div>
                                            ));
                                        } else if (Array.isArray(details) && details.length === 0) {
                                            return <p>{t('Empty detail list')}</p>;
                                        } else {
                                            return <p>{t('Invalid detail format (not an array)')}</p>;
                                        }
                                    } catch (e) {
                                        return <p>{t('Could not parse details')}</p>;
                                    }
                                })()}
                            </PopoverBody>
                        </Popover>
                    </>
                );
            }
        }
    ];

    const customStyles = {
        headCells: {
            style: {
                fontWeight: 'bold',
                color: '#6E6B7B',
                backgroundColor: '#F3F2F7',
                textTransform: 'uppercase',
            },
        },
    };
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handleDeviceSyncLogPage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    const closeModal = () => {
        dispatch(handleDeviceSyncLogPage(1));
        dispatch(handleDeviceSyncLogCount(0));
    };

    return (
        <Modal
            isOpen={showDeviceSyncLogModal}
            toggle={() => dispatch(handleShowDeviceSyncLogModal(!showDeviceSyncLogModal))}
            onClosed={() => closeModal()}
            className='modal-dialog-centered modal-lg'
            style={{ maxWidth: '40%' }}
        >
            <ModalHeader toggle={() => dispatch(handleShowDeviceSyncLogModal(!showDeviceSyncLogModal))}>
                {t("Device Sync Log")}
            </ModalHeader>
            <ModalBody>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columm}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={syncLogList}
                        subHeaderComponent={<DeviceSyncLogTableFilter />}
                        highlightOnHover
                        // customStyles={customStyles}
                        progressPending={loading}
                        progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
                    />
                </div>
            </ModalBody>
        </Modal>
    )
};

export default DeviceSyncLogModal;