import { useState } from "react";
import { useMutation } from "@apollo/client";
import { ADD_DEVICE_FILTER_INTO_UNKNOWN_ALARM } from "../../../apolo/graphql/FaceIntergration";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "reactstrap";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleAddDeviceFilterUnknownModal } from "./store";
import ACDeviceAddList from "../../security/access-control/ACDevice.SelectList";

const AddDeviceFilterUnknownModal = ({ onAddDevices, alreadyDeviceIds }) => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    const showAddDeviceFilterUnknownModal = useSelector(state => state.faceIntergration.showAddDeviceFilterUnknownModal);
    const [selectedDevice, setSelectedDevice] = useState([]);
    //#endregion

    //#region handle submit
    const [addDeviceFilter, { }] = useMutation(ADD_DEVICE_FILTER_INTO_UNKNOWN_ALARM);
    const handleSubmit = () => {
        addDeviceFilter({
            variables: {
                deviceIds: selectedDevice
            }
        }).then(() => {
            toast.success(t("Device added successfully"));
            if (onAddDevices) {
                onAddDevices(selectedDevice);
            }
            dispatch(handleAddDeviceFilterUnknownModal(!showAddDeviceFilterUnknownModal));
        }).catch((err) => {
            toast.error(err.message);
        })
    };
    //#endregion

    //#region handle close modal
    const closeModal = () => {
        setSelectedDevice([]);
    };
    //#endregion    

    return (
        <Modal
            isOpen={showAddDeviceFilterUnknownModal}
            toggle={() => dispatch(handleAddDeviceFilterUnknownModal(!showAddDeviceFilterUnknownModal))}
            onClosed={() => closeModal()}
            className="modal-dialog-centered modal-lg"
        >
            <ModalHeader toggle={() => dispatch(handleAddDeviceFilterUnknownModal(!showAddDeviceFilterUnknownModal))}>
                {t("Add Device Filter Unknown")}
            </ModalHeader>
            <ModalBody>
                <ACDeviceAddList
                    onChecked={(ids) => setSelectedDevice(ids)}
                    removeIds={alreadyDeviceIds}
                />
            </ModalBody>
            <ModalFooter>
                <Button color="primary" onClick={() => handleSubmit()}>
                    {t("Add")}
                </Button>
                <Button color="secondary" outline onClick={() => dispatch(handleAddDeviceFilterUnknownModal(!showAddDeviceFilterUnknownModal))}>
                    {t("Discard")}
                </Button>
            </ModalFooter>
        </Modal>
    )
};

export default AddDeviceFilterUnknownModal;