import { useState, useEffect, Fragment } from "react";
import { useLazyQuery, useMutation } from "@apollo/client";
import { CONFIG_INTEGRATION_UNKNOWN, REMOVE_DEVICE_FILTER_INTO_UNKNOWN_ALARM } from "../../../apolo/graphql/FaceIntergration";
import { SEARCH_ALL_DEVICE } from "@src/apolo/graphql/Device";
import { Button, Input, Label, Modal, ModalBody, ModalFooter, ModalHeader } from "reactstrap";
import { Check, X, Plus, Trash2 } from "react-feather";
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleAddDeviceFilterUnknownModal, handleAllUnknownDetailModal } from "./store";
import AddDeviceFilterUnknownModal from "./AddDeviceFilterUnknownModal";

const AllUnknownDetailConfigModal = ({ afterClose }) => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let showAllUnknownDetailModal = useSelector(state => state.faceIntergration.showAllUnknownDetailModal);
    let selectedAllUnknownConfig = useSelector(state => state.faceIntergration.selectedAllUnknownConfig);
    let showAddDeviceFilterUnknownModal = useSelector(state => state.faceIntergration.showAddDeviceFilterUnknownModal);
    const [formStates, setFormStates] = useState({
        isValid: false,
        values: {
            isAlarmReIdOnly: false,
            alarmDelayTime: 0,
            isDeviceFilter: false,
            deviceFilter: []
        },
        error: {},
    });
    const MySwal = withReactContent(Swal);
    //#endregion

    //#region query all devices
    const [deviceList, setDeviceList] = useState([]);
    const [filteredDeviceList, setFilteredDeviceList] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [searchAllDevice, allDevices] = useLazyQuery(SEARCH_ALL_DEVICE, {
        variables: {
            deviceTypes: [],
            deviceStatus: 0,
            deviceUpdateStatus: -1,
            search: ""
        },
        fetchPolicy: 'no-cache',
        nextFetchPolicy: 'no-cache',
    });

    useEffect(() => {
        if (allDevices.loading) return;
        if (!allDevices.data) return;
        setDeviceList(allDevices?.data?.searchAllDevice);
        setFilteredDeviceList(allDevices?.data?.searchAllDevice);
    }, [allDevices]);

    useEffect(() => {
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            setFilteredDeviceList(
                deviceList.filter(device =>
                    formStates.values.deviceFilter?.includes(device?.id) &&
                    (device?.id?.toLowerCase()?.includes(term) || device?.deviceName?.toLowerCase()?.includes(term))
                )
            );
        } else {
            setFilteredDeviceList(deviceList.filter(device => formStates.values.deviceFilter?.includes(device?.id)));
        }
    }, [searchTerm, deviceList, formStates.values.deviceFilter]);
    //#endregion

    useEffect(() => {
        if (selectedAllUnknownConfig && showAllUnknownDetailModal) {
            const detailParse = JSON.parse(selectedAllUnknownConfig);
            setFormStates(prev => ({
                ...prev,
                values: {
                    isAlarmReIdOnly: detailParse.alarm_re_id_only,
                    alarmDelayTime: detailParse.alarm_delay_time || 0,
                    isDeviceFilter: detailParse.is_device_filter,
                    deviceFilter: detailParse.device_filter
                }
            }));

            searchAllDevice({
                variables: {
                    deviceTypes: [],
                    deviceStatus: 0,
                    deviceUpdateStatus: -1,
                    search: ""
                }
            })
        }
    }, [selectedAllUnknownConfig, showAllUnknownDetailModal]);

    //#region handle submit
    const [configUnknown, { }] = useMutation(CONFIG_INTEGRATION_UNKNOWN);
    const [removeDeviceFilter, { }] = useMutation(REMOVE_DEVICE_FILTER_INTO_UNKNOWN_ALARM);
    const handleSubmit = async () => {
        await configUnknown({
            variables: {
                isAlarmReIdOnly: formStates.values.isAlarmReIdOnly,
                alarmDelayTime: formStates.values.alarmDelayTime,
                isDeviceFilter: formStates.values.isDeviceFilter ? formStates.values.deviceFilter.length > 0 : false,
                deviceFilter: formStates.values.isDeviceFilter ? formStates.values.deviceFilter : []
            }
        }).then(res => {
            toast.success(t("Config updated successfully"));
            dispatch(handleAllUnknownDetailModal(!showAllUnknownDetailModal));
        }).catch(err => {
            toast.error(err.message);
        })
    };
    //#endregion

    const handleRemoveDevice = async (deviceId) => {
        const result = await MySwal.fire({
            title: t('Delete this device filter?'),
            text: t("You won't be able to revert this!"),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: t('Yes, delete it!'),
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            await removeDeviceFilter({
                variables: {
                    deviceIds: [deviceId]
                }
            }).then(() => {
                toast.success(t("Device removed successfully"));
                setFormStates(prev => ({
                    ...prev,
                    values: {
                        ...prev.values,
                        deviceFilter: prev.values.deviceFilter.filter(id => id !== deviceId)
                    }
                }));
                MySwal.fire({
                    icon: 'success',
                    title: t('Deleted!'),
                    text: t('Your device filter has been deleted.'),
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message);
            });
        }
    };

    const closeModal = () => {
        afterClose();
        setFormStates({
            isValid: false,
            values: {
                isAlarmReIdOnly: false,
                alarmDelayTime: 0,
                isDeviceFilter: false,
                deviceFilter: []
            },
            error: {}
        })
    };

    const handleDevicesAdded = (newDeviceIds) => {
        setFormStates(prev => ({
            ...prev,
            values: {
                ...prev.values,
                isDeviceFilter: true,
                deviceFilter: [...prev.values.deviceFilter, ...newDeviceIds]
            }
        }));
        searchAllDevice();
    };

    return (
        <Fragment>
            <Modal
                isOpen={showAllUnknownDetailModal}
                toggle={() => dispatch(handleAllUnknownDetailModal(!showAllUnknownDetailModal))}
                onClosed={() => closeModal()}
                className='modal-dialog-centered modal-lg'
            >
                <ModalHeader toggle={() => dispatch(handleAllUnknownDetailModal(!showAllUnknownDetailModal))}>
                    {t("Unknown Detail Config")}
                </ModalHeader>
                <ModalBody>
                    <div className="d-flex align-items-center mb-1">
                        <Label className="form-label col-md-7" for="isAlarmReIdOnly">{t("Alarm ReID Only")}</Label>
                        <div className='form-switch'>
                            <Input
                                type='switch'
                                id="isAlarmReIdOnly"
                                className='form-check-input col-md-5'
                                checked={formStates.values.isAlarmReIdOnly ? true : false}
                                onChange={() => {
                                    setFormStates(prev => ({
                                        ...prev,
                                        values: {
                                            ...prev.values,
                                            isAlarmReIdOnly: !prev.values.isAlarmReIdOnly
                                        }
                                    }));
                                }}
                            />
                            <Label className='form-check-label' htmlFor="isAlarmReIdOnly">
                                <span className='switch-icon-left'>
                                    <Check size={14} />
                                </span>
                                <span className='switch-icon-right'>
                                    <X size={14} />
                                </span>
                            </Label>
                        </div>
                    </div>

                    <div className="d-flex align-items-center mb-1">
                        <Label className="form-label col-md-7" for="alarmDelayTime">{t("Alarm Delay Time (seconds)")}</Label>
                        <Input
                            type="number"
                            id="alarmDelayTime"
                            value={formStates.values.alarmDelayTime}
                            onChange={e => setFormStates(prev => ({
                                ...prev,
                                values: { ...prev.values, alarmDelayTime: parseInt(e.target.value) || 0 }
                            }))}
                            min="0"
                            className="col-md-5 w-25"
                        />
                    </div>

                    <div className="d-flex align-items-center mb-1 ">
                        <Label className="form-label me-2">{t("Device Filter")}</Label>
                        <div className="form-check form-check-inline">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                id={`isDeviceFilter`}
                                name={`isDeviceFilter`}
                                checked={formStates.values.isDeviceFilter}
                                onChange={e => setFormStates(prev => ({
                                    ...prev,
                                    values: {
                                        ...prev.values,
                                        isDeviceFilter: e.target.checked
                                    }
                                }))}
                            />
                        </div>
                    </div>

                    {formStates.values.isDeviceFilter && (
                        <div className="mt-2">
                            <div className="d-flex mb-2 align-items-center justify-content-end">
                                <Input
                                    type="text"
                                    placeholder={t("Enter device ID to search")}
                                    id="searchDevice"
                                    className="me-2 w-50"
                                    onChange={(e) => {
                                        setSearchTerm(e.target.value);
                                    }}
                                />
                                <Button.Ripple className="btn-icon btn-sm rounded-circle" color="primary" onClick={() => dispatch(handleAddDeviceFilterUnknownModal(!showAddDeviceFilterUnknownModal))}>
                                    <Plus size={14} />
                                </Button.Ripple>
                            </div>

                            <div className="border rounded p-2" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                                <h6 className="mb-3">{t("Filtered Devices")}</h6>
                                {formStates.values.deviceFilter?.length > 0 ? (
                                    <table className="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th style={{ width: '20%' }}>{t("Device ID")}</th>
                                                <th style={{ width: '30%' }}>{t("Device Name")}</th>
                                                <th style={{ width: '10%' }}>{t("Action")}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {(searchTerm ? filteredDeviceList : formStates.values.deviceFilter?.map(deviceId => ({ id: deviceId, deviceName: deviceList.find(d => d.id === deviceId)?.deviceName || '-' })))
                                                .map(device => (
                                                    <tr key={device.id}>
                                                        <td className="text-truncate">{device.id}</td>
                                                        <td>{device.deviceName}</td>
                                                        <td>
                                                            <Button
                                                                color="danger"
                                                                size="sm"
                                                                onClick={() => handleRemoveDevice(device.id)}
                                                            >
                                                                <Trash2 size={14} />
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))}
                                        </tbody>
                                    </table>
                                ) : (
                                    <div className="text-muted text-center">{t("No devices added")}</div>
                                )}
                            </div>
                        </div>
                    )}
                </ModalBody>
                <ModalFooter>
                    <Button color="primary" onClick={() => handleSubmit()}>
                        {t("Submit")}
                    </Button>
                    <Button color="secondary" outline onClick={() => dispatch(handleAllUnknownDetailModal(!showAllUnknownDetailModal))}>
                        {t("Discard")}
                    </Button>
                </ModalFooter>
            </Modal>

            <AddDeviceFilterUnknownModal
                onAddDevices={handleDevicesAdded}
                alreadyDeviceIds={formStates.values.deviceFilter}
            />
        </Fragment>
    )
};

export default AllUnknownDetailConfigModal;