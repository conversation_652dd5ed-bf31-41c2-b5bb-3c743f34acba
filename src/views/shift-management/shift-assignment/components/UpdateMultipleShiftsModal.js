import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner } from 'reactstrap';
import { useMutation, useLazyQuery } from '@apollo/client';
import { GET_ALL_TIME_SHIFT, UPDATE_MULTI_SHIFT_USER } from '../../../../apolo/graphql/TimeShift';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { handleShowUpdateMultipleModal } from '../store';

const UpdateMultipleShiftsModal = ({ afterClose }) => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let showUpdateMultipleModal = useSelector(state => state.shiftAssign.showUpdateMultipleModal);
    let selectedRows = useSelector(state => state.shiftAssign.selectedRows);
    const [newShift, setNewShift] = useState(null);
    const [shiftOptions, setShiftOptions] = useState([]);
    const [loadingShifts, setLoadingShifts] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    //#endregion

    //#region query shifts
    const [query, getAllTimeShiftQuery] = useLazyQuery(GET_ALL_TIME_SHIFT, {
        fetchPolicy: 'cache-and-network',
        nextFetchPolicy: 'cache-and-network',
    });

    useEffect(() => {
        setLoadingShifts(getAllTimeShiftQuery.loading);
        if (getAllTimeShiftQuery.data && getAllTimeShiftQuery.data.getShifts) {
            const options = getAllTimeShiftQuery.data.getShifts.map(shift => ({
                value: shift.code,
                label: `${shift.code} (${shift.startHour} - ${shift.endHour})`,
            }));
            setShiftOptions(options);
        }
    }, [getAllTimeShiftQuery]);

    useEffect(() => {
        if (showUpdateMultipleModal) {
            query();
        }
    }, [showUpdateMultipleModal]);
    //#endregion

    //#region handle submit
    const [updateMultiShiftUser, { }] = useMutation(UPDATE_MULTI_SHIFT_USER);
    const handleSubmit = async () => {
        if (!newShift) {
            toast.error(t('Please select a new shift.'));
            return;
        }
        setIsSubmitting(true);
        try {
            await updateMultiShiftUser({
                variables: {
                    userIds: [...new Set(selectedRows.map(row => row.userId))],
                    oldShiftIds: [...new Set(selectedRows.map(row => row.shift.code))],
                    newShiftIds: [newShift.value],
                    startDate: selectedRows[0].startDate,
                    endDate: selectedRows[0].endDate
                }
            }).then(() => {
                toast.success(t('Shifts updated successfully!'));
                dispatch(handleShowUpdateMultipleModal(!showUpdateMultipleModal));
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message || e?.message);
            })
        } catch (err) {
            toast.error(err.message);
        } finally {
            setIsSubmitting(false);
        }
    };

    const closeModal = () => {
        afterClose();
        setNewShift(null);
        setIsSubmitting(false);
    };
    //#endregion

    return (
        <Modal
            isOpen={showUpdateMultipleModal}
            toggle={() => dispatch(handleShowUpdateMultipleModal(!showUpdateMultipleModal))}
            onClosed={() => closeModal()}
            className='modal-dialog-centered'
        >
            <ModalHeader toggle={() => dispatch(handleShowUpdateMultipleModal(!showUpdateMultipleModal))}>{t('Update Selected Shifts')}</ModalHeader>
            <ModalBody>
                <div className='mb-2'>
                    <Label className='form-label' for='new-shift-select'>
                        {t('New Shift')}
                    </Label>
                    <Select
                        id='new-shift-select'
                        instanceId='new-shift-select'
                        theme={selectThemeColors}
                        className='react-select'
                        classNamePrefix='select'
                        options={shiftOptions}
                        isLoading={loadingShifts}
                        value={newShift}
                        onChange={(e) => setNewShift(e)}
                        isClearable
                    />
                </div>
                <p>{t('Number of shifts selected: ')} {selectedRows.length}</p>
            </ModalBody>
            <ModalFooter>
                <Button color='primary' onClick={() => handleSubmit()} disabled={isSubmitting || loadingShifts || !newShift}>
                    {isSubmitting ? <Spinner size='sm' /> : t('Submit')}
                </Button>
                <Button color='secondary' outline onClick={() => dispatch(handleShowUpdateMultipleModal(!showUpdateMultipleModal))} disabled={isSubmitting}>
                    {t('Dismiss')}
                </Button>
            </ModalFooter>
        </Modal>
    );
};

export default UpdateMultipleShiftsModal; 