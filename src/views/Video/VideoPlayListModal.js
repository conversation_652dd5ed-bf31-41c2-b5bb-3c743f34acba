import { useState, useEffect, useRef, Fragment } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalHeader } from "reactstrap";
import { useLazyQuery } from "@apollo/client";
import { SEARCH_EVENT_VIDEO_AI_BOX } from "../../apolo/graphql/SearchPerson";
import ReactPlayer from "react-player";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleShowViewVideoModal } from "./store";
import VideoUrlAlert from "@src/views/components/video/videoUrlAlert";

const VideoPlayListModal = () => {

    //#region states
    const { t } = useTranslation();
    const dispatch = useDispatch();
    let page = useSelector(state => state.viewVideo.page);
    let perPage = useSelector(state => state.viewVideo.perPage);
    let deviceIds = useSelector(state => state.viewVideo.deviceIds);
    let cameraIds = useSelector(state => state.viewVideo.cameraIds);
    let cameraIps = useSelector(state => state.viewVideo.cameraIps);
    let startTime = useSelector(state => state.viewVideo.startTime);
    let endTime = useSelector(state => state.viewVideo.endTime);
    let showVideoModal = useSelector(state => state.viewVideo.showViewVideoModal);
    //#endregion

    //#region query video list 
    const [videoList, setVideoList] = useState([]);
    const [queryVideo, getVideo] = useLazyQuery(SEARCH_EVENT_VIDEO_AI_BOX, {
        variables: {
            page: page,
            perPage: perPage,
            deviceIds: deviceIds,
            cameraIds: cameraIds,
            cameraIps: cameraIps,
            startTime: startTime,
            endTime: endTime,
        },
        fetchPolicy: 'cache-and-network',
        nextFetchPolicy: 'cache-and-network'
    });

    useEffect(() => {
        if (getVideo.loading) return;
        if (!getVideo.data) return;
        setVideoList(getVideo?.data?.searchEventVideoAiBox);
    }, [getVideo]);

    useEffect(() => {
        if (showVideoModal) {
            queryVideo({
                variables: {
                    page: page,
                    perPage: perPage,
                    deviceIds: deviceIds,
                    cameraIds: cameraIds,
                    cameraIps: cameraIps,
                    startTime: startTime,
                    endTime: endTime,
                },
            });
        }
    }, [showVideoModal, page, perPage, deviceIds, cameraIds, cameraIps, startTime, endTime]);
    //#endregion

    //#region map video list
    let reverseVideoList = videoList.slice().reverse();
    const [currentVideo, setCurrentVideo] = useState(0);
    const [nextVideo, setNextVideo] = useState(1);
    const [swap, setSwap] = useState(false);
    const playerRef = useRef(null);
    const nextPlayerRef = useRef(null);

    useEffect(() => {
        if (swap) {
            setCurrentVideo((currentVideo + 1) % videoList.length);
            setNextVideo((nextVideo + 1) % videoList.length);
            setSwap(false);
        }
    }, [swap]);

    const handleVideoEnd = () => {
        setSwap(true);
    };
    //#endregion

    return (
        <Fragment>
            <Modal
                isOpen={showVideoModal}
                toggle={() => dispatch(handleShowViewVideoModal(!showVideoModal))}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader toggle={() => dispatch(handleShowViewVideoModal(!showVideoModal))}>
                    {t("View Video")}
                </ModalHeader>
                <ModalBody>
                    <div className="d-flex justify-content-center">
                        <VideoUrlAlert url={reverseVideoList[currentVideo]?.path !== undefined ?
                            `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/event-video/` + reverseVideoList[currentVideo]?.path : ""
                        } swap={!swap} handleVideoEnd={handleVideoEnd} />
                        <VideoUrlAlert url={reverseVideoList[nextVideo]?.path !== undefined ?
                            `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/event-video/` + reverseVideoList[nextVideo]?.path : ""
                        } swap={swap} handleVideoEnd={handleVideoEnd} />
                    </div>
                </ModalBody>
            </Modal>
        </Fragment>
    )
};

export default VideoPlayListModal;