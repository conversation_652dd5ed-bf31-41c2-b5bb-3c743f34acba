import { useState, useEffect, Fragment } from "react";
import md5 from "md5";
import { Card, Modal, ModalBody, ModalHeader } from "reactstrap";
import axios from "axios";
import { toast } from "react-hot-toast";

const LiveViewAIBoxModal = ({ open, showVideoModal }) => {

    const videoSrc = "http://10.2.2.133:8080/live/4786c74e-b0de-43d6-a811-3d21770cbb7b"; //live/cameraId

    return (
        <Fragment>
            <Modal
                isOpen={open}
                toggle={() => showVideoModal(!open)}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader toggle={() => showVideoModal(!open)} />
                <ModalBody>
                    <Card className="overflow-hidden">
                        <div className="d-flex justify-content-center">
                            <iframe
                                src={videoSrc}
                                width="100%"
                                height="430px"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                            />
                        </div>
                    </Card>
                </ModalBody>
            </Modal>
        </Fragment>
    )
};

export default LiveViewAIBoxModal;