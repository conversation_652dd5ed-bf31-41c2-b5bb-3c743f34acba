import { Fragment, useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON>dal, <PERSON>dal<PERSON><PERSON>, ModalHeader } from "reactstrap";
// import ReactPlayer from "react-player";
import Hls from "hls.js";
import { toast } from "react-hot-toast";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleShowViewVMSVideoModal } from "./store";

const VideoPlayerVMSModal = () => {

    //#region states
    const { t } = useTranslation("");
    const videoRef = useRef();
    const dispatch = useDispatch();
    let startTime = useSelector(state => state.viewVideo.VMS.startTime);
    let endTime = useSelector(state => state.viewVideo.VMS.endTime);
    let cameraIp = "*********";
    let showViewVMSVideoModal = useSelector(state => state.viewVideo.showViewVMSVideoModal);
    const [url, setUrl] = useState();
    // const [loading, setLoading] = useState(false);

    const startTimeEpoch = new Date(startTime).getTime().toString();
    const endTimeEpoch = new Date(endTime).getTime();
    //#endregion

    //#region fetch replay url
    const fetchReplayUrl = async () => {
        // setLoading(true);
        try {
            const response = await axios.get(`http://********:18080/api/stream-urls/getReplayEventByRtspIp`, {
                headers: {
                    'access-token': '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
                    'Content-Type': 'application/json'
                },
                params: {
                    ip: cameraIp,
                    start_time: startTimeEpoch,
                    // end_time: endTimeEpoch,
                }
            });

            const data = response.data;
            const hls = new Hls();
            if (Hls.isSupported()) {
                console.log("HLS is supported, loading video...");
                hls.loadSource(data.data.replayUrl);
                hls.attachMedia(videoRef.current);
                hls.on(Hls.Events.ERROR, (event, data) => {
                    console.error("HLS.js error:", event, data);
                    toast.error("Error loading HLS stream");
                });
            } else {
                console.warn("HLS not supported, trying direct source");
                setUrl(data.data.replayUrl);
            }
        } catch (error) {
            toast.error("Error fetching replay URL");
            console.error(error);
        } finally {
            // setLoading(false);
        }
    };

    useEffect(() => {
        if (showViewVMSVideoModal) {
            fetchReplayUrl();

        }
    }, [showViewVMSVideoModal]);
    //#endregion

    return (
        <Fragment>
            <Modal
                isOpen={showViewVMSVideoModal}
                toggle={() => dispatch(handleShowViewVMSVideoModal(!showViewVMSVideoModal))}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader toggle={() => dispatch(handleShowViewVMSVideoModal(!showViewVMSVideoModal))}>
                    {t("View Video")}
                </ModalHeader>
                <ModalBody>
                    <Card className="overflow-hidden">
                        <div className="d-flex justify-content-center">
                            {/* {loading ? (
                                <p>Loading video...</p>
                            ) : url ? (
                                console.log(url), */}
                            {/* <ReactPlayer
                                    playerRef={playerRef}
                                    url={"http://10.2.2.213:80/cam2/cam2/replay_1739416351000_1739416361000_3/hls.m3u8?token=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}
                                    autoPlay
                                    controls
                                    width="100%"
                                    height="100%"
                                    onError={(error) => {
                                        console.error("Video error:", error);
                                        toast.error("Error loading the stream");
                                    }}
                                /> */}
                            {/* ) : (
                                <p>No video available</p>
                            )} */}
                            <video
                                ref={videoRef}
                                controls
                                // {...props}
                                src={url}
                                width="100%"
                                height="100%"
                                autoPlay
                            />
                        </div>
                    </Card>
                </ModalBody>
            </Modal>
        </Fragment>
    );
};

export default VideoPlayerVMSModal;
