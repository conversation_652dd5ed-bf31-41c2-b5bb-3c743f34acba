import { useState, useEffect, Fragment } from "react";
import md5 from "md5";
import { Card, Modal, ModalBody, ModalHeader } from "reactstrap";
import axios from "axios";
import { toast } from "react-hot-toast";
import ReactPlayer from "react-player";

const NxVideoPlayListModal = ({ open, showVideoModal, startTime, cameraId, duration }) => {

    const [authenKey, setAuthenKey] = useState("");

    const getNxAuthenKeys = async () => {
        let result = await axios({
            method: 'get',
            url: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/getNonce`,
        })
        var realm = result.data.reply.realm;
        var nonce = result.data.reply.nonce;
        var digest = md5("admin" + ":" + realm + ":" + "123456Aa");
        var partial_ha2 = md5("GET" + ":");
        var simplified_ha2 = md5(digest + ":" + nonce + ":" + partial_ha2);
        var authKey = btoa("admin" + ":" + nonce + ":" + simplified_ha2);
        setAuthen<PERSON>ey(authKey);
    }

    useEffect(() => {
        if (open) {
            getNxAuthenKeys();
        }
    }, [open]);

    return (
        <Fragment>
            <Modal
                isOpen={open}
                toggle={() => showVideoModal(!open)}
                className="modal-dialog-centered modal-lg"
            >
                <ModalHeader toggle={() => showVideoModal(!open)} />
                <ModalBody>
                    <Card className="overflow-hidden">
                        <div className="d-flex justify-content-center">
                            <ReactPlayer
                                controls
                                height='100%'
                                playing
                                muted
                                url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP_NX}/media/${cameraId}.webm?pos=${new Date(startTime).toISOString()}&low=true&duration=${duration < 30 ? 30 : duration.toFixed()}&auth=${authenKey}`}
                                // onProgress={(data) => {
                                //     if (data.playedSeconds === data.loadedSeconds) {
                                //         if (index !== 0) {
                                //             setIndex(index - 1)
                                //         }
                                //         // else {
                                //         //     query({
                                //         //         variables: {
                                //         //             "cameraId": "88d4dea6-f8a7-4e4b-a86f-1234asdsdasde",
                                //         //             "time": moment(videoList[index].fileLastModified).add(1, 's').format(`YYYY-MM-DD HH:mm:ssZ`),
                                //         //         },
                                //         //     })
                                //         //     setIndex(0)
                                //         // }
                                //     }
                                // }}
                                onError={(error) => {
                                    // console.log(error)
                                    toast.error("Error loading the stream");
                                }}
                            />
                        </div>
                    </Card>
                </ModalBody>
            </Modal>
        </Fragment>
    )
};

export default NxVideoPlayListModal;