import { createSlice } from '@reduxjs/toolkit';

const initState = () => {

    return {
        page: 1,
        perPage: 10,
        deviceIds: [],
        cameraIds: [],
        cameraIps: [],
        startTime: new Date(new Date().getFullYear(), new Date().getMonth(), 1, 0, 0, 0),
        endTime: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59, 59),
        showViewVideoModal: false,
        showViewVMSVideoModal: false,
        VMS: {
            startTime: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0),
            endTime: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59, 59),
            cameraIp: "",
        }
    }
}

export const videoSlice = createSlice({
    name: 'viewVideo',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleDeviceIds: (state, action) => {
            state.deviceIds = action.payload
        },
        handleCameraIds: (state, action) => {
            state.cameraIds = action.payload
        },
        handleCameraIps: (state, action) => {
            state.cameraIps = action.payload
        },
        handleStartTime: (state, action) => {
            state.startTime = action.payload
        },
        handleEndTime: (state, action) => {
            state.endTime = action.payload
        },
        handleShowViewVideoModal: (state, action) => {
            state.showViewVideoModal = action.payload
        },
        handleShowViewVMSVideoModal: (state, action) => {
            state.showViewVMSVideoModal = action.payload
        },
        handleStartTimeVMS: (state, action) => {
            state.VMS.startTime = action.payload
        },
        handleEndTimeVMS: (state, action) => {
            state.VMS.endTime = action.payload
        },
        handleCameraIpVMS: (state, action) => {
            state.VMS.cameraIp = action.payload
        },
    },
})

export const {
    handleInit,
    handlePage,
    handlePerPage,
    handleDeviceIds,
    handleCameraIds,
    handleCameraIps,
    handleStartTime,
    handleEndTime,
    handleShowViewVideoModal,
    handleShowViewVMSVideoModal,
    handleStartTimeVMS,
    handleEndTimeVMS,
    handleCameraIpVMS,
} = videoSlice.actions

export default videoSlice.reducer;