import { Fragment, useState, useEffect } from 'react'
import Select from 'react-select'
import ReactPaginate from 'react-paginate'
import DataTable from 'react-data-table-component'
import { Plus, Trash2, User, UserPlus, <PERSON>r<PERSON><PERSON><PERSON>, UserX, Eye } from 'react-feather'
import Avatar from '@components/avatar'
import {
    Row, Col, Card, Input, Label, Button, CardBody, CardTitle, CardHeader, DropdownMenu, DropdownItem,
    DropdownToggle, UncontrolledDropdown, Badge
} from 'reactstrap'
import '@styles/react/libs/react-select/_react-select.scss'
import '@styles/react/libs/tables/react-dataTable-component.scss'
import '../../../assets/css/OnclickText.css'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@apollo/client'
import { GET_ALL_COMPANY, FIND_DEPARTMENTS } from '../../../apolo/graphql/Company'
import { SEARCH_TELEGRAM_GROUP } from '../../../apolo/graphql/OTT'
import TelegramGroupTableFilter from './TelegramGroupTableFilter'
// import AddNewUser from './AddNewUser'
// import ExportUser from './ExportUserModal'
// import ImportUser from './ImportUserModal'
// import UserProfile from './UserProfile'
// import AddADUserModal from './AddADUserModal'
// import UserScanModal from './UserScanModal'


const CompanyTable = () => {
    const { t } = useTranslation();
    // ** States
    const [searchTerm, setSearchTerm] = useState('')
    const [currentPage, setCurrentPage] = useState(1)
    const [rowsPerPage, setRowsPerPage] = useState(10)

    const [companies, setCompany] = useState([]);
    const [count, setCount] = useState(0);

    // const [showUserProfileModal, setShowUserProfileModal] = useState(false)
    // const [addNewUserModal, setAddNewUserModal] = useState(false)
    // const [addDepartmentModal, setAddDepartmentModal] = useState(false)
    // const [exportUserModal, setExportUserModal] = useState(false)
    // const [importUserModal, setImportUserModal] = useState(false)
    // const [addADUserModal, setAddADUserModal] = useState(false)
    // const [userScanModal, setUserScanModal] = useState(false)
    // const [company, setCompany] = useState([]);
    // const [companies, setSearchCompany] = useState([]);
    // const [department, setDepartment] = useState([]);
    // const [status, setStatus] = useState(UserStatus.All);
    // const [mode, setMode] = useState("");
    // const [userId, setUserId] = useState("");


    const teleGroupQuery = useQuery(SEARCH_TELEGRAM_GROUP, {
        variables: {
            search: searchTerm,
            page: currentPage,
            perPage: rowsPerPage
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });
    useEffect(() => {
        if (teleGroupQuery.loading) return;
        if (!teleGroupQuery.data) {
            setCompany([]);
            return;
        }
        setCompany(teleGroupQuery?.data?.searchTelegramGroup)
        setCount(teleGroupQuery?.data?.searchTelegramGroup[0]?.count)
    }, [teleGroupQuery]);

    // ** Function to toggle sidebar
    // const newUserModal = () => setAddNewUserModal(!addNewUserModal)
    // const departmentModal = () => setAddDepartmentModal(!addDepartmentModal)
    // const exportModal = () => setExportUserModal(!exportUserModal)
    // const importModal = () => setImportUserModal(!importUserModal)
    // const ADUserModal = () => setAddADUserModal(!addADUserModal)

    // ** User filter options
    // const companyQuery = useQuery(GET_ALL_COMPANY, {
    //     variables: {
    //         name: ""
    //     }
    // });

    // const getAllDepartment = useQuery(FIND_DEPARTMENTS, {
    //     variables: {
    //         query: "",
    //         companyIds: []
    //     }
    // });

    const statusObj = {
        ACTIVE: 'light-success',
        DEACTIVE: 'light-danger'
    }

    const GroupTypeEnum = {
        "Face Recognition": 0,
        "Person Intrustion": 1,
        "Sercurity": 2,
        "All Type": 3,
        "Device Monitor": 4,
    }

    // const renderClient = row => {
    //     if (row.avatarBase64) {
    //         return <Avatar className='me-1' img={row.avatarBase64.data} width='32' height='32' />
    //     } else {
    //         return (
    //             <Avatar
    //                 initials
    //                 className='me-1'
    //             />
    //         )
    //     }
    // }

    const columm = [
        {
            name: `${t('Group Name')}`,
            sortable: true,
            minWidth: '150px',
            sortField: 'groupName',
            selector: row => row.name,
            cell: row => row.name
        },
        {
            name: `${t('Group Type')}`,
            sortable: true,
            minWidth: '150px',
            sortField: 'groupType',
            selector: row => row.type,
            cell: row => GroupTypeEnum[row.type]
        },
        {
            name: `${t('Status')}`,
            sortable: true,
            minWidth: '150px',
            sortField: 'status',
            selector: row => row.isEnable,
            cell: row => <Badge className='text-capitalize' color={row.isEnable ? statusObj.ACTIVE : statusObj.DEACTIVE} pill>
                {row.isEnable ? t('ACTIVE') : t('DEACTIVE')}
            </Badge>
        },
        {
            name: `${t('Company')}`,
            minWidth: '100px',
            sortable: true,
            sortField: 'company',
            selector: row => row.Companies,
            cell: row => <Badge className='text-capitalize on-click-text'>
                {row.Companies.length}
            </Badge>
        },
        {
            name: `${t('Department')}`,
            minWidth: '100px',
            sortable: true,
            sortField: 'department',
            selector: row => row.Departments,
            cell: row => <Badge className='text-capitalize on-click-text'>
                {row.Departments.length}
            </Badge>
        },
        {
            name: `${t('User')}`,
            minWidth: '100px',
            sortable: true,
            sortField: 'user',
            selector: row => row.Users,
            cell: row => <Badge className='text-capitalize on-click-text'>
                {row.Users.length}
            </Badge>
        },
    ]

    // ** Function in get data on rows per page
    const handlePerPage = e => {
        const value = parseInt(e.currentTarget.value)
        setRowsPerPage(value)
        setCurrentPage(1)
    }

    // ** Function in get data on search query change
    const handleFilter = val => {
        setSearchTerm(val)
    }

    // ** Custom Pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / rowsPerPage)}
                activeClassName='active'
                forcePage={currentPage !== 0 ? currentPage - 1 : 0}
                onPageChange={
                    e => {
                        setCurrentPage(e.selected + 1)
                    }
                }
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    }

    return (
        <Fragment>
            <Card className='overflow-hidden'>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columm}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={companies}
                        highlightOnHover
                        subHeaderComponent={
                            <TelegramGroupTableFilter
                                searchTerm={searchTerm}
                                rowsPerPage={rowsPerPage}
                                handleFilter={handleFilter}
                                handlePerPage={handlePerPage}
                            />
                        }
                    />
                </div>
            </Card>

            {/* <UserProfile
                open={showUserProfileModal}
                userProfileModal={setShowUserProfileModal}
                afterClose={() => { userQuery.refetch() }}
                userId={userId}
            />
            <AddNewUser
                open={addNewUserModal}
                newUserModal={newUserModal}
            />
            <AddDepartment
                open={addDepartmentModal}
                departmentModal={departmentModal}
                type={mode} />
            <ExportUser
                open={exportUserModal}
                exportModal={exportModal}
                userStatus={UserStatus} />
            <ImportUser
                open={importUserModal}
                importModal={importModal}
            />
            <AddADUserModal
                open={addADUserModal}
                ADUserModal={ADUserModal}
            />
            <UserScanModal
                open={userScanModal}
                userScanModal={setUserScanModal}
                afterClose={() => { userQuery.refetch() }}
                userStatus={UserStatus}
            /> */}
        </Fragment>
    )
}

export default CompanyTable