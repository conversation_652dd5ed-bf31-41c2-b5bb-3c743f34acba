import { useState, useEffect, Fragment } from "react";
import { <PERSON><PERSON>, Col, Input, Label, Modal, Modal<PERSON>ody, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "reactstrap";
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import { toast } from "react-hot-toast";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleShowSearchUserInDBModal } from "../faceid/timekeeping-unknown/store";
import { handleSelectedUserId, handleShowUserProfileModal } from "../urs/all-users/store";
import ApiUrlUserAvatarImage from "../urs/all-users/ApiUrlUserAvatarImage";

const SearchUserInDBModal = ({ afterClose }) => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let showSearchUserInDBModal = useSelector(state => state.timekeepingUnknown.showSearchUserInDBModal);
    let showUserProfileModal = useSelector((state) => state.user.showUserProfileModal);
    let image = useSelector((state) => state.faceHistory.SmartSearch.image);
    const [imageFetched, setImageFetched] = useState(null);
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [count, setCount] = useState(0);
    const [searchResult, setSearchResult] = useState([]);
    const [resultModal, setResultModal] = useState(false);
    const [formStates, setFormStates] = useState({
        isValid: false,
        values: {
            image: null,
            topK: 50,
            coreFaceIp: "",
            coreFaceId: "",
        },
        errors: {}
    });
    //#endregion

    useEffect(() => {
        const fetchData = async () => {
            if (image === undefined) return;
            if (showSearchUserInDBModal === false) return;
            axios.get(`${import.meta.env.VITE_PUBLIC_BACKEND_IP}${image}`, {
                responseType: 'arraybuffer',
                headers: {
                    Accept: 'image/jpeg',
                },
            }).then(async (res) => {
                const blob = new Blob([res.data], { type: 'image/jpeg' });
                const objectURL = URL.createObjectURL(blob);
                let image = await fetch(objectURL).then((res) => res.blob());
                setImageFetched(image);
            }).catch((err) => {
                console.log(err);
            });
        };

        fetchData();
    }, [image, showSearchUserInDBModal]);

    //#region functions
    const handleChange = (e) => {
        setFormStates({
            ...formStates,
            values: {
                ...formStates.values,
                [e.target.name]: e.target.value,
            },
        });
    };

    const handleSubmit = async () => {
        if (formStates.values.coreFaceIp === "") {
            toast.error(t("Core Face IP is required"));
            return;
        } else if (formStates.values.coreFaceId === "") {
            toast.error(t("Core Face ID is required"));
            return;
        } else if (formStates.values.topK <= 0) {
            toast.error(t("Top K must be greater than 0"));
            return;
        } else {
            var formData = new FormData();
            formData.append("image", formStates.values.image !== null ? formStates.values.image : imageFetched);
            formData.append("topK", formStates.values.topK);
            formData.append("coreFaceIp", formStates.values.coreFaceIp);
            formData.append("coreFaceId", formStates.values.coreFaceId);
            let result = await axios({
                method: 'post',
                headers: {
                    // Authorization: cookies.getAccessToken(),
                    'Content-Type': 'multipart/form-data'
                },
                url: `${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/face-search/v2`,
                data: formData
            }).then((res) => {
                setSearchResult(res.data);
                setCount(res.data.length);
                setResultModal(true);
            }).catch((err) => {
                toast.error(err.response.data.error);
            });
        }
    };

    //#region column
    const renderUserAvatar = row => {
        if (row.User?.avatar) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${row.User?.avatar}`} />
        } else null;
    };

    const column = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '350px',
            sortField: 'fullName',
            selector: row => row.fullName,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center cursor-pointer'>
                    {renderUserAvatar(row)}
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.User?.name}</span>
                        <small className='text-truncate text-muted mb-0'>{row.User?.email}</small>
                        <small className='text-truncate text-muted mb-0'>{row.userId}</small>
                    </div>
                </div>
            )
        },
        {
            name: t('Company'),
            minWidth: '300px',
            sortable: true,
            sortField: 'company',
            selector: row => row.Company.name,
            cell: row => <span className='text-capitalize'>{row.User?.Company?.name}</span>
        },
        {
            name: t('Department'),
            sortable: true,
            minWidth: '300px',
            sortField: 'department',
            selector: row => row.Department.name,
            cell: row => <span className='text-capitalize'>{row.User?.Department?.name}</span>
        },
        {
            name: t('Confidence'),
            sortable: true,
            minWidth: '100px',
            sortField: 'conf',
            selector: row => row.conf,
            cell: row => <span className='fw-bolder'>{Number(row.conf).toFixed(2)}</span>
        },
    ];
    //#endregion

    //#region custom pagination
    const indexOfLastItem = page * perPage;
    const indexOfFirstItem = indexOfLastItem - perPage;
    const finalData = searchResult?.slice(indexOfFirstItem, indexOfLastItem);
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => setPage(e.selected + 1)}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    const closeModal = () => {
        afterClose();
        setFormStates({
            isValid: false,
            values: {
                image: null,
                topK: 50,
                coreFaceIp: "",
                coreFaceId: "",
            },
            errors: {}
        })
    };

    return (
        <Fragment>
            <Modal
                isOpen={showSearchUserInDBModal}
                toggle={() => dispatch(handleShowSearchUserInDBModal(!showSearchUserInDBModal))}
                onClosed={() => closeModal()}
                className="modal-dialog-centered"
            >
                <ModalHeader toggle={() => dispatch(handleShowSearchUserInDBModal(!showSearchUserInDBModal))}>
                    {t("Search User In Database")}
                </ModalHeader>
                <ModalBody>
                    {image === "" ? (
                        <div className="mb-1">
                            <Label className="form-label" for="image">
                                {t("Image")}
                            </Label>
                            <Input
                                id="image"
                                name="image"
                                type="file"
                                className="form-control"
                                accept="image/*"
                                onChange={(e) => {
                                    setFormStates({
                                        ...formStates,
                                        values: {
                                            ...formStates.values,
                                            image: e.target.files[0],
                                        },
                                    });
                                }}
                            />
                        </div>
                    ) : null}
                    <div className="mb-1">
                        <Label className="form-label" for="topK">
                            {t("Top K")}
                        </Label>
                        <Input
                            id="topK"
                            name="topK"
                            type="number"
                            className="form-control"
                            min="1"
                            value={formStates.values.topK}
                            onChange={handleChange}
                        />
                    </div>
                    <div className="mb-1">
                        <Label className="form-label" for="coreFaceIp">
                            {t("Core Face IP")}
                        </Label>
                        <Input
                            id="coreFaceIp"
                            name="coreFaceIp"
                            type="text"
                            className="form-control"
                            value={formStates.values.coreFaceIp}
                            onChange={handleChange}
                            placeholder={t("Core Face IP")}
                        />
                    </div>
                    <div className="mb-1">
                        <Label className="form-label" for="coreFaceId">
                            {t("Core Face ID")}
                        </Label>
                        <Input
                            id="coreFaceId"
                            name="coreFaceId"
                            type="text"
                            className="form-control"
                            value={formStates.values.coreFaceId}
                            onChange={handleChange}
                            placeholder={t("Core Face ID")}
                        />
                    </div>
                </ModalBody>
                <ModalFooter>
                    <Button color="primary" onClick={() => handleSubmit()}>
                        {t("Submit")}
                    </Button>
                    <Button color="secondary" outline onClick={() => dispatch(handleShowSearchUserInDBModal(!showSearchUserInDBModal))}>
                        {t("Discard")}
                    </Button>
                </ModalFooter>
            </Modal>

            <Modal
                isOpen={resultModal}
                toggle={() => setResultModal(!resultModal)}
                className="modal-dialog-centered modal-lg"
                style={{ maxWidth: '65%' }}
            >
                <ModalHeader toggle={() => setResultModal(!resultModal)}>
                    {t("Search Result")}
                </ModalHeader>
                <ModalBody>
                    <div className='react-dataTable'>
                        <DataTable
                            noHeader
                            subHeader
                            pagination
                            responsive
                            paginationServer
                            columns={column}
                            className='react-dataTable'
                            paginationComponent={CustomPagination}
                            data={finalData}
                            pointerOnHover
                            highlightOnHover
                            subHeaderComponent={
                                <div className='invoice-list-table-header w-100 me-1 ms-50 my-1'>
                                    <Row>
                                        <Col xl='6' className='d-flex align-items-center p-0'>
                                            <div className='d-flex align-items-center w-100'>
                                                <label htmlFor='rows-per-page'>{t("Show")}</label>
                                                <Input
                                                    className='mx-50'
                                                    type='select'
                                                    id='rows-per-page'
                                                    value={perPage}
                                                    onChange={(e) => {
                                                        setPerPage(parseInt(e.target.value));
                                                        setPage(1);
                                                    }}
                                                    style={{ width: '5rem' }}
                                                >
                                                    <option value='10'>10</option>
                                                    <option value='15'>15</option>
                                                    <option value='20'>20</option>
                                                </Input>
                                                <label htmlFor='rows-per-page'>{t("Entries")}</label>
                                            </div>
                                        </Col>
                                    </Row>
                                </div>
                            }
                            onRowClicked={(row) => {
                                dispatch(handleSelectedUserId(row.userId));
                                dispatch(handleShowUserProfileModal(!showUserProfileModal));
                            }}
                        />
                    </div>
                </ModalBody>
            </Modal>
        </Fragment>
    )
};

export default SearchUserInDBModal;