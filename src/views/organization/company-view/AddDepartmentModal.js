import { useState, useEffect, Fragment } from 'react';
import { Button, Label, Input, Modal, ModalHeader, ModalBody, ModalFooter } from 'reactstrap';
import { useLazyQuery, useMutation } from '@apollo/client';
import { GET_ALL_COMPANY, GET_ALL_DEPARTMENT_IN_COMPANY } from '../../../apolo/graphql/Company';
import { ADD_DEPARTMENT } from '../../../apolo/graphql/Department';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { handleShowAddDepartmentModal } from './store';

const AddDepartmentModal = ({ afterClose }) => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let showAddDepartmentModal = useSelector(state => state.companyView.showAddDepartmentModal);
    const [formState, setFormState] = useState({
        isValid: false,
        values: {
            name: "",
            companyId: "-1",
            parentId: "-1",
        },
        errors: {
            name: ""
        },
    });
    //#endregion

    //#region query company list
    const [companies, setCompanies] = useState([]);
    const [query, companyQuery] = useLazyQuery(GET_ALL_COMPANY, {
        variables: {
            name: ""
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (companyQuery.loading) return;
        if (!companyQuery.data) return;
        setCompanies(companyQuery?.data?.companies);
    }, [companyQuery]);

    const [onSelectCompany, departmentQuery] = useLazyQuery(GET_ALL_DEPARTMENT_IN_COMPANY);

    useEffect(() => {
        if (showAddDepartmentModal) {
            query({
                variables: {
                    name: ""
                }
            });
        }
    }, [showAddDepartmentModal]);
    //#endregion

    //#region handle submit
    const [addDep, { }] = useMutation(ADD_DEPARTMENT);
    const handleSubmit = () => {
        if (formState.values.companyId === "-1") {
            toast.error("Please select a company");
            return;
        } else if (formState.values.name === "") {
            toast.error("Please enter a department name");
            return;
        }
        addDep({
            variables: {
                name: formState.values.name,
                companyId: formState.values.companyId,
                parentId: formState.values.parentId === "-1" ? "-1" : formState.values.parentId
            }
        }).then(() => {
            toast.success("Added Successfully");
            dispatch(handleShowAddDepartmentModal(!showAddDepartmentModal));
        }).catch(e => {
            toast.error(e?.graphQLErrors[0]?.message || e?.message);
        })
    };
    //#endregion

    //#region handle functions
    const handleChange = (event) => {
        setFormState((formState) => ({
            ...formState,
            values: {
                ...formState.values,
                [event.target.name]: event.target.value,
            },
        }));
    };

    function closeModal() {
        setFormState({
            isValid: false,
            values: {
                name: "",
                companyId: "-1",
                parentId: "-1",
            },
            errors: {
                name: ""
            },
        });
        afterClose();
    };
    //#endregion

    return (
        <Modal
            isOpen={showAddDepartmentModal}
            toggle={() => dispatch(handleShowAddDepartmentModal(!showAddDepartmentModal))}
            onClosed={() => closeModal()}
            className='modal-dialog-centered'
        >
            <ModalHeader toggle={() => dispatch(handleShowAddDepartmentModal(!showAddDepartmentModal))} >
                {t("Add Department")}
            </ModalHeader>
            <ModalBody>
                <div className='mb-1'>
                    <Label className='form-label' for='departmentName'>
                        {t("New Department Name")}
                    </Label>
                    <Input
                        type='text'
                        name='name'
                        id='departmentName'
                        value={formState.values.name}
                        onChange={handleChange}
                        placeholder='New Department Name'
                    />
                </div>

                <div className='mb-1'>
                    <Label className='form-label' for='companyId'>
                        {t("Company")}
                    </Label>
                    <Select
                        id="companyId"
                        name="companyId"
                        className='react-select'
                        classNamePrefix='select'
                        isClearable={false}
                        theme={selectThemeColors}
                        options={companies.map(el => {
                            return {
                                value: el.id,
                                label: el.name
                            }
                        })}
                        onChange={(company) => {
                            setFormState({
                                ...formState,
                                values: {
                                    ...formState.values,
                                    companyId: company?.value !== undefined && company?.value !== null ? company.value : ""
                                }
                            })
                            onSelectCompany({
                                variables: {
                                    companyId: company.value
                                }
                            });
                        }}
                    />
                </div>

                <div className='mb-1'>
                    <Label className='form-label' for="parentId">
                        {t("Sub-Department Of")}
                    </Label>
                    <Input
                        type='select'
                        id='parentId'
                        name='parentId'
                        className='form-control'
                        value={formState.values.parentId}
                        onChange={handleChange}
                    >
                        <option value="-1"> NAN </option>
                        {departmentQuery.loading ? null : (departmentQuery.data
                            ? departmentQuery.data.departmentsInCompany.map((dep) => {
                                return <Fragment key={dep.id}>
                                    <option value={dep.id}>{dep.name}</option>
                                </Fragment>
                            }) : null)}
                    </Input>
                </div>
            </ModalBody>
            <ModalFooter>
                <Button color='primary' onClick={() => handleSubmit()}>
                    {t("Submit")}
                </Button>
                <Button color='secondary' outline onClick={() => dispatch(handleShowAddDepartmentModal(!showAddDepartmentModal))}>
                    {t("Discard")}
                </Button>
            </ModalFooter>
        </Modal>
    )
};

export default AddDepartmentModal;