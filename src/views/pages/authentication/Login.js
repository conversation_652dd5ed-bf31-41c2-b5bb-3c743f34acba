import { useContext, useEffect, useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSkin } from '@hooks/useSkin';
import { useDispatch } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { handleLogin } from '@store/authentication';
import { AbilityContext } from '@src/utility/context/Can';
import InputPasswordToggle from '@components/input-password-toggle';
import { getHomeRouteForLoggedInUser } from '@utils';
import { Row, Col, Form, Input, Label, Button, CardTitle, FormText } from 'reactstrap';
import illustrationsLight from '@src/assets/images/pages/login-v2.svg';
import illustrationsDark from '@src/assets/images/pages/login-v2-dark.svg';
import '@styles/react/pages/page-authentication.scss';
import { useLazyQuery } from '@apollo/client';
import { LOGIN_BY_IAM, LOGIN_QUERY } from '../../../apolo/graphql/User';
import logo from '@src/assets/images/logo/logo <EMAIL>';
// import CryptoJS from "crypto-js";
// import { Buffer } from 'buffer';
import * as forge from 'node-forge';
import axios from 'axios';
import { useKeycloak } from '@src/contexts/KeycloakContext';
import { useLoading } from '@src/contexts/LoadingContext';

const defaultValues = {
    password: '',
    loginEmail: ''
}

const Login = () => {

    //#region states
    const { skin } = useSkin();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const ability = useContext(AbilityContext);
    const [lockTime, setLockTime] = useState(0);
    const [intervalId, setIntervalId] = useState(null);
    const { keycloak, isInitialized, isAuthenticated } = useKeycloak();
    const { setLoading } = useLoading();
    const location = useLocation();

    const {
        control,
        setError,
        handleSubmit,
        formState: { errors }
    } = useForm({ defaultValues });

    const source = skin === 'dark' ? illustrationsDark : illustrationsLight;
    //#endregion

    //#region login query
    const [login, loginResult] = useLazyQuery(LOGIN_QUERY, {
        onCompleted: (loginData) => {
            const data = {
                ...loginData.login,
                accessToken: loginData.login.accessToken,
                refreshToken: loginData.login.refreshToken,
                loginMethod: 'traditional',
            };
            if (loginData.login?.domain) {
                data.domain = loginData.login.domain
            }
            dispatch(handleLogin(data));
            if (loginData.login?.ability) ability.update(loginData.login?.ability);
            navigate(getHomeRouteForLoggedInUser(loginData.login.roles[0]?.id));
        },
        onError: (err) => {
            if (err.message.includes('Login locked')) {
                const seconds = parseInt(err.message.match(/\d+/)[0], 10);
                setLockTime(seconds);
            } else {
                setError('loginEmail', {
                    type: 'manual',
                    message: err.message
                });
            }
        }
    });
    //#endregion

    //#region login iam query
    const [loginIam, loginIamResult] = useLazyQuery(LOGIN_BY_IAM, {
        onCompleted: (loginData) => {
            const data = {
                ...loginData.loginIam,
                accessToken: loginData.loginIam.accessToken,
                refreshToken: loginData.loginIam.refreshToken,
                loginMethod: 'iam',
            };
            if (loginData.loginIam?.domain) {
                data.domain = loginData.loginIam.domain
            }
            dispatch(handleLogin(data));
            if (loginData.loginIam?.ability) ability.update(loginData.loginIam?.ability);

            setTimeout(() => {
                setLoading(false);
                navigate(getHomeRouteForLoggedInUser(loginData.loginIam.roles[0]?.id));
            }, 100);
        },
        onError: (err) => {
            setLoading(false);
            if (err.message) {
                keycloak?.logout({
                    redirectUri: `${window.location.origin}/login?iamError=${encodeURIComponent(err.message)}`
                });
            }
        }
    });

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const iamError = params.get('iamError');
        if (iamError) {
            setError('loginEmail', {
                type: 'manual',
                message: iamError
            });
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }, [location.search, setError]);
    //#endregion

    //#region check real time lock login
    useEffect(() => {
        if (lockTime > 0) {
            const id = setInterval(() => {
                setLockTime(prev => {
                    if (prev <= 1) {
                        clearInterval(id);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
            setIntervalId(id);
        };

        return () => {
            if (intervalId) clearInterval(intervalId);
        };
    }, [lockTime]);
    //#endregion

    //#region check iam user data
    const fetchUserProfile = async () => {
        setLoading(true);
        if (!keycloak || !keycloak.token || !keycloak.refreshToken) {
            console.error('Keycloak not initialized or tokens not available.');
            return;
        }
        const accessToken = keycloak.token;
        const refreshToken = keycloak.refreshToken;

        try {
            const response = await axios.get(`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/iam/profile`, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                },
            });

            const iamUserData = response.data;
            const finalUserData = {
                ...iamUserData,
                accessToken: accessToken,
                refreshToken: refreshToken,
            };

            loginIam({
                variables: {
                    email: finalUserData.email
                }
            }).catch((err) => {
                keycloak?.logout();
            });
        } catch (error) {
            console.error('Failed to fetch user profile', error);
            keycloak?.logout();
        } finally {
            setLoading(false);
        }
    };
    //#endregion

    //#region handle submit
    const onSubmit = data => {
        if (Object.values(data).every(field => field.length > 0)) {
            setLoading(true);
            let bufString = data.password;
            if (import.meta.env.VITE_PUBLIC_IS_LOGIN_ENCRYPT != 'false') {
                const pubPem = `-----BEGIN PUBLIC KEY-----
                MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvF6Jhimh2TgI9fKCTzkP
                GYTdRiBmefBh/KB5Ir75N3XE04UBb46Szhs265dywnd38KDqmiVAj6tzX6zYc56r
                TykRIT8FYaXbleKJoN2e7UuKFC/kL8byfC7Aa4W83Nh+DNHPypLtyCqwmIPs3Q9w
                j5/jG9n4w+lqmYuthoOvRhaTAcAlrTG+mAQ92NJf5cMmZP5hQBCTCeWYSkUj5tyl
                Mhhz3wJtn0qVh/kzdkyfiyom2K2cJvrAP3FuALL9ORj7uqdNPuy1DypKuk1C8D/F
                qzvCHm46o5iDQ60jMz9T380CzxrZMup/omvqHeAzpoQJ17oForiK7JGjtToMPLKy
                BQIDAQAB
                -----END PUBLIC KEY-----`;
                const pub2 = forge.pki.publicKeyFromPem(pubPem);
                const encrypted2 = pub2.encrypt(data.password);
                bufString = forge.util.encode64(encrypted2);
            }
            login({
                variables: {
                    email: data.loginEmail,
                    password: bufString,
                    type: 1
                }
            });
        } else {
            for (const key in data) {
                if (data[key].length === 0) {
                    setError(key, {
                        type: 'manual'
                    });
                }
            }
            setLoading(false);
        }
    };
    //#endregion

    useEffect(() => {
        if (isInitialized && isAuthenticated) {
            fetchUserProfile();
        }
    }, [isInitialized, isAuthenticated]);

    return (
        <div className='auth-wrapper auth-cover'>
            <Row className='auth-inner m-0'>
                <Link className='brand-logo' href="@src/favicon.ico" to='/' onClick={e => e.preventDefault()}>
                    <img src={logo} height='28' />
                    <h2 className='brand-text text-primary ms-1'>CIVAMS</h2>
                </Link>
                <Col className='d-none d-lg-flex align-items-center p-5' lg='8' sm='12'>
                    <div className='w-100 d-lg-flex align-items-center justify-content-center px-5'>
                        <img className='img-fluid' src={source} alt='Login Cover' />
                    </div>
                </Col>
                <Col className='d-flex align-items-center auth-bg px-2 p-lg-5' lg='4' sm='12'>
                    <Col className='px-xl-2 mx-auto' sm='8' md='6' lg='12'>
                        <CardTitle tag='h2' className='fw-bold mb-1'>
                            CIVAMS
                        </CardTitle>
                        <Form className='auth-login-form mt-2' onSubmit={handleSubmit(onSubmit)}>
                            <div className='mb-1'>
                                <Label className='form-label' for='login-email'>
                                    Email
                                </Label>
                                <Controller
                                    id='loginEmail'
                                    name='loginEmail'
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            autoFocus
                                            type='email'
                                            placeholder='Username|Email'
                                            invalid={errors.loginEmail && true}
                                            {...field}
                                        />
                                    )}
                                />
                                {errors.loginEmail && <FormText color='danger'>{errors.loginEmail.message}</FormText>}
                                {lockTime > 0 && <FormText color='danger'>Login locked for {lockTime} seconds.</FormText>}
                            </div>
                            <div className='mb-1'>
                                <div className='d-flex justify-content-between'>
                                    <Label className='form-label' for='login-password'>Password</Label>
                                    <Link to='/forgot-password'><small>Forgot Password?</small></Link>
                                </div>
                                <Controller
                                    id='password'
                                    name='password'
                                    control={control}
                                    render={({ field }) => (
                                        <InputPasswordToggle className='input-group-merge' invalid={errors.password && true} {...field} />
                                    )}
                                />
                            </div>
                            <div className='form-check mb-1'>
                                <Input type='checkbox' id='remember-me' />
                                <Label className='form-check-label' for='remember-me'>Remember Me</Label>
                            </div>
                            <Button type='submit' color='primary' block disabled={lockTime > 0}>Sign in</Button>

                            <div className='divider my-2'>
                                <div className='divider-text'>or</div>
                            </div>

                            <Button color='primary' block onClick={() => keycloak?.login()} disabled={!isInitialized}>Sign in with ATI IAM</Button>
                        </Form>
                    </Col>
                </Col>
            </Row>
        </div>
    );
};

export default Login;