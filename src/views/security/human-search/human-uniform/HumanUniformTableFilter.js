import { Fragment, useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { SEARCH_ALL_DEVICE } from "@src/apolo/graphql/Device";
import { GET_CAMERA_PAGE } from "../../../../apolo/graphql/Camera";
import { SEARCH_LOCATION } from "../../../../apolo/graphql/Location";
import { FIND_DEPARTMENTS, GET_ALL_COMPANY } from "../../../../apolo/graphql/Company";
import { SEARCH_USER_NEW } from "../../../../apolo/graphql/User";
import { Button, Col, Input, Label, Row } from "reactstrap";
import { BsRepeat } from "react-icons/bs";
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { isUserLoggedIn } from '@utils';
import { TreeSelect } from 'antd';
import pdfMake from "pdfmake/build/pdfmake";
(async () => {
    const pdfFonts = await import("pdfmake/build/vfs_fonts");
    pdfMake.vfs = pdfFonts.pdfMake.vfs;
})();
import { toast } from 'react-hot-toast';
import { selectThemeColors } from '@utils';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import { handlePage, handlePerPage, handleStartSearchDate, handleEndSearchDate, handleDeviceIds, handleDeviceTypes, handleCameraIds, handleIsFaceRecognizedOnly, handleAttFilterMode, handleIsExport, handleLocationIds, handleIsSocketActive, handleComIds, handleDepIds, handleUserIds, handleViolationMode, handleTimeViolationMode, handleLoading } from './store';
import DevicesConstants from '@src/constants/DevicesConstants';
import HumanUniformConstants from "@src/constants/HumanUniformConstants";
import moment from 'moment';

const HumanUniformTableFilter = ({ humanUniformInfos, tableData }) => {

    //#region states
    const HumanUniformAttFilterMode = HumanUniformConstants.NonUniformFilterTypeEnum;
    const ViolationMode = HumanUniformConstants.ViolationModeEnum;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    const DeviceStatus = DevicesConstants.DeviceStatus;
    const DeviceType = DevicesConstants.DeviceType;
    let startSearchDate = useSelector(state => state.humanUniform.startSearchDate);
    let endSearchDate = useSelector(state => state.humanUniform.endSearchDate);
    let perPage = useSelector(state => state.humanUniform.perPage);
    let deviceTypes = useSelector(state => state.humanUniform.deviceTypes);
    let comIds = useSelector(state => state.humanUniform.comIds);
    let depIds = useSelector(state => state.humanUniform.depIds);
    let isFaceRecognizedOnly = useSelector(state => state.humanUniform.isFaceRecognizedOnly);
    let attFilterMode = useSelector(state => state.humanUniform.attFilterMode);
    let violationMode = useSelector(state => state.humanUniform.violationMode);
    let timeViolationMode = useSelector(state => state.humanUniform.timeViolationMode);
    let isSocketActive = useSelector(state => state.humanUniform.isSocketActive);
    //#endregion

    //#region get user role
    const [userData, setUserData] = useState(null);
    useEffect(() => {
        if (isUserLoggedIn() !== null) {
            setUserData(JSON.parse(localStorage.getItem('userData')))
        }
    }, []);
    let userRole = userData ? userData?.roles[0].id : null;
    //#endregion

    //#region query all devices
    const [devices, setDevices] = useState([]);
    const allDevices = useQuery(SEARCH_ALL_DEVICE, {
        variables: {
            deviceTypes: deviceTypes,
            deviceStatus: DeviceStatus.Active,
            deviceUpdateStatus: DeviceStatus.All,
            search: ""
        },
        fetchPolicy: 'no-cache',
        nextFetchPolicy: 'no-cache',
    });

    useEffect(() => {
        if (allDevices.loading) return;
        if (!allDevices.data) return;
        setDevices(allDevices?.data?.searchAllDevice);
    }, [allDevices]);
    //#endregion

    //#region query camera
    const [searchCamera, setSearchCamera] = useState("");
    const [pageCamera, setPageCamera] = useState(1);
    const [perPageCamera, setPerPageCamera] = useState(10);
    const [count, setCount] = useState(0);
    const [cameraList, setCameraList] = useState([]);
    const getCamera = useQuery(GET_CAMERA_PAGE, {
        variables: {
            search: searchCamera,
            page: pageCamera,
            perPage: perPageCamera,
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (getCamera.loading) return;
        if (!getCamera.data) return;
        setCameraList(getCamera?.data?.cameraPage);
        setCount(getCamera?.data?.cameraPage[0]?.count ? getCamera?.data?.cameraPage[0]?.count : 0);
    }, [getCamera]);
    //#endregion

    //#region query location list
    const [locationSearch, setLocationSearch] = useState("");
    const [pageLocation, setPageLocation] = useState(1);
    const [perPageLocation, setPerPageLocation] = useState(10);
    const [countLocation, setCountLocation] = useState(0);
    const [locationList, setLocationList] = useState([]);
    const queryLocation = useQuery(SEARCH_LOCATION, {
        variables: {
            search: locationSearch,
            page: pageLocation,
            perPage: perPageLocation
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (queryLocation.loading) return;
        if (!queryLocation.data) return;
        setLocationList(queryLocation?.data?.searchLocation);
        setCountLocation(queryLocation?.data?.searchLocation[0]?.count ? queryLocation?.data?.searchLocation[0]?.count : 0);
    }, [queryLocation]);
    //#endregion

    //#region query user filter options
    const [comList, setComList] = useState([]);
    const companyQuery = useQuery(GET_ALL_COMPANY, {
        variables: {
            name: ""
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (companyQuery.loading) return;
        if (!companyQuery.data) return;
        setComList(companyQuery?.data?.companies);
    }, [companyQuery]);

    const [depList, setDepList] = useState([]);
    const getAllDepartment = useQuery(FIND_DEPARTMENTS, {
        variables: {
            query: "",
            companyIds: comIds
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (getAllDepartment.loading) return;
        if (!getAllDepartment.data) return;
        setDepList(getAllDepartment?.data?.findDepartments);
    }, [getAllDepartment]);
    //#endregion

    //#region query user list options
    const [userSearch, setUserSearch] = useState("");
    const [userSearchPage, setUserSearchPage] = useState(1);
    const [userSearchPerPage, setUserSearchPerPage] = useState(10);
    const [userCount, setUserCount] = useState(0);
    const [userList, setUserList] = useState([]);
    const userQuery = useQuery(SEARCH_USER_NEW, {
        variables: {
            "input": {
                "query": userSearch,
                "pageIndex": userSearchPage,
                "pageSize": userSearchPerPage,
                "departmentIds": depIds,
                "companyIds": comIds,
                "status": 0,
            }
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (userQuery.loading) return;
        if (!userQuery.data) return;
        setUserList(userQuery?.data?.search);
        setUserCount(userQuery?.data?.search[0]?.count);
    }, [userQuery]);
    //#endregion

    //#region handle export time violation mode
    const handleExport = (tableData) => {
        dispatch(handleLoading(true));
        if (tableData.length === 0) {
            toast.error(t("No data to export"));
            dispatch(handleLoading(false));
            return;
        }

        const docDefinition = {
            pageSize: 'A4',
            pageOrientation: 'landscape',
            pageMargins: [40, 60, 40, 60],
            content: [
                { text: 'Time Violation', style: 'header', alignment: 'center' },
                {
                    table: {
                        headerRows: 1,
                        widths: ['20%', '20%', '15%', '15%', '15%', '15%'],
                        body: [
                            [
                                { text: t('Name'), bold: true, fillColor: '#CCCCCC' },
                                { text: 'Email', bold: true, fillColor: '#CCCCCC' },
                                { text: t('Company'), bold: true, fillColor: '#CCCCCC' },
                                { text: t('Department'), bold: true, fillColor: '#CCCCCC' },
                                { text: t('First In'), bold: true, fillColor: '#CCCCCC' },
                                { text: t('Last Out'), bold: true, fillColor: '#CCCCCC' }
                            ],
                            ...tableData.map(user => [
                                { text: user.name || '-', fontSize: 8 },
                                { text: user.email || '-', fontSize: 8 },
                                { text: user.Company?.name || '-', fontSize: 8 },
                                { text: user.Department?.name || '-', fontSize: 8 },
                                { text: user.firstIn ? moment(user.firstIn).format('DD/MM/YYYY HH:mm') : '-', fontSize: 8 },
                                { text: user.lastOut ? moment(user.lastOut).format('DD/MM/YYYY HH:mm') : '-', fontSize: 8 }
                            ])
                        ]
                    },
                    layout: {
                        hLineWidth: (i, node) => 0.5,
                        vLineWidth: (i, node) => 0.5,
                        hLineColor: (i, node) => '#ccc',
                        vLineColor: (i, node) => '#ccc',
                        paddingTop: (i, node) => 4,
                        paddingBottom: (i, node) => 4
                    }
                }
            ],
            defaultStyle: {
                font: 'Roboto',
                fontSize: 8,
            }
        };

        pdfMake.createPdf(docDefinition).download("time-violation.pdf");
        setTimeout(() => {
            dispatch(handleLoading(false));
        }, 2000);
    };
    //#endregion

    return (
        <div className='d-flex flex-column gap-1 invoice-list-table-header my-1 w-100'>
            {userRole === "SUPER_ADMIN" || userRole === "ADMIN" ? (
                <Row>
                    <Col md='3'>
                        <Label className='form-label' for='date-from'>{t("From")}</Label>
                        <Flatpickr
                            value={startSearchDate}
                            id='date-from'
                            className='form-control text-center'
                            data-enable-time
                            onChange={date => {
                                dispatch(handleStartSearchDate(date[0]));
                                dispatch(handlePage(1));
                            }}
                            options={{
                                mode: 'single',
                                time_24hr: true,
                                dateFormat: 'd/m/Y h:i',
                            }}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className='form-label' for='date-to'>{t("To")}</Label>
                        <Flatpickr
                            id='date-to'
                            value={endSearchDate}
                            className='form-control text-center'
                            data-enable-time
                            onChange={date => {
                                dispatch(handleEndSearchDate(date[0]));
                                dispatch(handlePage(1));
                            }}
                            options={{
                                mode: 'single',
                                time_24hr: true,
                                dateFormat: 'd/m/Y h:i',
                            }}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className="form-label" for='selectedLocation'>{t("Location(s)")}</Label>
                        <Select
                            id="selectedLocation"
                            instanceId="selectedLocation"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isMulti={true}
                            isClearable={false}
                            options={locationList.map(el => {
                                return {
                                    value: el.id,
                                    label: el.name
                                }
                            })}
                            onInputChange={(input) => setLocationSearch(input)}
                            onMenuScrollToBottom={() => {
                                if (perPageLocation < countLocation && queryLocation.loading === false) {
                                    setPerPageLocation(perPageLocation + 10)
                                }
                            }}
                            onChange={(el) => {
                                dispatch(handleLocationIds(el.map(el => el.value)));
                                dispatch(handlePage(1));
                            }}
                            onMenuClose={() => setPerPageLocation(10)}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className="form-label" for='selectCamera'>{t("Camera(s)")}</Label>
                        <Select
                            id="selectCamera"
                            instanceId="selectCamera"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isMulti={true}
                            isClearable={false}
                            options={cameraList.map(el => {
                                return {
                                    value: el.id,
                                    label: el.name
                                }
                            })}
                            onInputChange={(input) => setSearchCamera(input)}
                            onMenuScrollToBottom={() => {
                                if (perPage < count && getCamera.loading === false) {
                                    setPerPageCamera(perPage + 10)
                                }
                            }}
                            onChange={(el) => {
                                dispatch(handleCameraIds(el.map(el => el.value)));
                                dispatch(handlePage(1));
                            }}
                            onMenuClose={() => setPerPageCamera(10)}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className='form-label' for='violationMode'>{t("Violation Mode")}</Label>
                        <TreeSelect
                            id='violationMode'
                            style={{
                                width: '100%',
                            }}
                            value={violationMode ? (timeViolationMode ? timeViolationMode : violationMode) : timeViolationMode}
                            treeDefaultExpandAll
                            onChange={(value) => {
                                if (value === 'lateOrNotIn' || value === 'late' || value === 'earlyOrNotOut' || value === 'early') {
                                    dispatch(handleTimeViolationMode(value));
                                    dispatch(handlePage(1));
                                } else if (value === 4) {
                                    dispatch(handleViolationMode(parseInt(value)));
                                    dispatch(handleTimeViolationMode(null));
                                } else {
                                    dispatch(handleViolationMode(parseInt(value)));
                                    dispatch(handleTimeViolationMode(null));
                                    dispatch(handlePage(1));
                                }
                            }}
                            treeData={Object.keys(ViolationMode).map(el => {
                                return {
                                    value: el,
                                    label: t(ViolationMode[el])
                                }
                            }).concat([
                                {
                                    title: t('Time Violation Only'),
                                    value: 4,
                                    children: [
                                        {
                                            title: t('Late or not in'),
                                            value: 'lateOrNotIn'
                                        },
                                        {
                                            title: t('Late'),
                                            value: 'late'
                                        },
                                        {
                                            title: t('Early or not out'),
                                            value: 'earlyOrNotOut'
                                        },
                                        {
                                            title: t('Early'),
                                            value: 'early'
                                        },
                                    ]
                                },
                            ])}
                            placeholder={t("Select...")}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className='form-label' for='multiSelectCom'>{t("Company(s)")}</Label>
                        <Select
                            id="multiSelectCom"
                            instanceId="multiSelectCom"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={true}
                            options={comList.map(el => {
                                return {
                                    value: el.id,
                                    label: el.name
                                }
                            })}
                            onChange={(el) => {
                                if (el !== null && el !== undefined) {
                                    dispatch(handleComIds(el.map(el => el.value)));
                                    dispatch(handlePage(1));
                                }
                            }}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className='form-label' for='multiSelectDept'>{t("Department(s)")}</Label>
                        <Select
                            id="multiSelectDept"
                            instanceId="multiSelectDept"
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={true}
                            options={depList.map(el => {
                                return {
                                    value: JSON.stringify({ id: el.id, company: el.company.name, }),
                                    companyName: el.company.name,
                                    companyId: el.company.id,
                                    label: el.name + " - " + el?.company?.name
                                }
                            })}
                            onChange={(departments) => {
                                let ids = departments.map((el) => {
                                    let value = JSON.parse(el.value)
                                    return value.id
                                })
                                dispatch(handleDepIds(ids));
                                dispatch(handlePage(1));
                            }}
                        />
                    </Col>

                    <Col md='3'>
                        <Label className='form-label' for='searchUser'>{t("User(s)")}</Label>
                        <Select
                            id='searchUser'
                            name='searchUser'
                            isClearable={false}
                            isMulti={true}
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            options={userList.map(el => {
                                return {
                                    value: JSON.stringify({
                                        id: el.id,
                                        email: el.email,
                                        integrationKey: el.integrationKey,
                                        department: el.department.name,
                                        company: el.company.name
                                    }),
                                    label: el.fullName + " - " + el.company.name
                                }
                            })}
                            onInputChange={(input) => {
                                setUserSearch(input);
                                setUserSearchPerPage(10);
                            }}
                            onMenuScrollToBottom={() => {
                                if (userSearchPerPage < userCount && userQuery.loading === false) {
                                    setUserSearchPerPage(userSearchPerPage + 10);
                                }
                            }}
                            onMenuClose={() => setUserSearchPerPage(10)}
                            onChange={(users) => {
                                if (users !== undefined && users !== null) {
                                    let ids = users.map((el) => {
                                        let value = JSON.parse(el.value)
                                        return value.id
                                    });
                                    dispatch(handleUserIds(ids));
                                    dispatch(handlePage(1));
                                }
                            }}
                            placeholder={"Name/Email/ID"}
                        />
                    </Col>

                    {/* <Col md='3'>
                    <Label className='form-label' for='deviceType-select'>{t("Device Type(s)")}</Label>
                    <Select
                        id='deviceType-select'
                        instanceId='deviceType-select'
                        className='react-select'
                        classNamePrefix='select'
                        theme={selectThemeColors}
                        isClearable={true}
                        isMulti={true}
                        options={Object.keys(DeviceType).map(el => {
                            return {
                                value: DeviceType[el],
                                label: t(DevicesConstants.DeviceTypeText[DeviceType[el]])
                            }
                        })}
                        onChange={(types) => {
                            dispatch(handleDeviceTypes(types.map(el => el.value)));
                        }}
                    />
                </Col>

                <Col md='3'>
                    <Label className='form-label' for='device-select'>{t("Device(s)")}</Label>
                    <Select
                        id='device-select'
                        className='react-select'
                        classNamePrefix='select'
                        theme={selectThemeColors}
                        isClearable={true}
                        isMulti={true}
                        options={devices.map(el => {
                            let timekeepingDevices = el.isTimekeepingDevice ? "* " + el.deviceName + " (Check In/Out)" : el.deviceName
                            return {
                                value: el.id,
                                label: timekeepingDevices
                            }
                        })}
                        onChange={(device) => {
                            dispatch(handleDeviceIds(device.map(el => el.value)));
                            dispatch(handlePage(1));
                        }}
                    />
                </Col> */}

                    {/* <Col md='3'>
                    <Label className='form-label' for='isFaceRecognizedOnly'>{t("Face Recognized")}</Label>
                    <Select
                        id='isFaceRecognizedOnly'
                        instanceId='isFaceRecognizedOnly'
                        className='react-select'
                        classNamePrefix='select'
                        theme={selectThemeColors}
                        isClearable={false}
                        isMulti={false}
                        options={[
                            { value: true, label: t("Yes") },
                            { value: false, label: t("No") },
                        ]}
                        value={{ value: isFaceRecognizedOnly, label: t(isFaceRecognizedOnly ? "Yes" : "No") }}
                        onChange={(el) => {
                            dispatch(handleIsFaceRecognizedOnly(el.value));
                            dispatch(handlePage(1));
                        }}
                    />
                </Col> */}

                    {/* <Col md='3'>
                    <Label className='form-label' for='attFilterMode'>{t("Att Filter Mode")}</Label>
                    <Select
                        id='attFilterMode'
                        instanceId='attFilterMode'
                        className='react-select'
                        classNamePrefix='select'
                        theme={selectThemeColors}
                        isClearable={false}
                        isMulti={false}
                        options={Object.keys(HumanUniformAttFilterMode).map(el => {
                            return {
                                value: el,
                                label: HumanUniformAttFilterMode[el]
                            }
                        })}
                        value={{ value: attFilterMode, label: HumanUniformAttFilterMode[attFilterMode] }}
                        onChange={(el) => {
                            dispatch(handleAttFilterMode(parseInt(el.value)));
                            dispatch(handlePage(1));
                        }}
                    />
                </Col> */}
                </Row>
            ) : (
                <Row>
                    <Col md='4'>
                        <Label className='form-label' for='date-from'>{t("From")}</Label>
                        <Flatpickr
                            value={startSearchDate}
                            id='date-from'
                            className='form-control text-center'
                            data-enable-time
                            onChange={date => {
                                dispatch(handleStartSearchDate(date[0]));
                                dispatch(handlePage(1));
                            }}
                            options={{
                                mode: 'single',
                                time_24hr: true,
                                dateFormat: 'd/m/Y h:i',
                            }}
                        />
                    </Col>

                    <Col md='4'>
                        <Label className='form-label' for='date-to'>{t("To")}</Label>
                        <Flatpickr
                            id='date-to'
                            value={endSearchDate}
                            className='form-control text-center'
                            data-enable-time
                            onChange={date => {
                                dispatch(handleEndSearchDate(date[0]));
                                dispatch(handlePage(1));
                            }}
                            options={{
                                mode: 'single',
                                time_24hr: true,
                                dateFormat: 'd/m/Y h:i',
                            }}
                        />
                    </Col>

                    <Col md='4'>
                        <Label className='form-label' for='violationMode'>{t("Violation Mode")}</Label>
                        <TreeSelect
                            id='violationMode'
                            style={{
                                width: '100%',
                            }}
                            value={violationMode ? (timeViolationMode ? timeViolationMode : violationMode) : timeViolationMode}
                            treeDefaultExpandAll
                            onChange={(value) => {
                                if (value === 'lateOrNotIn' || value === 'late' || value === 'earlyOrNotOut' || value === 'early') {
                                    dispatch(handleTimeViolationMode(value));
                                    dispatch(handlePage(1));
                                } else if (value === 4) {
                                    dispatch(handleViolationMode(parseInt(value)));
                                    dispatch(handleTimeViolationMode(null));
                                } else {
                                    dispatch(handleViolationMode(parseInt(value)));
                                    dispatch(handleTimeViolationMode(null));
                                    dispatch(handlePage(1));
                                }
                            }}
                            treeData={Object.keys(ViolationMode).map(el => {
                                return {
                                    value: el,
                                    label: t(ViolationMode[el])
                                }
                            }).concat([
                                {
                                    title: t('Time Violation Only'),
                                    value: 4,
                                    children: [
                                        {
                                            title: t('Late or not in'),
                                            value: 'lateOrNotIn'
                                        },
                                        {
                                            title: t('Late'),
                                            value: 'late'
                                        },
                                        {
                                            title: t('Early or not out'),
                                            value: 'earlyOrNotOut'
                                        },
                                        {
                                            title: t('Early'),
                                            value: 'early'
                                        },
                                    ]
                                },
                            ])}
                            placeholder={t("Select...")}
                        />
                    </Col>
                </Row>
            )}


            <Row>
                <Col xl='6' className='d-flex align-items-center'>
                    <div className='d-flex align-items-center w-100'>
                        <label htmlFor='rows-per-page'>{t("Show")}</label>
                        <Input
                            className='mx-50'
                            type='select'
                            id='rows-per-page'
                            value={perPage}
                            onChange={e => {
                                const value = parseInt(e.target.value);
                                dispatch(handlePerPage(value));
                                dispatch(handlePage(1));
                            }}
                            style={{ width: '5rem' }}
                        >
                            <option value='5'>5</option>
                            <option value='10'>10</option>
                            <option value='15'>15</option>
                        </Input>
                        <label htmlFor='rows-per-page'>{t("Entries")}</label>
                    </div>
                </Col>
                <Col xl='6' className='d-flex align-items-sm-center justify-content-xl-end justify-content-start flex-xl-nowrap flex-wrap flex-sm-row flex-column'>
                    <div className='d-flex align-items-center me-1'>
                        <Label className='form-check-label' for="isSocketActive">{t("Auto Refetch")}</Label>
                        <div className="form-check form-switch form-check-primary ms-1">
                            <Input
                                id="isSocketActive"
                                type="switch"
                                className="form-control"
                                checked={isSocketActive}
                                onChange={(e) => dispatch(handleIsSocketActive(e.target.checked))}
                            />
                        </div>
                        {!isSocketActive && (
                            <Button color="primary" size="sm" onClick={() => humanUniformInfos.refetch()}>
                                <BsRepeat size={18} />
                            </Button>
                        )}
                    </div>
                    {userRole === "SUPER_ADMIN" || userRole === "ADMIN" ?
                        <Button className="ms-1" color='primary' onClick={() => {
                            if (timeViolationMode) {
                                handleExport(tableData);
                            } else {
                                dispatch(handleIsExport(true));
                            };
                        }}>
                            {t("Export")}
                        </Button> : null
                    }
                </Col>
            </Row>
        </div>
    )
};

export default HumanUniformTableFilter;