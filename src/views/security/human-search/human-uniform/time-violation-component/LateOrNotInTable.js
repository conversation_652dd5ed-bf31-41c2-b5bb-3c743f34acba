import { useEffect, useState } from "react";
import { useLazyQuery } from "@apollo/client";
import { SEARCH_LATE_OR_NOT_IN } from "../../../../../apolo/graphql/SearchPerson";
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import moment from 'moment';
import Spinner from '@components/spinner/Loading-spinner';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleLoading, handlePage } from "../store";
import ApiUrlUserAvatarImage from "../../../../urs/all-users/ApiUrlUserAvatarImage";
import HumanUniformTableFilter from "../HumanUniformTableFilter";

const LateOrNotInTable = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let startSearchDate = useSelector(state => state.humanUniform.startSearchDate);
    let endSearchDate = useSelector(state => state.humanUniform.endSearchDate);
    let page = useSelector(state => state.humanUniform.page);
    let perPage = useSelector(state => state.humanUniform.perPage);
    let comIds = useSelector(state => state.humanUniform.comIds);
    let depIds = useSelector(state => state.humanUniform.depIds);
    let userIds = useSelector(state => state.humanUniform.userIds);
    let timeViolationMode = useSelector(state => state.humanUniform.timeViolationMode);
    let loading = useSelector(state => state.humanUniform.loading);
    const [count, setCount] = useState(0);
    //#endregion

    //#region query 
    const [lateOrNotInResult, setLateOrNotInResult] = useState([]);
    const [queryLateOrNotIn, getLateOrNotInResult] = useLazyQuery(SEARCH_LATE_OR_NOT_IN, {
        variables: {
            startDate: startSearchDate.toISOString(),
            endDate: endSearchDate.toISOString(),
            comIds: comIds,
            depIds: depIds,
            userIds: userIds,
            startHour: "08:30"
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (getLateOrNotInResult.loading) {
            dispatch(handleLoading(true));
            return;
        };
        dispatch(handleLoading(false));
        if (!getLateOrNotInResult.data) return;
        setLateOrNotInResult(getLateOrNotInResult?.data?.searchLateOrNotIn);
        setCount(getLateOrNotInResult?.data?.searchLateOrNotIn?.length);
    }, [getLateOrNotInResult]);

    useEffect(() => {
        if (timeViolationMode === 'lateOrNotIn') {
            queryLateOrNotIn({
                variables: {
                    startDate: startSearchDate.toISOString(),
                    endDate: endSearchDate.toISOString(),
                    comIds: comIds,
                    depIds: depIds,
                    userIds: userIds,
                    startHour: "08:00"
                }
            });
        }
    }, [timeViolationMode, startSearchDate, endSearchDate, comIds, depIds, userIds]);
    //#endregion

    //#region column
    const renderClient = row => {
        if (row.avatar) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${row.avatar}`} />
        }
        else {
            return null;
        }
    };

    const column = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '360px',
            sortField: 'name',
            selector: row => row.name,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center cursor-pointer'>
                    {renderClient(row)}
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.name}</span>
                        <small className='text-truncate text-muted mb-0'>{row.email}</small>
                    </div>
                </div>
            )
        },
        {
            name: t('Company'),
            minWidth: '200px',
            sortable: true,
            sortField: 'company',
            selector: row => row.Company.name,
            cell: row => <span className='text-capitalize'>{row.Company.name}</span>
        },
        {
            name: t('Department'),
            sortable: true,
            minWidth: '200px',
            sortField: 'department',
            selector: row => row.Department.name,
            cell: row => <span className='text-capitalize'>{row.Department.name}</span>
        },
        {
            name: t('First In'),
            minWidth: '150px',
            sortable: true,
            sortField: 'firstIn',
            selector: row => row.firstIn,
            cell: row => row.firstIn ? (
                <div className='d-flex flex-column'>
                    <span className='text-capitalize'>{moment(row.firstIn).format('DD/MM/YYYY')}</span>
                    <small className='text-muted'>{moment(row.firstIn).format('HH:mm:ss')}</small>
                </div>
            ) : null
        },
        {
            name: t('Last Out'),
            minWidth: '150px',
            sortable: true,
            sortField: 'lastOut',
            selector: row => row.lastOut,
            cell: row => row.lastOut ? (
                <div className='d-flex flex-column'>
                    <span className='text-capitalize'>{moment(row.lastOut).format('DD/MM/YYYY')}</span>
                    <small className='text-muted'>{moment(row.lastOut).format('HH:mm:ss')}</small>
                </div>
            ) : null
        }
    ];
    //#endregion

    //#region custom pagination
    const indexOfLastItem = page * perPage;
    const indexOfFirstItem = indexOfLastItem - perPage;
    const currentItems = lateOrNotInResult.slice(indexOfFirstItem, indexOfLastItem);
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={(e) => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <div className='react-dataTable'>
            <DataTable
                noHeader
                subHeader
                pagination
                responsive
                paginationServer
                columns={column}
                className='react-dataTable'
                paginationComponent={CustomPagination}
                data={currentItems}
                // pointerOnHover
                highlightOnHover
                subHeaderComponent={<HumanUniformTableFilter tableData={lateOrNotInResult} />}
                progressPending={loading}
                progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
            />
        </div>
    )
};

export default LateOrNotInTable;