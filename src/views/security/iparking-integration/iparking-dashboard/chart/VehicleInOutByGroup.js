import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { GET_VEHICLE_IN_OUT_BY_GROUP } from "../../../../../apolo/graphql/IParkingIntegration";
import { Card } from "reactstrap";
import { Calendar } from "react-feather";
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, CartesianGrid, ResponsiveContainer } from "recharts";
import Flatpickr from "react-flatpickr";
import "flatpickr/dist/flatpickr.min.css";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleEndTimeVehicleInOut, handleStartTimeVehicleInOut } from "../store";

const VehicleInOutByGroup = () => {

    //#region states
    const { t } = useTranslation("");
    const dispatch = useDispatch();
    const startTime = useSelector(state => state.iParkingDashboards.startTimeVehicleInOut);
    const endTime = useSelector(state => state.iParkingDashboards.endTimeVehicleInOut);
    const [totalIn, setTotalIn] = useState(0);
    const [totalOut, setTotalOut] = useState(0);
    //#endregion

    //#region query
    const [inOutData, setInOutData] = useState([]);
    const { data, loading } = useQuery(GET_VEHICLE_IN_OUT_BY_GROUP, {
        variables: {
            startTime: startTime,
            endTime: endTime
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (loading || !data) return;

        const eventIn = data.getVehicleInOutByGroup.eventIn;
        const eventOut = data.getVehicleInOutByGroup.eventOut;

        const groupNames = [...new Set([...eventIn.map(i => i.groupName), ...eventOut.map(i => i.groupName)])];

        const chartData = [
            { name: t("In") },
            { name: t("Out") }
        ];

        groupNames.forEach(group => {
            const inCount = eventIn.find(e => e.groupName === group)?.count || 0;
            const outCount = eventOut.find(e => e.groupName === group)?.count || 0;

            chartData[0][group] = inCount;
            chartData[1][group] = outCount;
        });

        setInOutData(chartData);

        // Calculate total in and out
        const totalInCount = eventIn.reduce((sum, item) => sum + item.count, 0);
        const totalOutCount = eventOut.reduce((sum, item) => sum + item.count, 0);
        setTotalIn(totalInCount);
        setTotalOut(totalOutCount);
    }, [data, loading]);
    //#endregion

    return (
        <Card className="p-2">
            <div className="d-flex justify-content-between align-items-center mb-2">
                <h5>{t("Event In/Out By Group")}: {t("In")}({totalIn}), {t("Out")}({totalOut})</h5>
                <div className="d-flex align-items-center justify-content-end">
                    <Flatpickr
                        className="form-control text-center w-50 border-0 fs-5"
                        value={moment(startTime).format("DD-MM-YYYY")}
                        options={{ dateFormat: "d-m-Y" }}
                        onChange={([day]) => {
                            dispatch(handleStartTimeVehicleInOut(moment(day).startOf('day').format("YYYY-MM-DD HH:mm:ss")));
                            dispatch(handleEndTimeVehicleInOut(moment(day).endOf('day').format("YYYY-MM-DD HH:mm:ss")));
                        }}
                    />
                    <Calendar />
                </div>
            </div>
            <ResponsiveContainer width="100%" height={300}>
                <BarChart
                    data={inOutData}
                    margin={{ top: 10, right: 20, left: 10, bottom: 5 }}
                    barGap={16}
                    barCategoryGap="25%"
                >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="THE UHF" fill="#0080FF" name="THE UHF" />
                    <Bar dataKey="Xe Dap" fill="#00D084" name="Xe Dap" />
                    <Bar dataKey="O To" fill="#FFA500" name="O To" />
                </BarChart>
            </ResponsiveContainer>
        </Card>
    );
};

export default VehicleInOutByGroup;