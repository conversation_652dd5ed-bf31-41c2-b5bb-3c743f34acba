import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { GET_VEHICLE_IN_OUT_BY_TIME } from "../../../../../apolo/graphql/IParkingIntegration";
import { Card } from "reactstrap";
import { Calendar } from "react-feather";
import { AreaChart, Area, XAxis, YAxis, Tooltip, Legend, CartesianGrid, ResponsiveContainer } from "recharts";
import Flatpickr from "react-flatpickr";
import "flatpickr/dist/flatpickr.min.css";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleEndTimeInOutByTime, handleStartTimeInOutByTime } from "../store";

const VehicleInOutByTime = () => {

    //#region states
    const { t } = useTranslation("");
    const dispatch = useDispatch();
    const startTime = useSelector(state => state.iParkingDashboards.startTimeInOutByTime);
    const endTime = useSelector(state => state.iParkingDashboards.endTimeInOutByTime);
    const [totalIn, setTotalIn] = useState(0);
    const [totalOut, setTotalOut] = useState(0);
    //#endregion

    //#region query
    const [inOutData, setInOutData] = useState([]);
    const { data, loading } = useQuery(GET_VEHICLE_IN_OUT_BY_TIME, {
        variables: {
            startTime,
            endTime
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (loading || !data) return;

        const eventIn = data.getVehicleInOutByTime.eventIn || [];
        const eventOut = data.getVehicleInOutByTime.eventOut || [];

        const mergedData = Array.from({ length: 24 }, (_, hour) => {
            const localHour = hour;
            const utcHour = (hour - 7 + 24) % 24;

            const inItem = eventIn.find(item => +item.groupName === utcHour);
            const outItem = eventOut.find(item => +item.groupName === utcHour);

            return {
                hour: `${localHour}h`,
                gui: inItem ? inItem.count : 0,
                lay: outItem ? outItem.count : 0
            };
        });

        setInOutData(mergedData);

        // Calculate total in and out
        const totalInCount = eventIn.reduce((sum, item) => sum + item.count, 0);
        const totalOutCount = eventOut.reduce((sum, item) => sum + item.count, 0);

        setTotalIn(totalInCount);
        setTotalOut(totalOutCount);
    }, [data, loading]);
    //#endregion

    return (
        <Card className="p-2">
            <div className="d-flex justify-content-between align-items-center mb-2">
                <h5>{t("Event In/Out By Time")}: {t("In")}({totalIn}), {t("Out")}({totalOut})</h5>
                <div className="d-flex align-items-center justify-content-end">
                    <Flatpickr
                        className="form-control text-center w-50 border-0 fs-5"
                        value={moment(startTime).format("DD-MM-YYYY")}
                        options={{ dateFormat: "d-m-Y" }}
                        onChange={([day]) => {
                            dispatch(handleStartTimeInOutByTime(moment(day).startOf("day").format("YYYY-MM-DD HH:mm:ss")));
                            dispatch(handleEndTimeInOutByTime(moment(day).endOf("day").format("YYYY-MM-DD HH:mm:ss")));
                        }}
                    />
                    <Calendar />
                </div>
            </div>
            <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={inOutData} margin={{ top: 10, right: 20, left: 10, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="gui" stroke="#00C49F" fill="#A8E6CF" name="Gửi xe" />
                    <Area type="monotone" dataKey="lay" stroke="#FF4C4C" fill="#FFB6B6" name="Lấy xe" />
                </AreaChart>
            </ResponsiveContainer>
        </Card>
    );
};

export default VehicleInOutByTime;