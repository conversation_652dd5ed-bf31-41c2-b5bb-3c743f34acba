import React, { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { SUMMARY_ALARM_EVENT } from "../../../apolo/graphql/Report";
import { Card } from "reactstrap";
import { Calendar } from "react-feather";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, Legend, CartesianGrid, ResponsiveContainer } from "recharts";
import Flatpickr from "react-flatpickr";
import "flatpickr/dist/flatpickr.min.css";
import moment from "moment";
import { useTranslation } from "react-i18next";
import AlertScriptTypeConstants from "@src/constants/AlertScriptTypeConstants";
import { handleEndDate, handleStartDate } from "./store";
import { useDispatch, useSelector } from "react-redux";

const AlarmEventSummary = () => {

    //#region states
    const AlertTypeEnum = AlertScriptTypeConstants.AlertType;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    const startDate = useSelector(state => state.securityReport.startDate);
    const endDate = useSelector(state => state.securityReport.endDate);
    //#endregion

    //#region query alarm event count
    const [alarmData, setAlarmData] = useState([]);
    const { data, loading, error } = useQuery(SUMMARY_ALARM_EVENT, {
        variables: {
            startDate: startDate,
            endDate: endDate
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (loading || error || !data) return;

        const mappedData = data.summaryAlarmEvent.map(event => ({
            ...event,
            type: AlertTypeEnum[event.type] || event.type
        }));

        setAlarmData(mappedData);
    }, [data, loading, error]);
    //#endregion

    return (
        <Card className="p-2">
            <div className="d-flex justify-content-between align-items-center mb-2">
                <h5>{t("Summary Alarm Event")}</h5>
                <div className="d-flex align-items-center">
                    <Flatpickr
                        id='selectTime'
                        className='form-control text-center border-0'
                        value={[startDate, endDate]}
                        data-enable-time
                        onChange={date => {
                            dispatch(handleStartDate(date[0]));
                            dispatch(handleEndDate(date[1]));
                        }}
                        options={{
                            mode: 'range',
                            dateFormat: 'd/m/Y',
                        }}
                    />
                    <Calendar />
                </div>
            </div>
            <ResponsiveContainer width="100%" height={300}>
                <BarChart data={alarmData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" fill="#35A6DC" name="Alarm Count" />
                </BarChart>
            </ResponsiveContainer>
        </Card>
    );
};

export default AlarmEventSummary;