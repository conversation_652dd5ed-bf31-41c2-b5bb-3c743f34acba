import { Fragment, useEffect, useState } from "react";
import { Col, Row } from "reactstrap";
import { useQuery } from "@apollo/client";
import { COUNT_ACTIVE_AISERVICE, COUNT_ACTIVE_CAMERA, COUNT_ACTIVE_ROI } from "../../../apolo/graphql/Report";
import StatsHorizontal from '@components/widgets/stats/StatsHorizontal';
import StatsVertical from '@components/widgets/stats/StatsVertical';
import { GiCctvCamera } from "react-icons/gi";
import { GrServices } from "react-icons/gr";
import { PiPolygon } from "react-icons/pi";
import { useTranslation } from "react-i18next";

const DeviceSummary = () => {

    //#region states
    const { t } = useTranslation('');
    //#endregion

    //#region active camera
    const [activeCamera, setActiveCamera] = useState(0);
    const activeCameraQuery = useQuery(COUNT_ACTIVE_CAMERA, {
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });
    useEffect(() => {
        if (activeCameraQuery.loading) return;
        if (!activeCameraQuery.data) return;
        setActiveCamera(activeCameraQuery?.data?.countActiveCamera);
    }, [activeCameraQuery]);
    //#endregion

    //#region active roi
    const [activeROI, setActiveROI] = useState(0);
    const activeROIQuery = useQuery(COUNT_ACTIVE_ROI, {
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });
    useEffect(() => {
        if (activeROIQuery.loading) return;
        if (!activeROIQuery.data) return;
        setActiveROI(activeROIQuery?.data?.countActiveROI);
    }, [activeROIQuery]);
    //#endregion

    //#region active roi
    const [activeAiService, setActiveAIService] = useState(0);
    const activeAiServiceQuery = useQuery(COUNT_ACTIVE_AISERVICE, {
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });
    useEffect(() => {
        if (activeAiServiceQuery.loading) return;
        if (!activeAiServiceQuery.data) return;
        setActiveAIService(activeAiServiceQuery?.data?.countActiveAiService);
    }, [activeAiServiceQuery]);
    //#endregion

    return (
        <Fragment>
            <Row className="match-height">
                <Col lg='4' sm='6' md='6'>
                    <StatsHorizontal
                        color='primary'
                        statTitle={t('Camera Active')}
                        icon={<GiCctvCamera size={23} />}
                        stats={activeCamera?.toString() || 0}
                    />
                </Col>
                <Col lg='4' sm='6' md='6'>
                    <StatsHorizontal
                        color='primary'
                        statTitle={t('ROI Active')}
                        icon={<PiPolygon size={23} />}
                        stats={activeROI?.toString() || 0}
                    />
                </Col>
                <Col lg='4' sm='6' md='6'>
                    <StatsHorizontal
                        color='primary'
                        statTitle={t("AI Service Active")}
                        icon={<GrServices size={23} />}
                        stats={activeAiService?.toString() || 0}
                    />
                </Col>
            </Row>
        </Fragment>
    )
};

export default DeviceSummary;