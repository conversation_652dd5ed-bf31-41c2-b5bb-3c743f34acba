import { createSlice } from '@reduxjs/toolkit';

const initState = () => {
    return {
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0),
        endDate: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59, 59),
    }
};

export const securityReportSlice = createSlice({
    name: 'securityReport',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleStartDate: (state, action) => {
            state.startDate = action.payload;
        },
        handleEndDate: (state, action) => {
            state.endDate = action.payload;
        },
    },
})

export const {
    handleInit,
    handleStartDate,
    handleEndDate,
} = securityReportSlice.actions

export default securityReportSlice.reducer;