import { Fragment, useEffect, useState } from "react";
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import { Trash2 } from "react-feather";
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { useLazyQuery, useMutation } from "@apollo/client";
import { GET_MQTT_CONFIG_DEVICES, TOGGLE_MQTT_CONFIG_DEVICES } from "../../../apolo/graphql/Server";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleConfigDeviceCount, handleConfigDevicePage } from "./store";
import DeviceConfigTableFilter from "./DeviceConfigTableFilter";
import AddConfigDeviceModal from "./AddConfigDeviceModal";

const DeviceConfig = () => {

    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let detailObj = useSelector(state => state.mqtt.detailObj);
    let search = useSelector(state => state.mqtt.ConfigDevice.search);
    let page = useSelector(state => state.mqtt.ConfigDevice.page);
    let perPage = useSelector(state => state.mqtt.ConfigDevice.perPage);
    let count = useSelector(state => state.mqtt.ConfigDevice.count);
    const MySwal = withReactContent(Swal);

    // Query config device
    const [configDeviceList, setConfigDeviceList] = useState([]);
    const [queryConfigDevice, getConfigDevice] = useLazyQuery(GET_MQTT_CONFIG_DEVICES, {
        variables: {
            configId: detailObj?.id,
            search: search,
        },
        fetchPolicy: "network-only",
        nextFetchPolicy: "network-only"
    });

    useEffect(() => {
        if (getConfigDevice.loading) return;
        if (!getConfigDevice.data) return;
        const activeDevices = getConfigDevice?.data?.getMqttConfigDevices.filter(device => !device.isDeleted);
        setConfigDeviceList(activeDevices);
        dispatch(handleConfigDeviceCount(activeDevices.length));
    }, [getConfigDevice]);

    useEffect(() => {
        queryConfigDevice({
            variables: {
                configId: detailObj?.id,
                search: search,
            }
        });
    }, [search, page, perPage, detailObj]);

    // Handle delete
    const [deleteDeviceConfig, { }] = useMutation(TOGGLE_MQTT_CONFIG_DEVICES);
    const handleDelete = async (configId, deviceId) => {
        const result = await MySwal.fire({
            title: 'Delete this device?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            await deleteDeviceConfig({
                variables: {
                    configId: configId,
                    deviceIds: [deviceId],
                    isDeleted: true
                }
            }).then(() => {
                let newPage = page;
                if ((page - 1) * perPage >= count - 1) {
                    newPage = page - 1;
                }
                if (newPage < 1) newPage = 1;
                dispatch(handleConfigDevicePage(newPage));
                getConfigDevice.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: 'Deleted!',
                    text: 'Your device has been deleted.',
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message || e?.message);
            });
        }
    };

    // Columns
    const column = [
        {
            name: t('Device'),
            sortable: true,
            center: false,
            minWidth: '250px',
            sortField: 'address',
            selector: row => row.deviceId,
            cell: row => <span className='fw-bold'>{row.Device.deviceName}</span>
        },
        {
            name: t('Status'),
            sortable: true,
            center: false,
            minWidth: '50px',
            sortField: 'isDeleted',
            selector: row => row.isDeleted,
            cell: row => (
                <div className='column-action d-flex align-items-center'>
                    <Trash2 className='text-body cursor-pointer'
                        size={17}
                        onClick={() => handleDelete(row.configId, row.deviceId)}
                    />
                </div>
            )
        },
    ];

    // Custom pagination
    const indexOfLastItem = page * perPage;
    const indexOfFirstItem = indexOfLastItem - perPage;
    const finalItems = configDeviceList?.slice(indexOfFirstItem, indexOfLastItem);
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handleConfigDevicePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-1 pe-1'}
            />
        )
    };

    return (
        <Fragment>
            <div className='react-dataTable'>
                <DataTable
                    noHeader
                    subHeader
                    pagination
                    responsive
                    paginationServer
                    columns={column}
                    className='react-dataTable'
                    paginationComponent={CustomPagination}
                    data={finalItems}
                    subHeaderComponent={<DeviceConfigTableFilter />}
                    highlightOnHover
                />
            </div>
            <AddConfigDeviceModal
                deviceInConfig={configDeviceList}
                afterClose={() => getConfigDevice.refetch()}
            />
        </Fragment>
    )
};

export default DeviceConfig; 