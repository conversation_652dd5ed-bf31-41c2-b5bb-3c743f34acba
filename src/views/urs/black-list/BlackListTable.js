import { Fragment, useState, useEffect } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { SEARCH_BLACK_LIST_USER, REMOVE_BLACK_LIST_USER } from '../../../apolo/graphql/black-list';
import { Card } from 'reactstrap';
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import { Trash2 } from 'react-feather';
import Breadcrumbs from '@components/breadcrumbs';
import { toast } from 'react-hot-toast';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Spinner from '@components/spinner/Loading-spinner';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { handleCount, handleInit, handleLoading, handlePage } from './store';
import ApiUrlUserAvatarImage from '../all-users/ApiUrlUserAvatarImage';
import BlackListTableFilter from './BlackListTableFilter';
import AddBlackListUserModal from './AddBlackListUserModal';

const BlackListUserTable = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.blackList.search);
    let page = useSelector(state => state.blackList.page);
    let perPage = useSelector(state => state.blackList.perPage);
    let companyIds = useSelector(state => state.blackList.companyIds);
    let departmentIds = useSelector(state => state.blackList.departmentIds);
    let status = useSelector(state => state.blackList.status);
    let count = useSelector(state => state.blackList.count);
    let loading = useSelector(state => state.blackList.loading);
    const MySwal = withReactContent(Swal);

    useEffect(() => {
        dispatch(handleInit());
        return () => dispatch(handleLoading(false));
    }, []);
    //#endregion

    //#region query user
    const [blackList, setBlackList] = useState([]);
    const blackListQuery = useQuery(SEARCH_BLACK_LIST_USER, {
        variables: {
            search: search,
            page: page,
            perPage: perPage,
            companyIds: companyIds.length > 0 ? companyIds : [],
            departmentIds: departmentIds.length > 0 ? departmentIds : [],
            status: status !== -1 ? status : null,
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (blackListQuery.loading) {
            dispatch(handleLoading(true));
            return;
        };
        dispatch(handleLoading(false));
        if (!blackListQuery.data) return;
        setBlackList(blackListQuery?.data?.searchBlackListUser);
        dispatch(handleCount(blackListQuery?.data?.searchBlackListUser[0]?.count));
    }, [blackListQuery]);
    //#endregion

    //#region handle delete
    const [removeAlertUser, { }] = useMutation(REMOVE_BLACK_LIST_USER);
    const handleDelete = async (userId) => {
        const result = await MySwal.fire({
            title: t('Remove Black List User?'),
            text: t("Are you sure to remove this Black List User?"),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            removeAlertUser({
                variables: {
                    userIds: [userId],
                    isBlacklist: false,
                }
            }).then(() => {
                let newPage = page;
                if ((page - 1) * perPage >= count - 1) {
                    newPage = page - 1;
                }
                if (newPage < 1) newPage = 1;
                dispatch(handlePage(newPage));
                blackListQuery.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: t('Removed!'),
                    text: t('Your Black List has been removed.'),
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.response?.data?.message || e?.message);
            });
        }
    };
    //#endregion

    //#region column
    const renderClient = row => {
        if (row.avatar) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${row.avatar}`} />
        } else null;
    };

    const columm = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '300px',
            sortField: 'fullName',
            selector: row => row.fullName,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center'>
                    {renderClient(row)}
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.fullName}</span>
                        <small className='text-truncate text-muted mb-0'>{row.email}</small>
                    </div>
                </div>
            )
        },
        {
            name: t('Company'),
            minWidth: '200px',
            sortable: true,
            sortField: 'company',
            selector: row => row.company.name,
            cell: row => <span className='text-capitalize'>{row.Company?.name}</span>
        },
        {
            name: t('Department'),
            sortable: true,
            minWidth: '200px',
            sortField: 'department',
            selector: row => row.department.name,
            cell: row => <span className='text-capitalize'>{row.Department?.name}</span>
        },
        {
            name: t('Action'),
            sortable: true,
            center: true,
            minWidth: '100px',
            sortField: 'action',
            selector: row => row.action,
            cell: row => <Trash2 className='cursor-pointer' size={17} onClick={() => handleDelete(row.id)} />
        }
    ];
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <Fragment>
            <Breadcrumbs title={t('Black List Users')} data={[{ title: t('User') }, { title: t('Black List Users') }]} />
            <Card>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columm}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={blackList}
                        subHeaderComponent={<BlackListTableFilter />}
                        progressPending={loading}
                        progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
                    />
                </div>
            </Card>

            <AddBlackListUserModal
                afterClose={() => blackListQuery.refetch()}
            />
        </Fragment>
    )
};

export default BlackListUserTable;