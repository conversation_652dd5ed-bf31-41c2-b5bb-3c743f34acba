import { Fragment, useEffect, useState } from "react";
import { Button, Col, Input, Label, Row, Tooltip } from "reactstrap";
import { useQuery } from '@apollo/client';
import { GET_ALL_COMPANY, FIND_DEPARTMENTS } from '../../../apolo/graphql/Company';
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import { handleCompanyIds, handleDepartmentIds, handleLoading, handlePage, handlePerPage, handleSearch, handleShowAddModal, handleStatus } from "./store";
import UserStatusConstants from "@src/constants/UserStatusConstants";

const VipUserTableFilter = () => {

    //#region states
    const UserStatus = UserStatusConstants.UserStatus;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.vip.search);
    let perPage = useSelector(state => state.vip.perPage);
    let companyIds = useSelector(state => state.vip.companyIds);
    let departmentIds = useSelector(state => state.vip.departmentIds);
    let status = useSelector(state => state.vip.status);
    let showAddModal = useSelector(state => state.vip.showAddModal);
    //#endregion

    //#region filter
    const [comList, setComList] = useState([]);
    const companyQuery = useQuery(GET_ALL_COMPANY, {
        variables: {
            name: ""
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (companyQuery.loading) return;
        if (!companyQuery.data) return;
        setComList(companyQuery?.data?.companies);
    }, [companyQuery]);

    const [deptList, setDeptList] = useState([]);
    const departmentQuery = useQuery(FIND_DEPARTMENTS, {
        variables: {
            query: "",
            companyIds: companyIds
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (departmentQuery.loading) return;
        if (!departmentQuery.data) return;
        setDeptList(departmentQuery?.data?.findDepartments);
    }, [departmentQuery]);
    //#endregion

    //#region handle search
    const [searchValue, setSearchValue] = useState('');
    const [warning, setWarning] = useState('');
    const [openTooltip, setOpenTooltip] = useState(false);
    const [timeoutId, setTimeoutId] = useState(null);

    const handleChange = (e) => {
        const value = e.target.value;
        setSearchValue(value);

        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        if (value.length < 3 && value.length > 0) {
            setWarning(t('Input must be at least 3 characters long.'));
            setOpenTooltip(true);
            dispatch(handleLoading(false));
            dispatch(handlePage(1));
        } else {
            dispatch(handleLoading(true));
            setWarning('');
            const newTimeoutId = setTimeout(() => {
                setOpenTooltip(false);
                if (value.length === 0) {
                    dispatch(handleSearch(''));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                } else {
                    dispatch(handleSearch(value));
                    dispatch(handlePage(1));
                    dispatch(handleLoading(false));
                }
            }, 1000);
            setTimeoutId(newTimeoutId);
        }
    };
    //#endregion

    return (
        <Fragment>
            <div className='invoice-list-table-header w-100 mt-1'>
                <Row>
                    <Col md='4'>
                        <Label className='form-label' for='multiSelectCompany'>
                            {t("Company")}
                        </Label>
                        <Select
                            id='multiSelectCompany'
                            instanceId='multiSelectCompany'
                            className='react-select'
                            classNamePrefix='select'
                            isClearable={false}
                            isMulti={true}
                            theme={selectThemeColors}
                            options={comList.map(el => {
                                return {
                                    value: el.id,
                                    label: el.name
                                }
                            })}
                            value={comList.filter(el => companyIds.includes(el.id)).map(el => ({
                                value: el.id,
                                label: el.name
                            }))}
                            onChange={(el) => {
                                dispatch(handleCompanyIds(el ? el.map(el => el.value) : []));
                                dispatch(handleDepartmentIds([]));
                                dispatch(handlePage(1));
                            }}
                        />
                    </Col>
                    <Col md='4'>
                        <Label className='form-label' for='multiSelectedDept'>
                            {t("Department")}
                        </Label>
                        <Select
                            id='multiSelectedDept'
                            instanceId='multiSelectedDept'
                            className='react-select'
                            classNamePrefix='select'
                            isClearable={false}
                            isMulti={true}
                            theme={selectThemeColors}
                            options={deptList.map(el => {
                                return {
                                    value: JSON.stringify({ id: el.id, company: el.company.name, }),
                                    companyName: el.company.name,
                                    companyId: el.company.id,
                                    label: el.name + " - " + el?.company?.name
                                }
                            })}
                            value={deptList.filter(el => departmentIds.includes(el.id)).map(el => ({
                                value: JSON.stringify({ id: el.id, company: el.company.name, }),
                                label: el.name + " - " + el?.company?.name
                            }))}
                            onChange={(el) => {
                                dispatch(handleDepartmentIds(el ? el.map(item => JSON.parse(item.value).id) : []));
                                dispatch(handlePage(1));
                            }}
                        />
                    </Col>
                    <Col md='4'>
                        <Label className='form-label' for='selectStatus'>
                            {t("Status")}
                        </Label>
                        <Select
                            id='selectStatus'
                            instanceId='selectStatus'
                            className='react-select'
                            classNamePrefix='select'
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={false}
                            options={Object.keys(UserStatus).map(key => {
                                return {
                                    value: UserStatus[key],
                                    label: t(key)
                                }
                            })}
                            value={{
                                value: status,
                                label: t(Object.keys(UserStatus).find(key => UserStatus[key] === status))
                            }}
                            onChange={(el) => {
                                dispatch(handleStatus(parseInt(el.value)));
                                dispatch(handlePage(1));
                            }}
                        />
                    </Col>
                </Row>
            </div>
            <div className='invoice-list-table-header w-100 my-1 ms-1'>
                <Row>
                    <Col xl='6' className='d-flex align-items-center p-0'>
                        <div className='d-flex align-items-center w-100'>
                            <label htmlFor='rows-per-page'>{t("Show")}</label>
                            <Input
                                className='mx-50'
                                type='select'
                                id='rows-per-page'
                                value={perPage}
                                onChange={(e) => {
                                    dispatch(handlePerPage(parseInt(e.target.value)));
                                    dispatch(handlePage(1));
                                }}
                                style={{ width: '5rem' }}
                            >
                                <option value='10'>10</option>
                                <option value='15'>15</option>
                                <option value='20'>20</option>
                            </Input>
                            <label htmlFor='rows-per-page'>{t("Entries")}</label>
                        </div>
                    </Col>
                    <Col xl='6' className='d-flex align-items-sm-center justify-content-xl-end'>
                        <div className='d-flex align-items-center mb-sm-0 mb-1 me-1'>
                            <label className='mb-0' htmlFor='search-invoice'>
                                {t("Search")}:
                            </label>
                            <Input
                                id='search-invoice'
                                className='ms-50 w-100'
                                type='text'
                                value={searchValue}
                                onChange={e => handleChange(e)}
                                placeholder={t("Search")}
                                invalid={warning !== ''}
                            />
                            {warning && (
                                <Tooltip
                                    placement='top'
                                    isOpen={openTooltip}
                                    target='search-invoice'
                                    toggle={() => setOpenTooltip(!openTooltip)}
                                >
                                    {warning}
                                </Tooltip>
                            )}
                        </div>
                        <Button color='primary' onClick={() => dispatch(handleShowAddModal(!showAddModal))}>
                            {t("Add")}
                        </Button>
                    </Col>
                </Row>
            </div>
        </Fragment>
    )
};

export default VipUserTableFilter;