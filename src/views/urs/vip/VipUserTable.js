import { Fragment, useState, useEffect } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { SEARCH_VIP_USER, DELETE_VIP_USER } from '../../../apolo/graphql/vip-user';
import { Card } from 'reactstrap';
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import { Trash2 } from 'react-feather';
import Breadcrumbs from '@components/breadcrumbs';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Spinner from '@components/spinner/Loading-spinner';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { handleCount, handleInit, handleLoading, handlePage } from './store';
import ApiUrlUserAvatarImage from '../all-users/ApiUrlUserAvatarImage';
import VipUserTableFilter from './VipUserTableFilter';
import AddVIPUserModal from './AddVIPUserModal';

const VipUserTable = () => {

    //#region states
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let search = useSelector(state => state.vip.search);
    let page = useSelector(state => state.vip.page);
    let perPage = useSelector(state => state.vip.perPage);
    let companyIds = useSelector(state => state.vip.companyIds);
    let departmentIds = useSelector(state => state.vip.departmentIds);
    let status = useSelector(state => state.vip.status);
    let count = useSelector(state => state.vip.count);
    let loading = useSelector(state => state.vip.loading);
    const MySwal = withReactContent(Swal);

    useEffect(() => {
        dispatch(handleInit());
        return () => dispatch(handleLoading(false));
    }, []);
    //#endregion

    //#region query user
    const [vipList, setVipList] = useState([]);
    const vipUserQuery = useQuery(SEARCH_VIP_USER, {
        variables: {
            search: search,
            page: page,
            perPage: perPage,
            companyIds: companyIds.length > 0 ? companyIds : [],
            departmentIds: departmentIds.length > 0 ? departmentIds : [],
            status: status !== -1 ? status : null,
        },
        fetchPolicy: "cache-and-network",
        nextFetchPolicy: "cache-and-network"
    });

    useEffect(() => {
        if (vipUserQuery.loading) {
            dispatch(handleLoading(true));
            return;
        };
        dispatch(handleLoading(false));
        if (!vipUserQuery.data) return;
        setVipList(vipUserQuery?.data?.searchVipUser);
        dispatch(handleCount(vipUserQuery?.data?.searchVipUser[0]?.count));
    }, [vipUserQuery]);
    //#endregion

    //#region handle delete
    const [removeAlertUser, { }] = useMutation(DELETE_VIP_USER);
    const handleDelete = async (userId) => {
        const result = await MySwal.fire({
            title: t('Remove VIP User?'),
            text: t("Are you sure to remove this VIP User?"),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            removeAlertUser({
                variables: {
                    userIds: [userId],
                    isVip: false,
                }
            }).then(() => {
                let newPage = page;
                if ((page - 1) * perPage >= count - 1) {
                    newPage = page - 1;
                }
                if (newPage < 1) newPage = 1;
                dispatch(handlePage(newPage));
                vipUserQuery.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: t('Removed!'),
                    text: t('Your VIP User has been removed.'),
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.response?.data?.message || e?.message);
            });
        }
    };
    //#endregion

    //#region column
    const renderClient = row => {
        if (row.avatar) {
            return <ApiUrlUserAvatarImage url={`${import.meta.env.VITE_PUBLIC_BACKEND_IP}/api/public/images/avatar/${row.avatar}`} />
        } else {
            return null;
        }
    };

    const columm = [
        {
            name: t('User'),
            sortable: true,
            minWidth: '300px',
            sortField: 'fullName',
            selector: row => row.fullName,
            cell: row => (
                <div className='d-flex justify-content-left align-items-center'>
                    {renderClient(row)}
                    <div className='d-flex flex-column'>
                        <span className='fw-bolder'>{row.fullName}</span>
                        <small className='text-truncate text-muted mb-0'>{row.email}</small>
                    </div>
                </div>
            )
        },
        {
            name: t('Company'),
            minWidth: '200px',
            sortable: true,
            sortField: 'company',
            selector: row => row.company.name,
            cell: row => <span className='text-capitalize'>{row.Company?.name}</span>
        },
        {
            name: t('Department'),
            sortable: true,
            minWidth: '200px',
            sortField: 'department',
            selector: row => row.department.name,
            cell: row => <span className='text-capitalize'>{row.Department?.name}</span>
        },
        {
            name: t('Action'),
            sortable: true,
            center: true,
            minWidth: '100px',
            sortField: 'action',
            selector: row => row.action,
            cell: row => <Trash2 className='cursor-pointer' size={17} onClick={() => handleDelete(row.id)} />
        }
    ];
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage) || 1}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handlePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    return (
        <Fragment>
            <Breadcrumbs title={t('VIP Users')} data={[{ title: t('User') }, { title: t('VIP Users') }]} />
            <Card>
                <div className='react-dataTable'>
                    <DataTable
                        noHeader
                        subHeader
                        pagination
                        responsive
                        paginationServer
                        columns={columm}
                        className='react-dataTable'
                        paginationComponent={CustomPagination}
                        data={vipList}
                        subHeaderComponent={<VipUserTableFilter />}
                        progressPending={loading}
                        progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
                    />
                </div>
            </Card>

            <AddVIPUserModal
                afterClose={() => vipUserQuery.refetch()}
            />
        </Fragment>
    )
};

export default VipUserTable;
