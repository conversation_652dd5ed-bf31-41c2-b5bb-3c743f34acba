import { createSlice } from '@reduxjs/toolkit';

const initState = () => {

    return {
        search: "",
        page: 1,
        perPage: 10,
        companyIds: [],
        departmentIds: [],
        status: -1,
        count: 0,
        showAddModal: false,
        addUserIds: [],
        loading: false,
    }
};

export const vipSlice = createSlice({
    name: 'vip',
    initialState: initState(),
    reducers: {
        handleInit: (state, action) => {
            return initState();
        },
        handleSearch: (state, action) => {
            state.search = action.payload
        },
        handlePage: (state, action) => {
            state.page = action.payload
        },
        handlePerPage: (state, action) => {
            state.perPage = action.payload
        },
        handleCompanyIds: (state, action) => {
            state.companyIds = action.payload
        },
        handleDepartmentIds: (state, action) => {
            state.departmentIds = action.payload
        },
        handleStatus: (state, action) => {
            state.status = action.payload
        },
        handleCount: (state, action) => {
            state.count = action.payload
        },
        handleShowAddModal: (state, action) => {
            state.showAddModal = action.payload
        },
        handleAddUserIds: (state, action) => {
            state.addUserIds = action.payload
        },
        handleLoading: (state, action) => {
            state.loading = action.payload
        },
    },
});

export const {
    handleInit,
    handleSearch,
    handlePage,
    handlePerPage,
    handleCompanyIds,
    handleDepartmentIds,
    handleStatus,
    handleCount,
    handleShowAddModal,
    handleAddUserIds,
    handleLoading,
} = vipSlice.actions;

export default vipSlice.reducer;