import { useState } from "react";
import { <PERSON><PERSON>, Col, Input, Label, Modal, ModalBody, <PERSON>dal<PERSON><PERSON>er, <PERSON>dal<PERSON><PERSON>er, Row, Nav, NavItem, NavLink, TabContent, TabPane } from "reactstrap";
import { useMutation } from "@apollo/client";
import { UPSERT_IPARKING_VEHICLE } from "../../../apolo/graphql/IParkingIntegration";
import Select from 'react-select';
import '@styles/react/libs/react-select/_react-select.scss';
import { selectThemeColors } from '@utils';
import Flatpickr from 'react-flatpickr';
import '@styles/react/libs/flatpickr/flatpickr.scss';
import { toast } from 'react-hot-toast';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleShowAddLicensePlateModal } from "./store";
import IParkingConstants from '@src/constants/IParkingConstants';

const AddLicensePlateModal = ({ afterClose }) => {

    //#region states
    const IParkingVehicleTypeEnums = IParkingConstants.IParkingVehicleType;
    const { t } = useTranslation('');
    const dispatch = useDispatch();
    let userId = useSelector(state => state.user.selectedUserId);
    let showAddLicensePlateModal = useSelector(state => state.user.LicensePlate.showAddLicensePlateModal);
    const [formStates, setFormStates] = useState({
        valid: false,
        values: {
            name: "",
            plateNumber: "",
            type: "",
            customerId: userId,
            expiredDate: new Date(),
            lastActivatedDate: new Date(),
            checkInByPlate: false,
            checkOutByPlate: false,
        },
        errors: {},
    });
    //#endregion

    //#region handle submit
    const [addVehicle, { }] = useMutation(UPSERT_IPARKING_VEHICLE);
    const handleSubmit = () => {
        if (formStates.values.name === "") {
            toast.error("Please enter name");
        } else if (formStates.values.plateNumber === "") {
            toast.error("Please enter plate number");
        } else {
            addVehicle({
                variables: {
                    name: formStates.values.name,
                    plateNumber: formStates.values.plateNumber,
                    type: formStates.values.type,
                    customerId: userId,
                    expiredDate: formStates.values.expiredDate,
                    lastActivatedDate: formStates.values.lastActivatedDate,
                    checkInByPlate: formStates.values.checkInByPlate,
                    checkOutByPlate: formStates.values.checkOutByPlate,
                }
            }).then(() => {
                toast.success("Added Successfully");
                dispatch(handleShowAddLicensePlateModal(!showAddLicensePlateModal));
            }).catch(e => {
                toast.error(e.message);
            })
        }
    };
    //#endregion

    const closeModal = () => {
        setFormStates({
            valid: false,
            values: {
                name: "",
                plateNumber: "",
                type: "",
                customerId: userId,
                expiredDate: new Date(),
                lastActivatedDate: new Date(),
                checkInByPlate: false,
                checkOutByPlate: false,
            },
            errors: {},
        });
        afterClose();
    };

    return (
        <Modal
            isOpen={showAddLicensePlateModal}
            toggle={() => dispatch(handleShowAddLicensePlateModal(!showAddLicensePlateModal))}
            onClosed={() => closeModal()}
            className='modal-dialog-centered'
        >
            <ModalHeader toggle={() => dispatch(handleShowAddLicensePlateModal(!showAddLicensePlateModal))}>
                {t("Add IParking Vehicle")}
            </ModalHeader>
            <ModalBody>
                <Row>
                    <Col md='12' sm='12' className="mb-1">
                        <Label className='form-label' for='name'>
                            {t('Name')} <span className="text-danger">*</span>
                        </Label>
                        <Input
                            type='text'
                            name='name'
                            id='name'
                            className='form-control'
                            value={formStates.values.name}
                            onChange={(e) => {
                                setFormStates((formStates) => ({
                                    ...formStates,
                                    values: {
                                        ...formStates.values,
                                        name: e.target.value,
                                    },
                                }));
                            }}
                            placeholder={t('Name')}
                        />
                    </Col>
                    <Col md='12' sm='12' className="mb-1">
                        <Label className='form-label' for='plateNumber'>
                            {t('Plate Number')} <span className="text-danger">*</span>
                        </Label>
                        <Input
                            type='text'
                            name='plateNumber'
                            id='plateNumber'
                            className='form-control'
                            value={formStates.values.plateNumber}
                            onChange={(e) => {
                                setFormStates((formStates) => ({
                                    ...formStates,
                                    values: {
                                        ...formStates.values,
                                        plateNumber: e.target.value,
                                    },
                                }));
                            }}
                            placeholder={t('Plate Number')}
                        />
                    </Col>
                    <Col md='12' sm='12' className="mb-1">
                        <Label className='form-label' for='type'>
                            {t('Type')} <span className="text-danger">*</span>
                        </Label>
                        <Select
                            id="type"
                            instanceId="type"
                            className="react-select"
                            classNamePrefix="select"
                            theme={selectThemeColors}
                            isClearable={false}
                            isMulti={false}
                            options={Object.keys(IParkingVehicleTypeEnums).map((key) => {
                                return {
                                    value: key,
                                    label: t(IParkingVehicleTypeEnums[key]),
                                };
                            })}
                            onChange={(e) => {
                                setFormStates((formStates) => ({
                                    ...formStates,
                                    values: {
                                        ...formStates.values,
                                        type: parseInt(e.value),
                                    },
                                }));
                            }}
                        />
                    </Col>
                    <Col md='12' sm='12' className="mb-1">
                        <Label className='form-label' for='expiredDate'>
                            {t('Expired Date')} <span className="text-danger">*</span>
                        </Label>
                        <Flatpickr
                            id="expiredDate"
                            className='form-control text-center'
                            data-enable-time
                            value={formStates.values.expiredDate}
                            onChange={(e) => {
                                setFormStates((formStates) => ({
                                    ...formStates,
                                    values: {
                                        ...formStates.values,
                                        expiredDate: e[0],
                                    },
                                }));
                            }}
                            options={{
                                dateFormat: 'd/m/Y H:i',
                                time_24hr: true
                            }}
                        />
                    </Col>
                </Row>
            </ModalBody>
            <ModalFooter>
                <Button color='primary' onClick={() => handleSubmit()}>
                    {t('Submit')}
                </Button>
                <Button color='secondary' outline onClick={() => dispatch(handleShowAddLicensePlateModal(!showAddLicensePlateModal))}>
                    {t('Discard')}
                </Button>
            </ModalFooter>
        </Modal>
    )
};

export default AddLicensePlateModal;