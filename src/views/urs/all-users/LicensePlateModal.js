import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ModalHeader } from "reactstrap";
import { useLazyQuery, useMutation } from "@apollo/client";
import { DELETE_IPARKING_VEHICLE, GET_IPARKING_VEHICLE } from "../../../apolo/graphql/IParkingIntegration";
import { Trash2 } from 'react-feather';
import ReactPaginate from 'react-paginate';
import DataTable from 'react-data-table-component';
import '@styles/react/libs/tables/react-dataTable-component.scss';
import Spinner from '@components/spinner/Loading-spinner';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import moment from 'moment';
import { toast } from 'react-hot-toast';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleLicensePlateCount, handleLicensePlatePage, handleShowLicensePlateModal } from "./store";
import LicensePlateFilter from "./LicensePlateFilter";
import IParkingConstants from "@src/constants/IParkingConstants";
import AddLicensePlateModal from "./AddLicensePlateModal";
const LicensePlateModal = ({ afterClose }) => {

    //#region states
    const IParkingVehicleTypeEnums = IParkingConstants.IParkingVehicleType;
    const { t } = useTranslation("");
    const dispatch = useDispatch();
    const userId = useSelector(state => state.user.selectedUserId);
    const showLicensePlateModal = useSelector(state => state.user.showLicensePlateModal);
    const search = useSelector(state => state.user.LicensePlate.search);
    const page = useSelector(state => state.user.LicensePlate.page);
    const perPage = useSelector(state => state.user.LicensePlate.perPage);
    const type = useSelector(state => state.user.LicensePlate.type);
    const count = useSelector(state => state.user.LicensePlate.count);
    const loading = useSelector(state => state.user.LicensePlate.loading);
    const MySwal = withReactContent(Swal);
    //#endregion

    //#region query license list
    const [licenseList, setLicenseList] = useState([]);
    const [query, getIparkingVehicle] = useLazyQuery(GET_IPARKING_VEHICLE, {
        variables: {
            search: search,
            page: page,
            perPage: perPage,
            customerIds: [userId],
            type: type
        },
        fetchPolicy: 'cache-and-network',
        nextFetchPolicy: 'cache-and-network',
    });

    useEffect(() => {
        if (getIparkingVehicle.loading) return;
        if (!getIparkingVehicle.data) return;
        setLicenseList(getIparkingVehicle?.data?.getIparkingVehicle);
        dispatch(handleLicensePlateCount(getIparkingVehicle?.data?.getIparkingVehicle[0]?.count));
    }, [getIparkingVehicle]);

    useEffect(() => {
        if (showLicensePlateModal) {
            query({
                variables: {
                    search: search,
                    page: page,
                    perPage: perPage,
                    customerIds: [userId],
                    type: type
                }
            });
        }
    }, [showLicensePlateModal, search, page, perPage, type]);
    //#endregion

    //#region handle delete
    const [deleteVehicle, { }] = useMutation(DELETE_IPARKING_VEHICLE);
    const handleDelete = async (id) => {
        const result = await MySwal.fire({
            title: t('Delete this vehicle?'),
            text: t("You won't be able to revert this!"),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: t('Yes, delete it!'),
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            },
            buttonsStyling: false
        });
        if (result.value) {
            deleteVehicle({
                variables: {
                    vehicleId: id
                }
            }).then(() => {
                let newPage = page;
                if ((page - 1) * perPage >= count - 1) {
                    newPage = page - 1;
                }
                if (newPage < 1) newPage = 1;
                dispatch(handleLicensePlatePage(newPage));
                getIparkingVehicle.refetch();
                MySwal.fire({
                    icon: 'success',
                    title: t('Deleted!'),
                    text: t('Your vehicle has been deleted.'),
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }).catch(e => {
                toast.error(e?.graphQLErrors[0]?.message || e?.message);
            });
        }
    };
    //#endregion

    //#region columns
    const column = [
        {
            name: t('Plate Number'),
            sortable: true,
            center: false,
            minWidth: '150px',
            sortField: 'plateNumber',
            selector: row => row.plateNumber,
            cell: row => <span className='fw-bolder text-uppercase'>{row.plateNumber}</span>
        },
        {
            name: t('Type'),
            sortable: true,
            center: true,
            minWidth: '150px',
            sortField: 'type',
            selector: row => row.type,
            cell: row => <Badge color={row.type === 0 ? 'light-success' : row.type === 1 ? 'light-primary' : 'light-info'} className="text-uppercase" pill>
                {IParkingVehicleTypeEnums[row.type]}
            </Badge>
        },
        {
            name: t('Expired Date'),
            sortable: true,
            center: false,
            minWidth: '150px',
            sortField: 'expiredDate',
            selector: row => row.expiredDate,
            cell: row => (
                <div className='d-flex flex-column'>
                    <span className='text-capitalize'>{moment(row.expiredDate).format('DD/MM/YYYY')}</span>
                    <small className='text-muted'>{moment(row.expiredDate).format('HH:mm:ss')}</small>
                </div>
            )
        },
        {
            name: t('Action'),
            sortable: true,
            center: true,
            minWidth: '50px',
            sortField: 'action',
            selector: row => row.action,
            cell: row => <Trash2 className='text-body cursor-pointer' size={17} onClick={() => handleDelete(row.id)} />
        },
    ];
    //#endregion

    //#region custom pagination
    const CustomPagination = () => {
        return (
            <ReactPaginate
                previousLabel={''}
                nextLabel={''}
                pageCount={Math.ceil(count / perPage)}
                activeClassName='active'
                forcePage={page !== 0 ? page - 1 : 0}
                onPageChange={e => dispatch(handleLicensePlatePage(e.selected + 1))}
                pageClassName={'page-item'}
                nextLinkClassName={'page-link'}
                nextClassName={'page-item next'}
                previousClassName={'page-item prev'}
                previousLinkClassName={'page-link'}
                pageLinkClassName={'page-link'}
                containerClassName={'pagination react-paginate justify-content-end my-2 pe-1'}
            />
        )
    };
    //#endregion

    const handleClose = () => {
        afterClose();
    };

    return (
        <Fragment>
            <Modal
                isOpen={showLicensePlateModal}
                toggle={() => dispatch(handleShowLicensePlateModal(!showLicensePlateModal))}
                onClosed={handleClose}
                className='modal-dialog-centered modal-lg'
                style={{ maxWidth: '40%' }}
            >
                <ModalHeader toggle={() => dispatch(handleShowLicensePlateModal(!showLicensePlateModal))}>
                    {t("License Plate")}
                </ModalHeader>
                <ModalBody>
                    <div className='react-dataTable'>
                        <DataTable
                            noHeader
                            subHeader
                            pagination
                            responsive
                            paginationServer
                            columns={column}
                            className='react-dataTable'
                            data={licenseList}
                            subHeaderComponent={<LicensePlateFilter />}
                            paginationComponent={CustomPagination}
                            pointerOnHover
                            highlightOnHover
                            progressPending={loading}
                            progressComponent={<div className="p-3"><Spinner color="primary" /></div>}
                        />
                    </div>
                </ModalBody>
            </Modal>

            <AddLicensePlateModal afterClose={() => getIparkingVehicle.refetch()} />
        </Fragment>
    );
};

export default LicensePlateModal;