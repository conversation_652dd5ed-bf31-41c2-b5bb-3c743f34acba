export const floodPrevent = {
    checkFlood: (socket) => checkFlood(socket), 
    resetProtection: () => resetProtection()
};

const FLOOD_TIME = 3000;
const FLOOD_MAX = 2;
let flood = {
    floods: {},
    lastFloodClear: new Date(),
}

function checkFlood(socket) {
    // Reset flood protection
    if( Math.abs( (new Date()).getTime() - flood.lastFloodClear.getTime()) > FLOOD_TIME ){
        resetProtection()
    }

    flood.floods[socket.id] == undefined ? flood.floods[socket.id] = {} : flood.floods[socket.id];
    flood.floods[socket.id].count == undefined ? flood.floods[socket.id].count = 0 : flood.floods[socket.id].count;
    flood.floods[socket.id].count++;
    // console.log(flood.floods[socket.id].count)

    //Disconnect the socket if he went over FLOOD_MAX in FLOOD_TIME
    if( flood.floods[socket.id].count > FLOOD_MAX){
        // console.log('FLOODPROTECTION ', socket.id)
        return false;
    }
    return true;
}

function resetProtection () {
    flood.floods = {};
    flood.lastFloodClear = new Date();
}
