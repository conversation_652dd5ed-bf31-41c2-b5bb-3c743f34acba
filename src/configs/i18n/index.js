// ** I18n Imports
import i18n from 'i18next'
import Backend from 'i18next-http-backend'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// ** Languages Imports
const en = new URL('../../assets/data/locales/en/common.json', import.meta.url).href
const vn = new URL('../../assets/data/locales/vi/common.json', import.meta.url).href
const jp = new URL('../../assets/data/locales/jp/common.json', import.meta.url).href

const languages = {
    en,
    vn,
    jp,
}

i18n

    // Enables the i18next backend
    .use(Backend)

    // Enable automatic language detection
    .use(LanguageDetector)

    // Enables the hook initialization module
    .use(initReactI18next)
    .init({
        lng: localStorage.getItem('lang'),
        backend: {
            /* translation file path */
            loadPath: lng => languages[lng]
        },
        fallbackLng: localStorage.getItem('lang'),
        debug: false,
        keySeparator: false,
        react: {
            useSuspense: false
        },
        interpolation: {
            escapeValue: false,
            formatSeparator: ','
        }
    })

export default i18n
