export default {
    ShiftUserExplainStatus: {
        "All": -1,
        "Waitting": 0,
        "Approved": 1,
        "Rejected": 2,
    },

    ShiftUserExplainStatusText: {
        "All": -1,
        0: "Waitting",
        1: "Approved",
        2: "Rejected",
    },

    ShiftUserExplainType: {
        "All": -1,
        "Work at company": 0,
        "Work at customer": 1,
        "Work at home": 2,
        "Late": 3,
        "Early": 4,
        "On leave": 5,
        "Absent": 6,
        "Maternity-related benefit": 7
    },

    ShiftExplainType: {
        0: "Work at company",
        1: "Work at customer",
        2: "Work at home",
        3: "Late",
        4: "Early",
        5: "On leave",
        6: "Absent",
        7: "Maternity-related benefit",
    },
}