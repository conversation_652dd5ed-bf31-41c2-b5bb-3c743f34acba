export default {
    AlertScriptType: {
        "All": -1,
        "Mobile Notification": 0,
        "Dashboard Notification": 1,
        "Send email": 2,
    },

    AlertLevel: {
        1: "High",
        2: "Medium",
        3: "Low",
    },

    AlertType: {
        [-1]: "All",
        0: "VIP detected",
        1: "Black list detected",
        2: "Loitering detected",
        3: "Break-in detected",
        4: "Crow detected",
        5: "Weapon detected",
        6: "Behavior detected",
        7: "System",
        8: "FireSmoke",
        9: "LineCrossing",
        10: "Person Area Detection",
        11: "Person Area Intrusion",
        12: "Face Area Detection",
        13: "Face Area Intrusion",
        14: "Person Line Crossing",
        15: "Vehicle Area Detection",
        16: "Weapon Area Detection",
        17: "Fire Area Detection",
        18: "Vehicle Area Intrustion",
        19: "Vehicle Line Crossing",
        20: "Crowd Detection",
        21: "Guest Access Control",
        22: "Unknown Face",
    },

    AlertTypeFilter: {
        0: "Create",
        1: "Update",
        2: "Delete",
        3: "Read",
    },

    AlertRuleType: {
        "ALL": "",
        "DEVICE_SIGNAL_ALERT": "DEVICE_SIGNAL_ALERT",
        "VIP_ALERT": "VIP_ALERT",
        "USER_BLACK_LIST_ALERT": "USER_BLACK_LIST_ALERT",
        "ACCESS_CONTROL_ALERT": "ACCESS_CONTROL_ALERT"
    }
}