export default {
    DataImportType: {
        0: "User Info Excel",
        1: "User Image Excel",
        2: "CIVAMS Users",
    },
    DataKeyValues: {
        "name": "<PERSON><PERSON> Tên",
        "birthday": "<PERSON><PERSON><PERSON>",
        "companyName": "<PERSON>ên <PERSON>",
        "departmentName": "Tên <PERSON>",
        "email": "Email",
        "employeeKey": "Mã nhân viên",
        "gender": "Giới tính",
        "phoneNumber": "Số điện thoại",
        "userId": "ID người dùng",
        "userName": "Tên người dùng",
        "dateCreated": "<PERSON><PERSON>y tạo",
        "dateModified": "<PERSON><PERSON><PERSON> sửa",
        "classCode": "Mã lớp",
        "className": "Tên lớp",
        "grade": "Khối",
        "majorCode": "Mã ngành",
        "majorName": "<PERSON>ê<PERSON> ngành",
        "validUntil": "<PERSON><PERSON><PERSON> hết hạn",
        "idCardNumber": "<PERSON><PERSON> căn cước công dân",
        "title": "<PERSON><PERSON><PERSON> danh",
        "image": "Ảnh",
        "voiceString": "<PERSON><PERSON><PERSON>ng nói",
        "welcomeImagePath": "Ảnh chào mừng",
        "welcomeVoicePath": "Âm thanh chào mừng",
        "displayString": "Chuỗi hiển thị",
        "color": "Màu",
        "isVip": "VIP",
        "image1": "Ảnh 1",
        "image2": "Ảnh 2",
        "image3": "Ảnh 3",
        "errors": "Lỗi",
    },
    ImportUserType: {
        0: "Create",
        1: "Update",
        2: "Skip",
    }
}