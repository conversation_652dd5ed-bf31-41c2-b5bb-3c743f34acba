export default {
    DeviceType: {
        // All: -1,
        Unidentified: 0,
        UpdaterAndMatcherOnly: 1,
        FlowDetectOnly: 2,
        FullFunctions: 3,
        DisplayOnly: 4,
        FaceTerminal: 5,
        DisplayOnlyWpf: 6,
        ServerRecogChannel: 7,
        WP<PERSON>aceRecog: 8,
        AIBox: 9,
        AICamera: 10,
        WPFFaceTerminal: 11,
        CloudCamera: 12,
        VMS: 13,
        Robot: 14,
        FcardTerminal: 15,
        AIHumanServer: 20,
        AIVehicleServer: 30,
        AIFaceSearchServer: 40,
    },

    DeviceTypeText: {
        0: 'Unidentified',
        1: 'Updater And Matcher Only',
        2: 'Flow Detect Only',
        3: 'Full Functions',
        4: 'Display Only',
        5: 'Face Terminal',
        6: 'Display Only Wpf',
        7: 'Server Recog Channel',
        8: 'WPF Face Recog',
        9: 'AI Box',
        10: 'AI Camera',
        11: 'WPF Face Terminal',
        12: 'Cloud Camera',
        13: 'VMS',
        14: 'Robot',
        15: 'Fcard Terminal',
        20: 'AI Human Server',
        30: 'AI Vehicle Server',
        40: 'AI Face Search Server',
    },

    DeviceStatus: {
        All: -1,
        Active: 0,
        InActive: 1
    },

    DeviceStatusText: {
        0: 'Active',
        1: 'Deactive'
    },
}