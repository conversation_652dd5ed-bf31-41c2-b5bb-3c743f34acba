export default {
    IParkingLaneTypeEnums: {
        "In": 0,
        "Out": 1,
    },
    IParkingLaneTypeEnumsText: {
        0: "In",
        1: "Out",
    },
    IParkingVehicleTypeEnum: {
        "Bike": 0,
        "Car": 1,
    },
    IParkingVehicleTypeEnumText: {
        0: "Bike",
        1: "Car",
    },
    IParkingDeviceTypeEnum: {
        "Face Recognize": 0,
        "License Plate Recognize": 1,
        "Barrie Controller": 2,
        "Alarm Controller": 3,
    },
    IParkingDeviceTypeEnumText: {
        0: "Face Recognize",
        1: "License Plate Recognize",
        2: "Barrie Controller",
        3: "Alarm Controller",
    },
    IParkingBarrieModeEnum: {
        "Face & License": 0,
        "License Only": 1,
        "Face Only": 2,
    },
    IParkingBarrieModeEnumText: {
        0: "Face & License",
        1: "License Only",
        2: "Face Only",
    },
    IParkingIdentityType: {
        0: "Card",
        1: "QR Code",
        2: "Finger Print",
        3: "Face ID",
        4: "Plate Number",
        5: "One Time QR Code"
    },
    IParkingIdentityStatus: {
        0: "In Use",
        1: "Locked"
    },
    IParkingIdentityGroupType: {
        0: "Monthly",
        1: "Daily",
        2: "Free",
        4: "VIP"
    },
    IParkingPlateNumberValidation: {
        0: "Exactly",
        1: "Approximate",
        2: "None"
    },
    IParkingPlatePlateNumberComparison: {
        0: "Full Comparison",
        1: "Event In Only",
        2: "Event Out Only"
    },
    IParkingVehicleType: {
        0: "Car",
        1: "Motorbike",
        2: "Bike"
    },
    IParkingLaneType: {
        0: "In",
        1: "Out",
        2: "KioskIn",
        3: "Dynamic"
    }
}