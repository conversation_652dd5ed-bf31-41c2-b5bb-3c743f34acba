{"Dashboards": "painel de controle", "Analytics": "Analytics", "eCommerce": "comércio eletrônico", "Apps": "Apps", "Email": "O email", "Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Todo": "Façam", "Calendar": "<PERSON><PERSON><PERSON><PERSON>", "Ecommerce": "Comércio eletrô<PERSON>o", "Shop": "fazer compras", "Wish List": "Lista de Desejos", "Details": "<PERSON><PERSON><PERSON>", "Checkout": "<PERSON><PERSON><PERSON>", "User": "Do utilizador", "List": "Lista", "View": "Visão", "Edit": "<PERSON><PERSON>", "Starter Kit": "Kit iniciante", "1 Column": "1 coluna", "2 Columns": "2 colunas", "Fixed Navbar": "Navbar fixa", "Floating Navbar": "<PERSON>vbar flutuante", "Fixed Layout": "Layout fixo", "Static Layout": "Layout está<PERSON>o", "Dark Layout": "Layout escuro", "Light Layout": "Layout de luz", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Grid": "Grade", "Typography": "Tipografia", "Text Utilities": "Utilitários de texto", "Syntax Highlighter": "Marcador de sintaxe", "Helper Classes": "Classes Auxiliares", "Icons": "Ícones", "Feather": "<PERSON><PERSON>", "UI Elements": "Elementos da interface do usuário", "Card": "Cartão", "Basic": "Basic", "Advance": "<PERSON><PERSON><PERSON><PERSON>", "Statistics": "Estatisticas", "Actions": "Ações do Cartão", "Table": "<PERSON><PERSON><PERSON>", "Reactstrap Tables": "Mesa Reactstrap", "React Tables": "Mesa React", "DataTable": "Tabela de dados", "Advanced": "Avançado", "Mail Template": "<PERSON><PERSON>rre<PERSON>", "Page Layouts": "Layouts de página", "Collapsed Menu": "<PERSON><PERSON> recolhido", "Layout Boxed": "Layout em caixa", "Without Menu": "<PERSON><PERSON>", "Layout Empty": "Layout vazio", "Layout Blank": "Layout em branco", "Components": "Componentes", "Alerts": "<PERSON><PERSON><PERSON>", "Buttons": "<PERSON><PERSON><PERSON><PERSON>", "Breadcrumbs": "Pão ralado", "Carousel": "<PERSON><PERSON><PERSON>", "Collapse": "Colapso", "Dropdowns": "Dropdowns", "List Group": "Grupo de listas", "Modals": "<PERSON><PERSON><PERSON>", "Pagination": "Paginação", "Navs Component": "Componente Navs", "Navbar": "<PERSON><PERSON><PERSON>", "Tabs Component": "Componente de guias", "Pills Component": "Componente de comprimidos", "Tooltips": "Dicas de ferramentas", "Popovers": "Popovers", "Badges": "Distintivos", "Pill Badges": "<PERSON><PERSON><PERSON>", "Progress": "Progresso", "Media Objects": "Objetos de Mídia", "Spinner": "Girador", "Toasts": "<PERSON><PERSON><PERSON>", "Timeline": "Linha do tempo", "Extra Components": "Componentes extras", "Avatar": "Avatar", "Chips": "Salgadin<PERSON>", "Divider": "Divisor", "Wizard": "Mago", "Form Elements": "Elementos do formulário", "Forms & Tables": "Formulários e tabelas", "Select": "Selecione", "Switch": "Interruptor", "Checkbox": "Caixa de seleção", "Radio": "<PERSON><PERSON><PERSON>", "Input": "Entrada", "Input Groups": "Grupos de entrada", "Number Input": "Entrada numérica", "Textarea": "Textarea", "Date & Time Picker": "Selecionador de data e hora", "Input Mask": "Máscara de entrada", "Form Layout": "Layout do formulário", "Form Wizard": "Assistente de Formulário", "Form Validation": "Validação de Formulário", "React Hook Form": "React Hook Form", "Pages": "<PERSON><PERSON><PERSON><PERSON>", "Authentication": "Autenticação", "Login v1": "Conecte-se v1", "Login v2": "Conecte-se v2", "Register v1": "Registro v1", "Register v2": "Registro v2", "Forgot Password v1": "Esqueceu a senha v1", "Forgot Password v2": "Esqueceu a senha v2", "Reset Password v1": "Redefinir senha v1", "Reset Password v2": "Redefinir senha v2", "Coming Soon": "Em breve", "Error": "Erro", "Not Authorized": "Não autorizado", "Maintenance": "Manutenção", "Profile": "Perfil", "FAQ": "<PERSON><PERSON><PERSON> frequentes", "Knowledge Base": "Base de Conhecimento", "Search": "Procurar", "Account Settings": "Configurações da conta", "Invoice": "<PERSON><PERSON>", "Swiper": "Swiper", "Miscellaneous": "Diversas", "Charts & Maps": "Gráficos e Mapas", "Charts": "Grá<PERSON><PERSON>", "Apex": "Ápice", "ChartJS": "ChartJS", "Recharts": "Recharts", "Leaflet Maps": "Leaflet Maps", "Extensions": "Extensões", "Sweet Alert": "<PERSON><PERSON><PERSON>", "React Hot Toasts": "<PERSON><PERSON><PERSON>", "Sliders": "Sliders", "File Uploader": "Uploader de arquivos", "Editor": "Editor", "Tree": "á<PERSON><PERSON>e", "Drag & Drop": "Arraste e solte", "Tour": "Tour", "Auto Complete": "Autocompletar", "Clipboard": "Pranchet<PERSON>", "I18n": "I18n", "React Paginate": "React Paginate", "Export": "Exportação", "Import": "Importar", "Export Selected": "Exportar selecionado", "Access Control": "Controle de acesso", "Others": "Outras", "Menu Levels": "Níveis de Menu", "Second Level": "<PERSON><PERSON><PERSON>", "Second Level 2.1": "Segundo nível 2.1", "Second Level 2.2": "<PERSON><PERSON><PERSON> nível 2.2", "Third Level": "<PERSON><PERSON><PERSON> nivel", "Third Level 3.1": "Terceiro nivel 3.1", "Third Level 3.2": "Terceiro nivel 3.2", "Disabled Menu": "<PERSON><PERSON> desati<PERSON>do", "Documentation": "Documentação", "Raise Support": "Levantar Suporte", "Change Log": "Log de alterações", "text": "O sésamo do bolo agarra dinamarquês do pão-de-espécie do queque eu amo o pão-de-espécie. Torta de torta de maçã jujuba chupa chups muffin halvah pirulito. Ameixa do açúcar do maçapão do tiramisu do bolo da aveia do bolo de chocolate. Bolo de aveia de torta doce donut dragée fruitcake algodão doce gotas de limão.", "Pricing": "Preços", "Blog": "Blog", "Detail": "<PERSON><PERSON><PERSON>", "Form Repeater": "Repetidor de formulários", "Preview": "Pré-visualização", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Ratings": "Avaliações", "show": "mostrar", "entries": "entradas", "search": "procurar", "Prev": "Anterior", "Next": "Próximo", "BlockUI": "BlockUI", "Reactstrap": "Reactstrap", "Welcome": "Be<PERSON>-<PERSON><PERSON>", "Reset Password": "<PERSON><PERSON><PERSON><PERSON>", "Verify Email": "Verificar e-mail", "Deactivate Account": "Desativar conta", "Promotional": "Promocional", "Apps & Pages": "Aplicativos e páginas", "User Interface": "Interface de usuário", "Misc": "Misc", "Roles": "Funções", "License": "Licença", "API Key": "Chave API", "Accordion": "Acordeão", "OffCanvas": "OffCanvas", "Permissions": "Permissões", "Modal Examples": "<PERSON><PERSON><PERSON><PERSON>", "Roles & Permissions": "Funções e permissões"}