{"Month": "Month", "Checkin": "Checkin", "Checkout": "Checkout", "Bothway": "Bothway", "Day": "Day", "Users": "Users", "Devices": "Devices", "Role": "Role", "User Id": "User Id", "User Name": "User Name", "Change Role": "Change Role", "Status": "Status", "Employee": "Employee", "Company": "Company", "Dept": "Department", "Email": "Email", "Date of Birth": "Date of Birth", "Phone Number": "Phone Number", "Gender": "Gender", "Male": "Male", "Female": "Female", "Other": "Other", "Employee Code": "Employee Code", "Department": "Department", "Face Images": "Face Images", "Delete User": "Delete User", "Lock User": "Lock User", "Unlock User": "Unlock User", "Change avatar": "Change avatar", "Change password": "Change password", "Reset password": "Reset password", "Save": "Save", "Old password": "Old password", "New password": "New password", "Retype new password *": "Retype new password", "Change": "Change", "Profile": "Profile", "Sign out": "Sign out", "User by AD": "User by AD", "User by Email": "User by Email", "Import User": "Import User", "Export User": "Export User", "Add mode": "Add mode", "New Company Name": "New Company Name", "New Department Name": "New Department Name", "Sub-Department Of": "Sub-Department Of", "Active Domain User Name": "Active Domain User Name", "Add User": "Add User", "Add new company": "Add new company", "Add new department": "Add new department", "Import From Exported File": "Import From File", "Add": "Add", "User's data file": "User's data file", "User's data zip file": "User's data zip file", "Overwrite User": "Overwrite User", "Import": "Import", "Company and Dept(s)": "Company and Dept(s)", "Please select a company or a department to start": "Please select a company or a department to start", "RENAME": "RENAME", "DELETE": "DELETE", "DETAILS": "DETAILS", "prev": "prev", "next": "next", "Search": "Search", "From": "From", "To": "To", "Dept(s)": "Dept(s)", "Export": "Export", "All": "All", "Active": "Active", "Inactive": "Inactive", "Lock": "Lock", "Cancel": "Cancel", "select company": "select company", "select department": "select department", "Please select a department": "Please select a department", "Delete": "Delete", "Please adjust your search fields": "Please adjust your search fields", "No Results": "No Results", "Loading...": "Loading...", "Deleting a user permanently cannot be undone": "Deleting a user permanently cannot be undone", "Please lock user before deleting": "Please lock user before deleting", "Confirm with your password to delete this user": "Confirm with your password to delete this user", "Change Time Access": "Change Time Access", "Lock Access": "Lock Access", "Unlock Access": "Unlock Access", "User status": "User status", "Search In Department": "Search In Department", "Read Data": "Read Data", "Companies": "Companies", "Departments": "Departments", "All in companies": "All in companies", "Select": "Select", "No results": "No results", "Loading": "Loading", "Face_Images": "Upload New Face Images", "Scan User": "<PERSON>an <PERSON>r", "Scan": "<PERSON><PERSON>", "Active Domain Name": "Active Domain Name", "Title": "Title", "Edit": "Edit", "Lock Update Face": "Lock Update Face", "Unlock Update Face": "Unlock Update Face", "Date": "Date", "Device Type(s)": "Device Type(s)", "Device(s)": "<PERSON>ce(s)", "Calculate": "Calculate", "Select excel template": "Select excel template", "Timekeeping only": "Timekeeping only", "results found": "results found", "device(s)": "device(s)", "dept(s)": "dept(s)", "user(s)": "user(s)", "New location name": "New location name", "Select access control device(s)": "Select device(s)", "Device Type": "Device Type", "Device Status": "Device Status", "Location Info": "Location Info", "AC Department": "AC Department", "AC User": "AC User", "AC Hour": "AC Hour", "AC Device": "AC Device", "AC Camera": "AC Camera", "Submit": "Submit", "Department(s)": "Department(s)", "Employee(s)": "Employee", "No time limit": "No time limit", "Create New AC Hour": "Create New AC Hour", "Add AC Hour to Location": "Add AC Hour to Location", "Add AC Camera To Location": "Add AC Camera To Location", "There are no records to display": "There are no records to display", "Please add AC Department or AC User first": "Please add AC Department or AC User first", "Please add new AC User": "Please add new AC User", "Please add new AC Deparment": "Please add new AC Deparment", "Please add new AC Device": "Please add new AC Device", "Step": "Step", "No AC Location": "No AC Location", "Name": "Name", "Start Date": "Start Date", "End Date": "End Date", "Start Time": "Start Time", "End Time": "End Time", "Repeat": "Repeat", "Update": "Update", "Next": "Next", "Previous": "Previous", "Choose Groups/Individuals": "Choose Groups/Individuals", "department(s) chosen": "department(s) chosen", "personnel chosen": "personnel chosen", "Choose AC Hour(s)": "Choose AC Hour(s)", "Review and Finish": "Review and Finish", "Create": "Create", "In Use": "In Use", "AC Location(s)": "AC Location(s)", "AC Department(s)": "AC Department(s)", "AC User(s)": "AC User(s)", "AC Hour(s)": "AC Hour(s)", "Camera(s)": "Camera(s)", "Please select group(s)/individual(s)": "Please select group(s)/individual(s)", "Please select AC Hour(s)": "Please select AC Hour(s)", "Config Key": "Config Key", "Config Value": "Config Value", "Restart Server": "Restart Server", "Confirm with your password to restart server": "Confirm with your password to restart server", "All service will unavaiable during restart": "All service will unavaiable during restart", "Table": "Table", "Device Update Status": "Device Update Status", "Sort by": "Sort by", "Order": "Order", "Device ID": "Device ID", "Device Name": "Device Name", "Authenticated": "Authenticated", "Version": "Version", "Licensed": "Licensed", "Timekeeping Device": "Timekeeping Device", "Default Device": "De<PERSON><PERSON>", "Firmware Auto-update": "Firmware Auto-update", "Face Result Editable": "Face Result Editable", "Reset Token": "Reset <PERSON>ken", "Success": "Success", "Updating": "Updating", "Fail": "Fail", "Date Created": "Date Created", "Last Updated Time": "Last Updated Time", "Ascending": "Ascending", "Descending": "Descending", "Activated": "Activated", "Locked": "Locked", "Sync": "Sync", "Update failed": "Update failed", "Updated": "Updated", "Lived": "Lived", "Max Age": "Max Age", "Vol": "Vol", "Current Vol": "Current Vol", "User view mode": "User view mode", "View by department": "View by department", "View by user": "View by user", "Companies/groups in device": "Companies/groups in device", "Update Manually": "Update Manually", "AI Services": "AI Services", "Camera": "Camera", "Face Recognition Service": "Face Recognition Service", "Person Recognition Service": "Person Recognition Service", "Camera type": "Camera type", "ROI Enable": "ROI Enable", "Undefined": "Undefined", "Check-in": "Check-in", "Check-out": "Check-out", "Zoom Enable": "Zoom Enable", "Refresh": "Refresh", "Processing Cameras": "Processing Cameras", "Add Camera": "Add Camera", "Update Cameras": "Update Cameras", "Camera Name": "Camera Name", "Type": "Type", "Action": "Action", "Delete Device": "Delete Device", "Add Device Group": "Add Device Group", "By organization(s)": "By organization(s)", "By user(s)": "By user(s)", "Shift": "Shift", "Date Range": "Date Range", "Create New Shift Type": "Create New Shift Type", "Start Hour": "Start Hour", "End Hour": "End Hour", "Flexible Hour": "Flexible Hour", "Export Raw data": "Export Raw Data", "Export By template": "Export By Template", "Add New Shift": "Add New Shift", "User": "User", "Template": "Template", "SHIFT": "SHIFT", "Current In/Out": "Current In/Out", "IN": "IN", "OUT": "OUT", "New In/Out": "New In/Out", "Reset": "Reset", "No records": "No records", "Device / Device Group": "Device / Device Group", "Create Device Group": "Create Device Group", "Eating Shift": "Eating Shift", "Time": "Time", "Create New Time Type": "Create New Time Type", "Display all results (including records missing in/out)": "Display all results (including records missing in/out)", "Settings": "Settings", "Shift Time": "Shift Time", "Default Shift(s)": "De<PERSON>ult <PERSON>(s)", "Default Shift": "De<PERSON>ult <PERSON>(s)", "Break": "Break", "Initial": "Initial", "Calculate Report": "Calculate Report", "All Shifts": "All Shifts", "Import Shift": "Import Shift", "Result Export": "Result Export", "Add Shift Type": "Add Shift Type", "Export Shift User": "Export Shift User", "Shift Assignment": "Shift Assignment", "Import Shift User": "Import Shift User", "Export All Result": "Export All Result", "Export Timekeeping Only": "Export Timekeeping Only", "Export Result On Call": "Export Result On Call", "Data Backup": "Data Backup", "Import Face Result": "Import Face Result", "Export Face Result": "Export Face Result", "Data file Face results": "Data file Face results", "Service Name": "Service Name", "Face result type": "Face result type", "Face Recognition": "Face Recognition", "Person Intrustion": "Person Intrustion", "Service type": "Service type", "Both-way": "Both-way", "Per page": "Per page", "Service Type": "Service Type", "Add service": "Add service", "Name can't be empty": "Name can't be empty", "Service Id": "Service Id", "Delete All Face Images": "Delete All Face Images", "Remove": "Remove", "Search Text": "Name/Id", "Add Success": "Camera added", "IP": "Camera IP", "RTSP": "RTSP", "Username": "Username", "Password": "Password", "Name error": "Camera name invalid", "IP error": "Camera IP invalid", "RTSP error": "Camera RTSP invalid", "Username error": "Camera username invalid", "Pass error": "Camera password invalid", "Delete Camera": "Delete Camera", "Port": "RTSP port", "Update Success": "Camera updated", "Add success": "Event added", "PerPage": "Select Top Result", "Search range": "Search range", "Search image": "Search image", "Start date err": "Invalid start date", "End date err": "Invalid end date", "Delay": "Search delay (small value will effect system perfomance)", "Add Attendant Time": "Add Attendant Time", "Search Holder": "Name/ID", "Name is required": "Name is required", "Is active": "Is Activated", "Is no time limit": "Not Limited By Time", "Is no user limit": "Not Limited By Users", "Attendant Device": "Attendant <PERSON>(s)", "Upload": "Upload", "Device Group": "Device Group", "Export By Departments": "Export By Departments", "Add Mannualy": "<PERSON><PERSON>", "No camera": "No camera setting", "Add camera": "Add camera to device", "Sync mannualy": "Sync Camera Mannualy", "Waitting": "Waitting", "Approved": "Approved", "Rejected": "Rejected", "Work at company": "Work at company", "Work at customer": "Work at customer", "Work at home": "Work at home", "Late": "Late", "Early": "Early", "On leave": "On leave", "Absent": "Absent", "Add Admin": "Add Admin", "Admin": "Admin", "Note": "Note", "Add VIP": "Add VIP", "Add Black List": "Add Black List", "Search by Face": "Search by Face", "Search by Feature": "Search by Feature", "Bag": "Bag", "Upper": "Upper", "Lower": "Lower", "Total Person(s) Found": "Total Person(s) Found", "Total User(s) Found": "Total User(s) Found", "Total Face(s) Found": "Total Face(s) Found", "Show Result Detail": "Show Result Detail", "Image": "Image", "Human Result(s)": "Human Result(s)", "User Result(s)": "User Result(s)", "Face Result(s)": "Face Result(s)", "Show video(s)": "Show video(s)", "Color": "Color", "Auto Refetch": "Auto Refetch", "Remove This Image": "Remove This Image", "Re-search": "Re-search", "Student Name": "Student Name", "Class Code": "Class Code", "Class Name": "Class Name", "Grade": "Grade", "Major Code": "Major Code", "Major Name": "Major Name", "Valid Until": "<PERSON>id <PERSON>", "Remove Student": "Remove Student", "Add Student": "Add Student", "Notification": "Notification", "Yay! You have seen it all": "Yay! You have seen it all", "Mark As Read": "<PERSON>", "No": "No", "Yes": "Yes", "Auto Backup": "Auto Backup", "Export Only": "Export Only", "Auto Increament Only": "Auto Increament Only", "Discard": "Discard", "Fill this field to scan": "Fill this field to scan", "Entries": "Entries", "Show": "Show", "ADD MODE": "ADD MODE", "Dashboards": "Dashboards", "You can use letters, numbers & periods": "You can use letters, numbers & periods", "Add New Black List User": "Add New Black List User", "Add New VIP User": "Add New VIP User", "Filters": "Filters", "Import Data Backup": "Import Data Backup", "Data File Backup": "Data File Backup", "Restart CoreBE": "Restart <PERSON>", "Restart Scheduler": "Restart Scheduler", "Restart FE": "Restart FE", "Restart Report": "Restart Report", "Restart All Server": "<PERSON>art All Server", "Restart PM2 Thread": "Restart PM2 Thread", "Change User Role": "Change User Role", "Student Information": "Student Information", "Updating student details will receive a privacy audit": "Updating student details will receive a privacy audit", "Add Shift User Filter": "Add Shift User Filter", "Exclude Filter User": "Exclude Filter User", "Exclude Rule User": "Exclude Rule User", "Download": "Download", "Create New Time": "Create New Time", "Create New Location": "Create New Location", "Device(s) In Use": "Device(s) In Use", "Select New Device(s)": "Select New Device(s)", "Read all notifications": "Read all notifications", "Notifications": "Notifications", "Do you want to read this notification?": "Do you want to read this notification?", "Do you want to read all notifications?": "Do you want to read all notifications?", "MODE": "MODE", "View Image Profile": "View Image Profile", "User Information": "User Information", "Updating user details will receive a privacy audit": "Updating user details will receive a privacy audit", "Password mismatch": "Password mismatch", "Confirm with your password to add this image": "Confirm with your password to add this image", "Check Quality Image": "Check Quality Image", "Save Config": "Save Config", "Edit Device": "<PERSON>", "Organization": "Organization", "Organization:": "Organization:", "Add Organization": "Add Organization", "Add Organization User": "Add Organization User", "Organization Name": "Organization Name", "Please select an organization": "Please select an organization", "Add Remote Recognize User": "Add Remote Recognize User", "Remote Recognize User": "Remote Recognize User", "Add Attendant Device": "Add Attendant <PERSON>", "Edit Attendant Time": "Edit Attendant Time", "-": "- : <PERSON><PERSON><PERSON> không phải đi làm theo ca làm việc (<PERSON><PERSON> đi làm chấm công thì cũng ko ghi nhận)", "Off": "Off: <PERSON><PERSON><PERSON><PERSON> đi làm", "Half": "Half: <PERSON><PERSON><PERSON>y", "Full -": "Full -: <PERSON><PERSON>, vi ph<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> không bị phạt", "Full (P-P)": "Full (P-P): <PERSON><PERSON> công nhưng là đăng ký nghỉ phép cả ngày", "Full (P-.)": "Full (P-.): <PERSON><PERSON> công nhưng đăng ký nghỉ phép buổi sáng", "Full (.-P)": "Full (.-P): <PERSON><PERSON> công nhưng đăng ký nghỉ phép buổi chiều", "Half (.-P)": "Half (.-P): <PERSON><PERSON><PERSON><PERSON><PERSON>, ghi nhận công do đăng ký phép buổi chiều", "Full ---": "Full ---: <PERSON><PERSON>, b<PERSON>t", "Half ---": "Half ---: <PERSON><PERSON><PERSON>, b<PERSON>t", "Quater": "Quater: 1/4 công", "Total Working Days": "Total Working Days", "Actual Working Days": "Actual Working Days", "Paid Leave": "<PERSON>id <PERSON>", "Function": "Function", "Read": "Read", "New Company/Department": "New Company/Department", "New User": "New User", "Import Users": "Import Users", "Download template file here!!": "Download template file here!!", "Overwrite Users": "Overwrite Users", "File not selected": "File not selected", "Imported Successfully": "Imported Successfully", "Imported Failed": "Imported Failed", "Export Users": "Export Users", "Deactive": "Deactive", "True": "True", "False": "False", "Excel file": "Excel file", "Zip file": "Zip file", "Job Id": "Job Id", "Total Image": "Total Image", "Total User": "Total User", "Errors": "Errors", "Finished": "Finished", "New VIP User": "New VIP User", "New Black List User": "New Black List User", "Create New Shift Explanation": "Create New Shift Explanation", "Create Shift Explanation": "Create Shift Explanation", "In times": "In times", "Out times": "Out times", "Unknown times": "Unknown times", "First in": "First in", "Last out": "Last out", "Please input title": "Please input title", "Please input detail": "Please input detail", "Created Feedback Successfully": "Created Feedback Successfully", "Create New Feedback": "Create New Feedback", "Detail": "Detail", "Reply Detail": "<PERSON><PERSON>", "Image 1": "Image 1", "Images": "Images", "Error Account": "Error Account", "Error Data": "Error Data", "Error System": "Error System", "Description": "Description", "Start": "Start", "End": "End", "Break Start": "Break Start", "Break End": "Break End", "Set Auto Days": "Set Auto Days", "Successfully": "Successfully", "Search Shift Type": "Search Shift Type", "Add Shift Mannualy": "Add Shift Mannualy", "Added successfully": "Added successfully", "File is required": "File is required", "Updated successfully": "Updated successfully", "Edit Shift": "Edit Shift", "Set Auto": "Set Auto", "Exclude Filter User ID": "Exclude Filter User ID", "Exclude Rule User ID": "Exclude Rule User ID", "Report File Name": "Report File Name", "Processed": "Processed", "Add Shift Default": "Add Shift Default", "Mode": "Mode", "Calculate Shift User": "Calculate Shift User", "All Users": "All Users", "Confirm with your password to calculate!": "Confirm with your password to calculate!", "This action cannot be undone": "This action cannot be undone", "Time is required": "Time is required", "Register Full Shift For User": "Register Full Shift For User", "Add Shift User Admin": "Add Shift User Admin", "User is required": "User is required", "Company is required": "Company is required", "Department is required": "Department is required", "Export Data": "Export Data", "Import Data": "Import Data", "Import Shift User Admin Explanation": "Import Shift User Admin Explanation", "Data File": "Data File", "Export By Department": "Export By Department", "Organization is required": "Organization is required", "Start date is required": "Start date is required", "End date is required": "End date is required", "Add New": "Add New", "Card Image": "Card Image", "Birthday": "Birthday", "Card ID": "Card ID", "Nationality": "Nationality", "Place Of Birth": "Place Of Birth", "Address": "Address", "Expired Time": "Expired Time", "Full User Name": "Full User Name", "Access": "Access", "Location": "Location", "Location is required": "Location is required", "Check In/Out": "Check In/Out", "Location Name": "Location Name", "Add AC Location": "Add AC Location", "Add New Guest Access To Location": "Add New Guest Access To Location", "Guest": "Guest", "Duration": "Duration", "New Student": "New Student", "Recognition Type": "Recognition Type", "Device": "<PERSON><PERSON>", "Tracking ID": "Tracking ID", "All Faces": "All Faces", "Age": "Age", "Emotion": "Emotion", "Remote Status": "Remote Status", "Denied": "Denied", "Processing": "Processing", "Display all results": "Display all results", "Add Mobile Admins": "Add Mobile Admins", "Remove Mobile Admins": "Remove Mobile Admins", "Please select at least one admin": "Please select at least one admin", "Please select at least one user": "Please select at least one user", "Please select at least one company": "Please select at least one company", "Please select at least one department": "Please select at least one department", "Please select at least one organization": "Please select at least one organization", "Add Mobile Approvers": "Add Mobile Approvers", "Approver": "Approver", "Month Limit": "Month Limit", "Month Used": "Month Used", "Set Limit": "<PERSON>", "View Mode": "View Mode", "Set Recognize Limit": "Set Recognize Limit", "Must select an integer": "Must select an integer", "Last Time Signal": "Last Time Signal", "Uptime": "Uptime", "Memory": "Memory", "Free Disk": "Free Disk", "Progress": "Progress", "Device IP": "Device IP", "Uptime (Hours)": "Uptime (Hours)", "Total Memory": "Total Memory", "User Memory": "User Memory", "Reboot Device": "Reboot Device", "Reboot device?": "Reboot Device?", "Service witl temporay unavaiable": "Service witl temporay unavaiable", "Reset Successfully": "Reset Successfully", "All data saved in device will be removed.": "Tất cả dữ liệu đư<PERSON><PERSON> lưu trong thiết bị sẽ bị xóa.", "Yes, delete it!": "Yes, delete it!", "Deleted!": "Delete!", "Your device has been deleted.": "Your device has been deleted.", "Signal Sent": "Signal Sent", "Camera Ip": "Camera Ip", "Language": "Language", "Door Access": "Door Access", "Sound": "Sound", "Server IP": "Server IP", "Server Port": "Server Port", "Face Confident": "Face Confident", "BBox Margin": "BBox Margin", "Align Mode": "Align Mode", "Max Size": "<PERSON>", "Face Best Shot Duration": "Face Best Shot Duration", "Named Sent Duration": "Named Sent Duration", "Face Best Shot Enable": "Face Best Shot Enable", "Person Detect Confident": "Person Detect Confident", "Kpt Confident": "Kpt Confident", "Person Best Shot Duration": "Person Best Shot Duration", "Fas Validation Count": "Fas Validation Count", "Fas Confident": "Fas Confident", "Fake Face Enable": "Fake Face Enable", "Server Url": "Server Url", "Time Out": "Time Out", "Camera IP": "Camera IP", "Camera User": "Camera User", "Camera Pass": "Camera Pass", "Camera RTSP": "Camera RTSP", "Camera FPS": "Camera FPS", "Do you want to remove ": "Do you want to remove ", "You won't be able to revert this!": "You won't be able to revert this!", "Your ROI has been deleted.": "Your ROI has been deleted.", "ROI is not available! Please delete and choose again!": "ROI is not available! Please delete and choose again!", "Can not choose this service! Please choose another one!": "Can not choose this service! Please choose another one!", "AI service is required": "AI service is required", "ROI is required": "ROI is required", "All config for device will be removed.": "All config for device will be removed.", "ROI List": "ROI List", "ROI Configuration": "ROI Configuration", "Create New ROI": "Create New ROI", "PersonAreaDetection": "PersonAreaDetection", "PersonAreaIntrusion": "PersonAreaIntrusion", "FaceAreaDetection": "FaceAreaDetection", "FaceAreaIntrusion": "FaceAreaIntrusion", "PersonLineCrossing": "PersonLineCrossing", "AI Service Name": "AI Service Name", "Total Configs": "Total Configs", "Config(s)": "Config(s)", "Total Cameras": "Total Cameras", "Camera.Name error": "Camera.Name error", "Camera.IP error": "Camera.IP error", "Camera.RTSP error": "Camera.RTSP error", "All config for camera will be removed.": "All config for camera will be removed.", "Your camera has been deleted.": "Your camera has been deleted.", "Camera Config": "Camera Config", "Frame Rate": "Frame Rate", "Stream Mode": "Stream Mode", "No Stream": "No Stream", "Stream RTSP": "Stream RTSP", "Record File": "Record File", "Stream RTSP & Record File": "Stream RTSP & Record File", "Record Duration": "Record Duration", "Input Codec": "Input Codec", "Output Codec": "Output Codec", "Distance Min": "Distance Min", "Send Token": "Send Token", "Add New Camera": "Add New Camera", "Device is required": "Device is required", "Remove this device group?": "Remove this device group?", "Remove this device group will also delete other information related to this device group.": "Remove this device group will also delete other information related to this device group.", "Your device group has been deleted.": "Your device group has been deleted.", "Device Group Name": "Device Group Name", "Device In Group": "Device In Group", "Search Device Group": "Search Device Group", "Add New Device Group": "Add New Device Group", "Add AC Device": "Add AC Device", "Face Terminal": "Face Terminal", "Stream Recognizer": "Stream Recognizer", "Stream Detector": "Stream Detector", "Screen Display": "Screen Display", "Access Control": "Access Control", "Full Functions": "Full Functions", "Display Only WPF": "Display Only WPF", "Server Recog Channel": "Server Recog Channel", "WPF Face Recog": "WPF Face Recog", "AI Box": "AI Box", "AI Camera": "AI Camera", "Windows App": "Windows App", "Cloud Camera": "Cloud Camera", "Location name can't be blank!": "Location name can't be blank!", "Select All": "Select All", "Unselect All": "Unselect All", "Remove this department from the location?": "Remove this department from the location?", "Your department has been deleted.": "Your department has been deleted.", "Deparment Name": "Deparment Name", "set no time limit": "set no time limit", "restore access hour(s)": "restore access hour(s)", "Do you want to ": "Do you want to ", "Changed Successfully": "Changed Successfully", "Remove this AC Hour from the location?": "Remove this AC Hour from the location?", "Your AC Hour has been deleted.": "Your AC Hour has been deleted.", "Weekdays": "Weekdays", "Actions": "Actions", "Security & AI": "Security & AI", "Search (Location, Dept, User)": "Search (Location, Dept, User)", "Do you want to permanently delete this location?": "Do you want to permanently delete this location?", "Your location has been deleted.": "Your location has been deleted.", "Remove this user from the location?": "Remove this user from the location?", "Your user has been deleted.": "Your user has been deleted.", "Avatar": "Avatar", "Add AC Department To Location": "Add AC Department To Location", "Add AC Hour": "Add AC Hour", "Add AC User To Location": "Add AC User To Location", "Edit AC Hour": "Edit AC Hour", "Add Dashboard Notification Alert": "Add Dashboard Notification Alert", "Add Mobile Notification Alert": "Add Mobile Notification Alert", "Mobile Notification": "Mobile Notification", "Dashboard Notification": "Dashboard Notification", "Send email": "Send email", "Level": "Level", "Range Limit": "Range Limit", "Time Limit": "Time Limit", "Actor": "Actor", "Alert Rules": "Alert <PERSON>", "Remove this user from the alert rule?": "Remove this user from the alert rule?", "User will be removed from the alert rule.": "User will be removed from the alert rule.", "Removed Successfully": "Removed Successfully", "This mail will be removed from the alert rule.": "This mail will be removed from the alert rule.", "Receiver Mail": "Receiver Mail", "Confident": "Confident", "View Video": "View Video", "Human Action": "Human Action", "Attributes": "Attributes", "Human Tracking": "Human Tracking", "Body color is": "Body color is", "Lower color is": "Lower color is", "disabled": "disabled", "File invalid": "File invalid", "Create Smart Search": "Create Smart Search", "Face Captured": "Face Captured", "Image(s)": "Image(s)", "Confidence": "Confidence", "No record found": "No record found", "Human Captured": "Human Captured", "Locations": "Locations", "Last seen": "Last seen", "Human Result": "Human Result", "User Result": "User Result", "Face Result": "Face Result", "Re-search Successfully": "Re-search Successfully", "Human Found": "Human Found", "User Found": "User Found", "Face Found": "Face Found", "Smart Search": "Smart Search", "Confidence Without Mask": "Confidence Without Mask", "Confidence With Mask": "Confidence With Mask", "Alarm Events": "Alarm Events", "Please select an image": "Please select an image", "Score": "Score", "Facebook Url": "Facebook Url", "Social Search": "Social Search", "Choose image to search": "Choose image to search", "Remove Image": "Remove Image", "Result": "Result", "Confidence human": "Confidence human", "value:": "value:", "Confidence user": "Confidence user", "Confidence face": "Confidence face", "Auto Backup Successfully": "Auto Backup Successfully", "Table Name": "Table Name", "Backup Date": "Backup Date", "ClusterSync": "ClusterSync", "Running": "Running", "Restore this backup?": "Restore this backup?", "Restored!": "Restored!", "Your backup has been restored.": "Your backup has been restored.", "Remove this backup file?": "Remove this backup file?", "Your backup file has been deleted.": "Your backup file has been deleted.", "Server Settings": "Server Settings", "Please select file data backup": "Please select file data backup", "Please select table": "Please select table", "You want to run backup?": "You want to run backup?", "Backup Success!": "Backup Success!", "Your backup has been successfully!": "Your backup has been successfully!", "Manual Import": "Manual Import", "Organization Full Info": "Organization Full Info", "All Employee": "All Employee", "Timekeeping Logs By Day": "Timekeeping Logs By Day", "Timekeeping Logs By Month": "Timekeeping Logs By Month", "Month Work Time": "Month Work Time", "Name invalid": "Name invalid", "Add Group": "Add Group", "Group Name": "Group Name", "Group Type": "Group Type", "All Type": "All Type", "Device Monitor": "Device Monitor", "Copied To Clipboard !": "Copied To Clipboard !", "Telegram ID": "Telegram ID", "Telegram User Name": "Telegram User Name", "Telegram First Name": "Telegram First Name", "Telegram Token": "Telegram Token", "Add Telegram Notification Time": "Add Telegram Notification Time", "Telegram Group User": "Telegram Group User", "Receiver": "Receiver", "Noti Time": "Noti Time", "Notification Time": "Notification Time", "Add Telegroup": "Add Telegroup", "Delete this time?": "Delete this time?", "Your time has been deleted.": "Your time has been deleted.", "Start Min": "Start Min", "Start Sec": "Start Sec", "End Min": "End Min", "End Sec": "End Sec", "Telegram Notification Time": "Telegram Notification Time", "Add Notify Department": "Add Notify Department", "First Name": "First Name", "Last Name": "Last Name", "Department Id": "Department Id", "Company Name": "Company Name", "Department Name": "Department Name", "Company Id": "Company Id", "Device Id": "<PERSON>ce Id", "Telegram Group": "Telegram Group", "Group ID": "Group ID", "Notify Receivers": "Notify Receivers", "Notify Users": "Notify Users", "Notify Departments": "Notify Departments", "Notify Companies": "Notify Companies", "Notify Devices": "Notify Devices", "Notify Times": "Notify Times", "Thread Name is required": "Thread Name is required", "Add PM2 Thread": "Add PM2 Thread", "Thread Name": "Thread Name", "CoreBackend": "CoreBackend", "Scheduler Only": "Scheduler Only", "Report Only": "Report Only", "Front End": "Front End", "Thread Deleted": "Thr<PERSON> Deleted", "Confirm with your password to delete this thread": "Confirm with your password to delete this thread", "Deleting a thread permanently cannot be undone": "Deleting a thread permanently cannot be undone", "Server restarting": "Server restarting", "CoreBE Restarting": "CoreBE Restarting", "Front End Restarting": "Front End Restarting", "Report Restarting": "Report Restarting", "Thread restarting": "Thread restarting", "Scheduler Restarting": "Scheduler Restarting", "Restart Server PM2 Thread": "Restart Server PM2 Thread", "Key": "Key", "Value": "Value", "Server Config": "Server Config", "Server PM2 Thread": "Server PM2 Thread", "Function Id": "Function Id", "Message": "Message", "New Value": "New Value", "System Log": "System Log", "Telegram Config": "Telegram Config", "OTT Config": "OTT Config", "Total Users": "Total Users", "Active Users In Month": "Active Users In Month", "Active Users In Day": "Active Users In Day", "Add Organization Master": "Add Organization Master", "Organizations Admin": "Organizations Admin", "Home": "Home", "VIP User": "VIP User", "User In/Out": "User In/Out", "Timeshift": "Timeshift", "Shift Explanation": "Shift Explanation", "Report": "Report", "Face ID": "Face ID", "Payroll": "Payroll", "Feedback": "<PERSON><PERSON><PERSON>", "Black List": "Black List", "Typography": "Typography", "Layout without menu": "Layout without menu", "Layouts": "Layouts", "All User": "All User", "AI Devices": "AI Devices", "Feather Icons": "Feather Icons", "All Organizations": "All Organizations", "Statistics Cards": "Statistics Cards", "Cards": "Cards", "No Icons Found!": "No Icons Found!", "Icon Name Copied! 📋": "Icon Name Copied! 📋", "Basic Cards": "Basic Cards", "Analytics Cards": "Analytics Cards", "Advanced Cards": "Advanced Cards", "Card Actions": "Card Actions", "Icon": "Icon", "Details": "Details", "Collapse": "Collapse", "School": "School", "User Roles": "User Roles", "Permission": "Permission", "VIP Users": "VIP Users", "Shift Management": "Shift Management", "All Shift Type": "All Shift Type", "Shift Explanation Admin": "Shift Explanation Admin", "Shift Report": "Shift Report", "Attendant": "Attendant", "Attendant Time": "Attendant Time", "User Config": "User Config", "Guests": "Guests", "All Students": "All Students", "Medical": "Medical", "Patients": "Patients", "Unknown": "Unknown", "Fake Result": "<PERSON><PERSON> Result", "Tree View": "Tree View", "Company View": "Company View", "ORGANIZATION": "ORGANIZATION", "Mobile Face ID": "Mobile Face ID", "Mobile Results": "Mobile Results", "Mobile Admins": "Mobile Admins", "Mobile Approvers": "Mobile Approvers", "Mobile Users": "Mobile Users", "Create Feedback": "Create <PERSON><PERSON><PERSON>", "ShiftExplanation Ad": "ShiftExplanation Ad", "Face Results": "Face Results", "Fake Results": "Fake Results", "Remove Remote Recognize User": "Remove Remote Recognize User", "Add AI Service": "Add AI Service", "Add New AI Service": "Add New AI Service", "AI Service Type": "AI Service Type", "Support": "Support", "CIVAMS Mobile Apps": "CIVAMS Mobile Apps", "Login": "<PERSON><PERSON>", "DEVICES": "DEVICES", "Events": "Events", "ADD AD Users": "ADD AD Users", "Email can't be empty": "Email can't be empty", "Must select a company": "Must select a company", "Must select a department": "Must select a department", "User name invalid": "User name invalid", "More Actions": "More Actions", "Enable Update Face Image": "Enable Update Face Image", "Enable Check From Mobile": "Enable Check From Mobile", "Edit Shift User": "Edit Shift User", "Default by shift user": "Default by shift user", "User by day": "User by day", "Shift Default": "Shift Default", "Assign Shift": "Assign <PERSON>", "Export By Template": "Export By Template", "Add Shift User": "Add Shift User", "No records found": "No records found", "Export Sign PDF": "Export Sign PDF", "New Camera": "New Camera", "Attribute": "Attribute", "Attribute(s)": "Attribute(s)", "Children": "Children", "Adult": "Adult", "Carrying backpack": "Carrying backpack", "Wearing hat": "Wearing hat", "Long hair": "Long hair", "Short hair": "Short hair", "Wearing long sleeve": "Wearing long sleeve", "Wearing short sleeve": "Wearing short sleeve", "Wearing trouser jean": "Wearing trouser jean", "Wearing skirt": "Wearing skirt", "Wearing shorts": "Wearing shorts", "Wearing PPE head": "Wearing PPE head", "None wearing PPE head": "None wearing PPE head", "Wearing PPE body": "Wearing PPE body", "None wearing PPE body": "None wearing PPE body", "Vehicle": "Vehicle", "Traffic": "Traffic", "Vehicle Search": "Vehicle Search", "LANE IN": "LANE IN", "LANE OUT": "LANE OUT", "Open Barrie": "Open Barrie", "Face Image and License Plate are not matched": "Face Image and License Plate are not matched", "Checkin and checkout are not matched": "Checkin and checkout are not matched", "IParking License": "IParking License", "IParking Config": "IParking Config", "IParking History": "IParking History", "IParking Result": "IParking Result", "License Plate": "License Plate", "Lane": "Lane", "Lane Name": "Lane Name", "Lane In": "Lane In", "Lane Out": "Lane Out", "Lane Type": "Lane Type", "Vehicle Type": "Vehicle Type", "Barrie Mode": "<PERSON><PERSON>", "Channel": "Channel", "Parking Lane Info": "Parking Lane Info", "Add Device To Parking Lane": "Add Device To Parking Lane", "Add Parking Lane": "Add Parking Lane", "License Plate Info": "License Plate Info", "Add License Plate": "Add License Plate", "Create Vehicle Search": "Create Vehicle Search", "Human Confident": "Human Confident", "User Confident": "User Confident", "Add Device": "Add <PERSON>", "Attendant Devices": "Attendant Devices", "Add Att Devices": "Add Att Devices", "Attendant Users": "Attendant Users", "Add Att Users": "Add Att Users", "Un-Attendant Users": "Un-Attendant Users", "Un-Attendant Guests": "Un-Attendant Guests", "Un-Attendant Unknown": "Un-Attendant Unknown", "User/Guest": "User/Guest", "License Plate Image": "License Plate Image", "User(s)": "User(s)", "Checked": "Checked", "Un-Checked": "Un-Checked", "Organizations": "Organizations", "IParking": "IParking", "IParking Dashboard": "IParking Dashboard", "License Parking Summary": "License Parking Summary", "License Parking Left In": "License Parking Left In", "Screenshots": "Screenshots", "Door Hold Time": "Door Hold Time", "Devices Name": "Devices Name", "Guest Detail": "Guest Detail", "Attendant Guests": "Attendant Guests", "Add Att Guests": "Add <PERSON>s", "None": "None", "Para-Pro": "Para-Pro", "Professional": "Professional", "Management": "Management", "Excutive": "Excutive", "Security Dashboard": "Security Dashboard", "History Detail": "History Detail", "Meeting Config": "Meeting Config", "Meeting Detail": "Meeting Detail", "Live View": "Live View", "User Checked": "User Checked", "Guest Checked": "Guest Checked", "Employee Management": "Employee Management", "Violation": "Violation", "Compliance": "Compliance", "Customers/Unknowns Management": "Customers/Unknowns Management", "Wearing Card": "Wearing Card", "Wearing Uniform": "Wearing Uniform", "In Allowed Time": "In Allowed Time", "First In": "First In", "Last Out": "Last Out", "Card": "Card", "Uniform": "Uniform", "No videos available": "No videos available", "Violation History": "Violation History", "Movement History": "Movement History", "Detail Violation": "Detail Violation", "Employee Profile": "Employee Profile", "Location(s)": "Location(s)", "Face Recognized": "Face Recognized", "Att Filter Mode": "Att Filter Mode", "Debug Info": "Debug Info", "Refetch": "Refetch", "Meeting Devices": "Meeting Devices", "Meeting Users": "Meeting Users", "Meeting Guests": "Meeting Guests", "Un-Meeting Users": "Un-Meeting Users", "Un-Meeting Guests": "Un-Meeting Guests", "Un-Meeting Unknown": "Un-Meeting Unknown", "Users Invited": "Users Invited", "User Absent": "User Absent", "Meeting": "Meeting", "Show Case": "Show Case", "Human Search": "Human Search", "Security": "Security", "Access Control Alert": "Access Control Alert", "Access Control Log": "Access Control Log", "Video Management": "Video Management", "Work Location": "Work Location", "Add Location": "Add Location", "Location Detail": "Location Detail", "Add Device Into Location": "Add Device Into Location", "Integration": "Integration", "Add Face Integration": "Add Face Integration", "Company MGMT": "Company MGMT", "Uniform Dashboard": "Uniform Dashboard", "Violation Dashboard": "Violation Dashboard", "Age & Emotion": "Age & Emotion", "Average Statistics": "Average Statistics", "Average Positive Emotions": "Average Positive Emotions", "Average Negative Emotions": "Average Negative Emotions", "Average Neutral Emotions": "Average Neutral Emotions", "Average Emotion Distribution": "Average Emotion Distribution", "Average Emotion Intensity Distribution": "Average Emotion Intensity Distribution", "Positive": "Positive", "Negative": "Negative", "Neutral": "Neutral", "No data available": "No data available", "No data available for the selected criteria": "No data available for the selected criteria", "Key Statistics": "Key Statistics", "Total People": "Total People", "Average Age": "Average Age", "Most Common Age Group": "Most Common Age Group", "Age Group Distribution": "Age Group Distribution", "Detailed Age Distribution": "Detailed Age Distribution", "Children (0-12)": "Children (0-12)", "Teenager (13-19)": "Teenager (13-19)", "Young Adult (20-35)": "Young Adult (20-35)", "Adult (36-50)": "Adult (36-50)", "Senior (51+)": "Senior (51+)", "Detail Result": "Detail Result", "Cached": "<PERSON><PERSON><PERSON>", "Face Track": "Face Track", "Devices Admin": "<PERSON><PERSON>", "Config": "Config", "General": "General", "Plate Number": "Plate Number", "Customer": "Customer", "Customer(s)": "Customer(s)", "Expired Date": "Expired Date", "Check-in By Plate": "Check-in By Plate", "Check-out By Plate": "Check-out By Plate", "URL": "URL", "Authentication URL": "Authentication URL", "Configs": "Configs", "IParking Config Detail": "IParking Config Detail", "Add IParking Config": "Add IParking Config", "Add IParking Vehicle": "Add IParking Vehicle", "Free Memory": "Free Memory", "License Expired Date": "License Expired Date", "License Max User": "License Max User", "MAC Address": "MAC Address", "Enable Device": "Enable Device", "Drop Frame": "Drop Frame", "Cameras Used": "Cameras Used", "ROI Used": "ROI Used", "Device & Camera": "Device & Camera", "Face History": "Face History", "Guest History": "Guest History", "Finger Print": "Finger Print", "User History Report": "User History Report", "Guest History Report": "Guest History Report", "User In Out Report": "User In Out Report", "Full": "Full", "User ID": "User ID", "Server Usage": "Server Usage", "CPU Usage": "CPU Usage", "Memory Usage": "Memory Usage", "Disk Usage": "Disk Usage", "unknown": "unknown", "Sync Data": "Sync Data", "Sync Iparking Time": "Sync Iparking Time", "Code": "Code", "Group": "Group", "Identity": "Identity", "Plate Number Comparison": "Plate Number Comparison", "Full Comparison": "Full Comparison", "Free": "Free", "NONE": "NONE", "Exactly": "Exactly", "Plate Number Validation": "Plate Number Validation", "BIKE": "BIKE", "Bike": "Bike", "bike": "bike", "CAR": "CAR", "Car": "Car", "car": "car", "MOTORBIKE": "MOTORBIKE", "Motorbike": "Motorbike", "IN USE": "IN USE", "PLATE NUMBER": "PLATE NUMBER", "FACE ID": "FACE ID", "FINGER PRINT": "FINGER PRINT", "QR CODE": "QR CODE", "Re ID": "Re ID", "MQTT Config": "MQTT Config", "MQTT Add In": "MQTT Add In", "Access V2": "Access V2", "Device Check Type": "Device Check Type", "Statistics of Quantity": "Statistics of Quantity", "Vehicle In Parking": "Vehicle In Parking", "Vehicle In Out": "Vehicle In Out", "Access by Area": "Access by Area", "Motobike": "Motobike", "Violations of leaving early, arriving late": "Violations of leaving early, arriving late", "Violations of leaving early": "Violations of leaving early", "Violations of arriving late": "Violations of arriving late", "Company(s)": "Company(s)", "Violation Mode": "Violation Mode", "Uniform Or Card Violation": "Uniform Or Card Violation", "Uniform Violation Only": "Uniform Violation Only", "Card Violation Only": "Card Violation Only", "Time Violation Only": "Time Violation Only", "Readed": "Readed", "Your notification has been read.": "Your notification has been read.", "All notification has been read.": "All notification has been read.", "Process": "Process", "Completed": "Completed", "Not Started": "Not Started", "Unique Videos": "Unique Videos", "Unique ReIDs": "Unique ReIDs", "Total Appearances": "Total Appearances", "File Name": "File Name", "Appearances": "Appearances", "Video Groups": "Video Groups", "Video Group": "Video Groups", "Video Face ReID": "Video Face ReID", "Label": "Label", "Count Seen": "Count <PERSON>", "Scene Name": "Scene Name", "Start Scene Time": "Start Scene Time", "End Scene Time": "End Scene Time", "Event Count": "Event Count", "Detail ReID In Video": "Detail ReID In Video", "Add Video Face ReID Group": "Add Video Face ReID Group", "Detail Video Face ReID Group": "Detail Video Face ReID Group", "Event In/Out By Group": "Event In/Out By Group", "In": "In", "Out": "Out", "Event In/Out By Time": "Event In/Out By Time", "Forbidden": "Forbidden", "Reference User": "Reference User", "Maternity-related benefit": "Maternity-related benefit", "Late or not in": "Late or not in", "Early or not out": "Early or not out"}