import { Circle } from 'react-feather';
import { FaTrafficLight } from "react-icons/fa";
import { GiTrafficLightsGreen } from "react-icons/gi";

export default [
    {
        id: '/security/vehicle',
        title: 'Vehicle',
        resource: 'VEHICLE_SEARCH',
        icon: <GiTrafficLightsGreen size={20} />,
        children: [
            {
                id: '/security/vehicle/traffic',
                title: 'Traffic',
                resource: 'VEHICLE_SEARCH',
                icon: <Circle size={20} />,
                navLink: '/security/vehicle/traffic'
            },
            {
                id: '/security/vehicle/vehicle-search',
                title: 'Vehicle Search',
                resource: 'VEHICLE_SEARCH',
                icon: <Circle size={20} />,
                navLink: '/security/vehicle/vehicle-search'
            },
        ]
    },
]
