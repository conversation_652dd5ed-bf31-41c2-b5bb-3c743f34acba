import { Circle } from 'react-feather';
import { PiSecurityCamera } from "react-icons/pi";

export default [
    {
        id: '/security',
        title: 'Security',
        resource: 'USERS',
        icon: <PiSecurityCamera size={20} />,
        children: [
            {
                id: '/security/security-events',
                title: 'Alarm Events',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/security-events'
            },
            {
                id: '/security/alert-rule',
                title: 'Alert Rules',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/alert-rule'
            },
            {
                id: '/security/access-control-alert',
                title: 'Access Control Alert',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/access-control-alert'
            },
            {
                id: '/security/access-control',
                title: 'Access Control',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/access-control'
            },
            {
                id: '/security/access-control-beta',
                title: 'Access V2',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/access-control-beta'
            },
            {
                id: '/security/access-control-mb',
                title: 'Access MB',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/access-control-MB'
            },
            {
                id: '/security/door-log',
                title: 'Access Control Log',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/door-log'
            },
            {
                id: '/security/event-video',
                title: 'Video Management',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/event-video'
            },
            {
                id: '/security/live-view-video',
                title: 'Live View',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/security/live-view-video'
            },
        ]
    }
]
