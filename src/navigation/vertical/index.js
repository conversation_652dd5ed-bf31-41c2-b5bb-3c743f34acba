// ** Navigation imports
import dashboards from './dashboards'
import user from './user'
import security from './security'
import device from './device'
import setting from './setting'
import role from './role'
import shiftManagement from './shift-management'
// import attendant from './attendant'
import guest from './guest'
import school from './school'
import medical from './medical'
import faceId from './face-id'
import mobileFaceId from './mobile-face-id'
import humanSearch from './human-search'
import vehicleSearch from './vehicle-search'
import iparking from './iparking'
import support from './support'
import bidv from './bidv'
import video from './video'

// ** Merge & Export
export function getDomainMenu() {
    const domain = typeof window !== 'undefined' ? localStorage.getItem('domain') : null;
    if (!domain) {
        return [
            ...dashboards,
            ...bidv,
            ...role,
            ...user,
            ...shiftManagement,
            // ...attendant,
            ...guest,
            ...school,
            ...medical,
            ...faceId,
            ...mobileFaceId,
            ...humanSearch,
            ...security,
            ...video,
            ...vehicleSearch,
            ...iparking,
            ...device,
            ...setting,
            ...support,
        ];
    }
    const domainMap = {
        Face: [
            ...dashboards,
            ...role,
            ...user,
            ...shiftManagement,
            ...faceId,
            ...mobileFaceId,
            ...device,
            ...setting,
            ...support,
        ],
        Security: [
            ...dashboards,
            ...bidv,
            ...role,
            ...user,
            ...faceId,
            ...guest,
            ...humanSearch,
            ...security,
            ...video,
            ...device,
            ...setting,
            ...support,
        ],
        Traffic: [
            ...dashboards,
            ...vehicleSearch,
            ...iparking,
            ...device,
            ...setting,
            ...support,
        ],
        Medical: [
            ...dashboards,
            ...role,
            ...user,
            ...medical,
            ...faceId,
            ...device,
            ...setting,
            ...support,
        ],
        Bank: [
            ...dashboards,
            ...bidv,
            ...role,
            ...user,
            ...guest,
            ...shiftManagement,
            ...faceId,
            ...mobileFaceId,
            ...humanSearch,
            ...security,
            ...video,
            ...device,
            ...setting,
            ...support,
        ],
        School: [
            ...dashboards,
            ...role,
            ...user,
            ...shiftManagement,
            ...school,
            ...faceId,
            ...mobileFaceId,
            ...device,
            ...setting,
            ...support,
        ],
    };
    return domainMap[domain] || [];
}