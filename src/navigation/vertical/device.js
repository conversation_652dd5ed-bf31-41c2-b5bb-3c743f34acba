import { Camera, Cpu } from 'react-feather';
import { MdOutlineImportantDevices } from "react-icons/md";
import { PiDevicesBold } from "react-icons/pi";
import { GrServices } from "react-icons/gr";

export default [
    {
        header: 'Devices',
        action: 'manage',
        resource: 'DEVICES',
    },
    {
        id: '/devices/all-devices',
        title: 'AI Devices',
        action: 'manage',
        resource: 'DEVICES',
        icon: <Cpu size={20} />,
        navLink: '/devices/all-devices'
    },
    {
        id: '/devices/device-admin',
        title: 'Devices Admin',
        action: 'manage',
        resource: 'DEVICES',
        icon: <MdOutlineImportantDevices size={20} />,
        navLink: '/devices/device-admin'
    },
    {
        id: '/devices/ai-services',
        title: 'AI Services',
        action: 'manage',
        resource: 'DEVICES',
        icon: <GrServices size={20} />,
        navLink: '/devices/ai-services'
    },
    {
        id: '/devices/camera',
        title: 'Camera',
        action: 'manage',
        resource: 'CAMERA',
        icon: <Camera size={20} />,
        navLink: '/devices/camera'
    },
    {
        id: '/devices/device-group',
        title: 'Device Group',
        action: 'manage',
        resource: 'DEVICES',
        icon: <PiDevicesBold size={20} />,
        navLink: '/devices/device-group'
    }
]
