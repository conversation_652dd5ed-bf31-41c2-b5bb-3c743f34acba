// ** Icons Import
import { Circle, User } from 'react-feather';

export default [
    {
        id: '/guest',
        title: 'Guest',
        action: 'read',
        resource: 'USER_GUESTS',
        icon: <User size={20} />,
        children: [
            {
                id: '/guest/guests',
                title: 'Guests',
                action: 'manage',
                resource: 'USER_GUESTS',
                icon: <Circle size={20} />,
                navLink: '/guest/guests'
            },
            {
                id: '/guest/history',
                title: 'History',
                action: 'read',
                resource: 'USER_GUESTS',
                icon: <Circle size={12} />,
                navLink: '/guest/history'
            },
            // {
            //     id: '/guest/access',
            //     title: 'Access',
            //     action: 'manage',
            //     resource: 'USER_GUESTS',
            //     icon: <Circle size={20} />,
            //     navLink: '/guest/access'
            // }
        ]
    },
]
