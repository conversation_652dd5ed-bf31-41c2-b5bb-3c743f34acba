import { Circle } from 'react-feather';
import { RiUserSearchLine } from "react-icons/ri";

export default [
    {
        header: 'Security & AI',
        action: 'manage',
        resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
    },
    {
        id: '/security/human-search',
        title: 'Human Search',
        resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
        icon: <RiUserSearchLine size={20} />,
        children: [
            {
                id: '/security/human-search/mct',
                title: 'Human Tracking',
                resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
                icon: <Circle size={20} />,
                navLink: '/security/human-search/mct'
            },
            {
                id: '/security/human-search/human-uniform',
                title: 'Uniform',
                resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
                icon: <Circle size={20} />,
                navLink: '/security/human-search/human-uniform'
            },
            {
                id: '/security/human-search/human-action',
                title: 'Human Action',
                resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
                icon: <Circle size={20} />,
                navLink: '/security/human-search/human-action'
            },
            {
                id: '/security/human-search/search-feature-image',
                title: 'Smart Search',
                resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
                icon: <Circle size={20} />,
                navLink: '/security/human-search/search-feature-image'
            },
            {
                id: '/security/human-search/social-search',
                title: 'Social Search',
                resource: 'SEARCH_FEATURE_IMAGE_SERVICE',
                icon: <Circle size={20} />,
                navLink: '/security/human-search/social-search'
            },
        ]
    },
]
