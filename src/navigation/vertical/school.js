import { Circle } from 'react-feather';
import { IoSchoolOutline } from "react-icons/io5";

export default [
    {
        id: '/school',
        title: 'School',
        action: 'manage',
        resource: 'STUDENT',
        icon: <IoSchoolOutline size={20} />,
        children: [
            {
                id: '/school/all-students',
                title: 'All Students',
                action: 'manage',
                resource: 'STUDENT',
                icon: <Circle size={20} />,
                navLink: '/school/all-students'
            }
        ]
    },
]
