import { Circle } from 'react-feather';
import { GoOrganization } from "react-icons/go";

export default [
    {
        id: '/organization',
        title: 'Organization',
        action: 'manage',
        resource: 'USERS',
        icon: <GoOrganization size={20} />,
        children: [
            {
                id: '/organization/all-org',
                title: 'All Organization',
                action: 'manage',
                resource: 'ORGANIZATION',
                icon: <Circle size={20} />,
                navLink: '/organization/all-org'
            },
            {
                id: '/organization/tree-view',
                title: 'Tree View',
                action: 'manage',
                resource: 'ORGANIZATION',
                icon: <Circle size={20} />,
                navLink: '/organization/tree-view'
            },
            {
                id: '/organization/company-management',
                title: 'Company MGMT',
                action: 'manage',
                resource: 'ORGANIZATION',
                icon: <Circle size={20} />,
                navLink: '/organization/company-management'
            },
            {
                id: '/organization/company-view',
                title: 'Company View',
                action: 'manage',
                resource: 'ORGANIZATION',
                icon: <Circle size={20} />,
                navLink: '/organization/company-view'
            },
            {
                id: '/organization/organization-admin',
                title: 'Admin',
                action: 'manage',
                resource: 'ORGANIZATION_USER',
                icon: <Circle size={20} />,
                navLink: '/organization/organization-admin'
            },
            {
                id: '/organization/location',
                title: 'Work Location',
                action: 'manage',
                resource: 'ORGANIZATION',
                icon: <Circle size={20} />,
                navLink: '/organization/location'
            },
        ]
    },
]
