import { Circle, UserCheck } from 'react-feather';

export default [
    {
        id: '/attendant',
        title: 'Attendant',
        action: 'manage',
        resource: 'ATT',
        icon: <UserCheck size={20} />,
        children: [
            {
                id: '/attendant/att',
                title: 'By Location',
                action: 'manage',
                resource: 'ATT',
                icon: <Circle size={20} />,
                navLink: '/attendant/att'
            },
            // {
            //     id: '/attendant/history',
            //     title: 'History',
            //     action: 'manage',
            //     resource: 'ATT',
            //     icon: <Circle size={20} />,
            //     navLink: '/attendant/history'
            // },
            {
                id: '/attendant/by-day',
                title: 'By Day',
                action: 'manage',
                resource: 'ATT',
                icon: <Circle size={20} />,
                navLink: '/attendant/by-day'
            },
            // {
            //     id: '/attendant/att-time',
            //     title: 'Attendant Time',
            //     action: 'manage',
            //     resource: 'ATT',
            //     icon: <Circle size={12} />,
            //     navLink: '/attendant/att-time'
            // },
            // {
            //     id: '/attendant/user-config',
            //     title: 'User Config',
            //     action: 'manage',
            //     resource: 'ATT',
            //     icon: <Circle size={20} />,
            //     navLink: '/attendant/user-config'
            // }
        ]
    },
]
