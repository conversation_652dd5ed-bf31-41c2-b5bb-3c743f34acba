// ** Icons Import
import { Circle, Calendar } from 'react-feather';

export default [
    {
        id: '/shift-management',
        title: 'Shift Management',
        action: 'manage',
        resource: 'USERS',
        icon: <Calendar size={20} />,
        children: [
            {
                id: '/shift-management/all-shift-type',
                title: 'All Shift Type',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/shift-management/all-shift-type'
            },
            {
                id: '/shift-management/shift-assignment',
                title: 'Shift Assignment',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={12} />,
                navLink: '/shift-management/shift-assignment'
            },
            {
                id: '/shift-management/admin-shift-explanation',
                title: 'ShiftExplanation Ad',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={12} />,
                navLink: '/shift-management/admin-shift-explanation'
            },
            {
                id: '/shift-management/shift-report',
                title: 'Shift Report',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/shift-management/shift-report'
            },
            {
                id: '/shift-management/sf4c',
                title: 'SF4C',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/shift-management/sf4c'
            },
        ]
    },
]
