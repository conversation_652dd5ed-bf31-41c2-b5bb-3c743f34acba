import { Circle } from 'react-feather';
import { TbParking } from "react-icons/tb";

export default [
    {
        id: '/security/parking',
        title: 'IParking',
        resource: 'I_PARKING_VIEWER',
        icon: <TbParking size={20} />,
        children: [
            // {
            //     id: '/security/parking/parking-dashboard',
            //     title: 'IParking Dashboard',
            //     resource: 'I_PARKING_ADMIN',
            //     icon: <Circle size={20} />,
            //     navLink: '/security/parking/parking-dashboard'
            // },

            // {
            //     id: '/security/parking/parking-config',
            //     title: 'IParking Config',
            //     resource: 'I_PARKING_ADMIN',
            //     icon: <Circle size={20} />,
            //     navLink: '/security/parking/parking-config'
            // },
            // {
            //     id: '/security/parking/parking-history',
            //     title: 'IParking History',
            //     resource: 'I_PARKING_VIEWER',
            //     icon: <Circle size={20} />,
            //     navLink: '/security/parking/parking-history'
            // },
            // {
            //     id: '/security/parking/parking-result',
            //     title: 'IParking Result',
            //     resource: 'I_PARKING_VIEWER',
            //     icon: <Circle size={20} />,
            //     navLink: '/security/parking/parking-result'
            // },
            {
                id: '/security/iparking-integration/iparking-config',
                title: 'Config',
                resource: 'IPARKING_INTEGRATION',
                icon: <Circle size={20} />,
                navLink: '/security/iparking-integration/iparking-config'
            },
            {
                id: '/security/parking/parking-license',
                title: 'Vehicle Allowed',
                resource: 'I_PARKING_ADMIN',
                icon: <Circle size={20} />,
                navLink: '/security/parking/parking-license'
            },
            {
                id: '/security/iparking-integration/iparking-general',
                title: 'General',
                resource: 'IPARKING_INTEGRATION',
                icon: <Circle size={20} />,
                navLink: '/security/iparking-integration/iparking-general'
            },
            {
                id: '/security/iparking-integration/iparking-sync-data',
                title: 'Sync Data',
                resource: 'IPARKING_INTEGRATION',
                icon: <Circle size={20} />,
                navLink: '/security/iparking-integration/iparking-sync-data'
            },
            {
                id: '/security/iparking-integration/iparking-vehicle',
                title: 'Vehicle',
                resource: 'IPARKING_INTEGRATION',
                icon: <Circle size={20} />,
                navLink: '/security/iparking-integration/iparking-vehicle'
            },
        ]
    }
]
