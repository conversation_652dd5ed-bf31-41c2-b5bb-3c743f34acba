import { Bell, Edit2, Save } from 'react-feather';
import { MdOutlineBackup } from "react-icons/md";
import { LuServerCog } from "react-icons/lu";
import { SiMqtt } from "react-icons/si";
import { LuTimerReset } from "react-icons/lu";

export default [
    {
        header: 'Server Settings',
        action: 'manage',
        resource: 'SERVER_CONFIG',
    },
    {
        id: '/settings/ott',
        title: 'OTT Config',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <Bell size={20} />,
        navLink: '/settings/ott'
    },
    {
        id: '/settings/server-settings',
        title: 'Server Config',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <LuServerCog size={20} />,
        navLink: '/settings/server-settings'
    },
    {
        id: '/settings/mqtt-add-in',
        title: 'MQTT Config',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <SiMqtt size={20} />,
        navLink: '/settings/mqtt-add-in'
    },
    {
        id: '/settings/system-log',
        title: 'System Log',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <Edit2 size={12} />,
        navLink: '/settings/system-log'
    },
    {
        id: '/settings/cron-job',
        title: 'Cron Job',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <LuTimerReset size={20} />,
        navLink: '/settings/cron-job'
    },
    {
        id: '/settings/data-backup',
        title: 'Data Backup',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <Save size={20} />,
        navLink: '/settings/data-backup'
    },
    {
        id: '/settings/manual-import',
        title: 'Manual Import',
        action: 'manage',
        resource: 'SERVER_CONFIG',
        icon: <MdOutlineBackup size={20} />,
        navLink: '/settings/manual-import'
    },
]
