import { Circle } from 'react-feather';
import { TbFaceId } from "react-icons/tb";

export default [
    {
        id: '/faceid',
        title: 'Face ID',
        action: 'read',
        resource: 'TIMEKEEPING',
        icon: <TbFaceId size={20} />,
        children: [
            {
                id: '/faceid/check-in-out-data',
                title: 'History',
                action: 'read',
                resource: 'TIMEKEEPING',
                icon: <Circle size={20} />,
                navLink: '/faceid/check-in-out-data'
            },
            {
                id: '/faceid/attendant',
                title: 'Attendant',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/faceid/attendant'
            },
            {
                id: '/faceid/timekeeping_unknown',
                title: 'Unknown',
                action: 'read',
                resource: 'ORPHAN_RESULTS',
                icon: <Circle size={20} />,
                navLink: '/faceid/timekeeping_unknown'
            },
            {
                id: '/faceid/reid',
                title: 'Re ID',
                action: 'read',
                resource: 'ORPHAN_RESULTS',
                icon: <Circle size={20} />,
                navLink: '/faceid/reid'
            },
            {
                id: '/faceid/age-emotion',
                title: 'Age & Emotion',
                action: 'read',
                resource: 'ORPHAN_RESULTS',
                icon: <Circle size={20} />,
                navLink: '/faceid/age-emotion'
            },
            {
                id: '/faceid/report',
                title: 'Report',
                action: 'manage',
                resource: 'TIMEKEEPING',
                icon: <Circle size={20} />,
                navLink: '/faceid/report'
            },
            {
                id: '/faceid/face-intergration',
                title: 'Integration',
                action: 'manage',
                resource: 'TIMEKEEPING',
                icon: <Circle size={20} />,
                navLink: '/faceid/face-intergration'
            },
        ]
    },
]
