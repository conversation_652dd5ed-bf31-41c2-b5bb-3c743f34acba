// ** Icons Import
import { Home, Circle } from 'react-feather';

export default [
    {
        id: '/dashboards',
        title: 'Dashboards',
        action: 'read',
        resource: 'DASHBOARD',
        icon: <Home size={20} />,
        children: [
            {
                id: '/dashboards/events',
                title: 'Events',
                action: 'read',
                resource: 'DASHBOARD',
                icon: <Circle size={20} />,
                navLink: '/dashboards/events'
            },
            {
                id: '/security/iparking-integration/iparking-dashboard',
                title: 'Vehicle',
                resource: 'IPARKING_INTEGRATION',
                icon: <Circle size={20} />,
                navLink: '/security/iparking-integration/iparking-dashboard'
            },
            {
                id: '/dashboards/security',
                title: 'Security',
                resource: 'DASHBOARD',
                icon: <Circle size={20} />,
                navLink: '/dashboards/security'
            },
            {
                id: '/dashboards/show-cases',
                title: 'Show Case',
                action: 'read',
                resource: 'DASHBOARD',
                icon: <Circle size={12} />,
                navLink: '/dashboards/show-cases'
            }
        ]
    }
]
