// ** Icons Import
import { Circle, Smartphone } from 'react-feather';

export default [
    {
        id: '/remote-faceid',
        title: 'Mobile Face ID',
        action: 'read',
        resource: 'REMOTE_FACE_RESULT',
        icon: <Smartphone size={20} />,
        children: [
            {
                id: '/remote-faceid/remote-result',
                title: 'Mobile Results',
                action: 'manage',
                resource: 'REMOTE_FACE_RESULT',
                icon: <Circle size={20} />,
                navLink: '/remote-faceid/remote-result'
            },
            {
                id: '/remote-faceid/remote-admin',
                title: 'Mobile Admins',
                action: 'manage',
                resource: 'REMOTE_ADMIN',
                icon: <Circle size={20} />,
                navLink: '/remote-faceid/remote-admin'
            },
            {
                id: '/remote-faceid/remote-approver',
                title: 'Mobile Approvers',
                action: 'manage',
                resource: 'REMOTE_APPROVE',
                icon: <Circle size={20} />,
                navLink: '/remote-faceid/remote-approver'
            },
            {
                id: '/remote-faceid/remote-user',
                title: 'Mobile Users',
                action: 'manage',
                resource: 'REMOTE_USER',
                icon: <Circle size={20} />,
                navLink: '/remote-faceid/remote-user'
            }
        ]
    }
]
