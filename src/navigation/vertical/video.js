import { Circle } from 'react-feather';
import { RxVideo } from "react-icons/rx";
import { BsCameraVideo } from "react-icons/bs";

export default [
    {
        id: '/videos',
        title: 'Videos',
        resource: 'VIDEOS',
        icon: <RxVideo size={20} />,
        children: [
            {
                id: '/videos/video-analytic-group',
                title: 'Video Group',
                resource: 'VIDEOS',
                icon: <Circle size={20} />,
                navLink: '/videos/video-analytic-group'
            },
            {
                id: '/videos/video-analytic',
                title: 'Video Face ReID',
                resource: 'VIDEOS',
                icon: <Circle size={20} />,
                navLink: '/videos/video-analytic'
            },
            {
                id: '/videos/event-video',
                title: 'Event Video',
                resource: 'VIDEOS',
                icon: <Circle size={20} />,
                navLink: '/videos/event-video'
            },
        ]
    },
]
