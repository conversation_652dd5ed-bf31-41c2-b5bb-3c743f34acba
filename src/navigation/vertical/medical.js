import { Circle } from 'react-feather';
import { BsHospital } from "react-icons/bs";

export default [
    {
        id: '/medical',
        title: 'Medical',
        action: 'manage',
        resource: 'PATIENT',
        icon: <BsHospital size={20} />,
        children: [
            {
                id: '/medical/patients',
                title: 'Patients',
                action: 'manage',
                resource: 'PATIENT',
                icon: <Circle size={20} />,
                navLink: '/medical/patients'
            },
            {
                id: '/medical/access',
                title: 'Access',
                action: 'manage',
                resource: 'PATIENT',
                icon: <Circle size={20} />,
                navLink: '/medical/access'
            }
        ]
    },
]
