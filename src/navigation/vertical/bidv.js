import { Circle, Home } from 'react-feather';
import { AiOutlineBank } from "react-icons/ai";

export default [
    {
        id: '/bidv',
        title: 'Violation Dashboard',
        resource: 'SECURITY',
        icon: <Home size={20} />,
        children: [
            {
                id: '/bidv/dashboard',
                title: 'Dashboards',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/bidv/dashboard'
            },
            {
                id: '/bidv/history-detail',
                title: 'History Detail',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/bidv/history-detail'
            },
            {
                id: '/bidv/meeting',
                title: 'Meeting Config',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/bidv/meeting'
            },
            {
                id: '/bidv/meeting-detail',
                title: 'Meeting Detail',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/bidv/meeting-detail'
            },
            {
                id: '/bidv/live-view',
                title: 'Live View',
                resource: 'SECURITY',
                icon: <Circle size={20} />,
                navLink: '/bidv/live-view'
            },
        ]
    }
];