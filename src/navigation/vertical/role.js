import { Circle } from 'react-feather';
import { RiAdminLine } from "react-icons/ri";
import { GoOrganization } from "react-icons/go";
import org from './org'

export default [
    {
        header: 'Users & Shift',
        action: 'read',
        resource: 'USERS',
    },
    ...org,
    {
        id: '/roles',
        title: 'Admin',
        action: 'manage',
        resource: 'USERS',
        icon: <RiAdminLine size={20} />,
        children: [
            {
                id: '/roles/role',
                title: 'User Roles',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={12} />,
                navLink: '/roles/role'
            },
            {
                id: '/roles/permission',
                title: 'Permission',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/roles/permission'
            },
        ]
    },

]
