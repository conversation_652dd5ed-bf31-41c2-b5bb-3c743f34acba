// ** Icons Import
import { Users, Circle } from 'react-feather';

export default [
    {
        id: '/urs',
        title: 'User',
        action: 'read',
        resource: 'USERS',
        icon: <Users size={20} />,
        children: [
            {
                id: '/urs/all-users',
                title: 'All Users',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/urs/all-users'
            },
            {
                id: '/urs/ekyc-users',
                title: 'eKYC Users',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/urs/ekyc-users'
            },
            {
                id: '/urs/vip',
                title: 'VIP Users',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/urs/vip'
            },
            {
                id: '/urs/black-list',
                title: 'Black List',
                action: 'manage',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/urs/black-list'
            },
            {
                id: '/urs/time-shift',
                title: 'Timeshift',
                action: 'read',
                resource: 'SHIFT_USER',
                icon: <Circle size={20} />,
                navLink: '/urs/time-shift'
            },
            {
                id: '/urs/payroll',
                title: 'Payroll',
                action: 'read',
                resource: 'SHIFT_USER',
                icon: <Circle size={20} />,
                navLink: '/urs/payroll'
            },
            {
                id: '/urs/import-user',
                title: 'Import',
                action: 'read',
                resource: 'USERS',
                icon: <Circle size={20} />,
                navLink: '/urs/import-user'
            },
            {
                id: '/urs/shift-explanation',
                title: 'Shift Explanation',
                action: 'read',
                resource: 'SHIFT_EXPLANATION',
                icon: <Circle size={20} />,
                navLink: '/urs/shift-explanation'
            },

            // {
            //     id: '/urs/feedback',
            //     title: 'Feedback',
            //     action: 'read',
            //     resource: 'USER_FEEDBACK',
            //     icon: <Circle size={20} />,
            //     navLink: '/urs/feedback'
            // },

        ]
    },
]
