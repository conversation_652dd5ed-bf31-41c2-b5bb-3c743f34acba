import mock from '../mock'

// Avatar Images
import avatar3 from '@src/assets/images/avatars/3-small.png'
import avatar11 from '@src/assets/images/portrait/small/avatar-s-11.jpg'

const data = {
  accountSetting: {
    general: {
      avatar: avatar11,
      username: 'johndo<PERSON>',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      company: 'PIXINVENT'
    },
    info: {
      bio: '',
      dob: null,
      country: 'USA',
      website: '',
      phone: **********
    },
    social: {
      socialLinks: {
        twitter: 'https://www.twitter.com',
        facebook: '',
        google: '',
        linkedIn: 'https://www.linkedin.com',
        instagram: '',
        quora: ''
      },
      connections: {
        twitter: {
          profileImg: avatar11,
          id: 'johndoe'
        },
        google: {
          profileImg: avatar3,
          id: 'luraweber'
        },
        facebook: {},
        github: {}
      }
    },
    notification: {
      commentOnArticle: true,
      answerOnForm: true,
      followMe: false,
      newAnnouncements: true,
      productUpdates: true,
      blogDigest: false
    }
  }
}

mock.onGet('/account-setting/data').reply(() => [200, data.accountSetting])
