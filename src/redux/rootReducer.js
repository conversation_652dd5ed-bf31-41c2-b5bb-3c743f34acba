import navbar from './navbar'
import layout from './layout'
import auth from './authentication'

import shiftAssign from '@src/views/shift-management/shift-assignment/store'
import userReport from '@src/views/shift-management/shift-report/store'
import timeShift from '@src/views/urs/time-shift/store'
import remoteRecognizeUser from '@src/views/remote-faceid/remote-user/store'
import remoteApprover from '@src/views/remote-faceid/remote-approver/store'
import remoteAdmin from '@src/views/remote-faceid/remote-admin/store'
import allOrgTable from '@src/views/organization/all-org/store'
import telegramOttConfig from '@src/views/settings/ott/store'
import securityEvents from '@src/views/security/security-events/store'
import user from '@src/views/urs/all-users/store'
import vip from '@src/views/urs/vip/store'
import blackList from '@src/views/urs/black-list/store'
import systemLog from '@src/views/settings/system-log/store'
import smartSearch from '@src/views/security/human-search/search-feature-image/store'
import humanTracking from '@src/views/security/human-search/mct/store'
import traffic from '@src/views/security/vehicle/traffic/store'
import vehicleSearch from '@src/views/security/vehicle/vehicle-search/store'
import device from '@src/views/devices/ai-devices/store'
import aiServices from '@src/views/devices/ai-services/store'
import deviceAdmin from '@src/views/devices/device-admin/store'
import companyView from '@src/views/organization/company-view/store'
import iparking from '@src/views/security/parking/parking-config/store'
import iparkingResult from '@src/views/security/parking/parking-result/store'
import iparkingLicense from '@src/views/security/parking/parking-license/store'
import iparkingHistory from '@src/views/security/parking/parking-history/store'
import iparkingDashboard from '@src/views/security/parking/parking-dashboard/store'
import ekycUsers from '@src/views/urs/ekyc-users/store'
import attTime from '@src/views/attendant/att-time/store'
import att from '@src/views/attendant/att/store'
import timekeepingUnknown from '@src/views/faceid/timekeeping-unknown/store'
import guest from '@src/views/guest/guests/store'
import guestHistory from '@src/views/guest/history/store'
import faceResult from '@src/views/faceid/face-result/store'
import accessControl from '@src/views/security/access-control/store'
import deviceGroup from '@src/views/devices/device-group/store'
import dataBackup from '@src/views/settings/data-backup/store'
import remoteResult from '@src/views/remote-faceid/remote-result/store'
import acBeta from '@src/views/security/access-control-beta/store'
import acMb from '@src/views/security/access-control-mb/store'
import faceIntergration from '@src/views/faceid/face-intergration/store'
import attByDay from '@src/views/attendant/by-day/store'
import faceHistory from '@src/views/faceid/check-in-out-data/store'
import faceReport from '@src/views/faceid/report/store'
import camera from '@src/views/devices/camera/store'
import userRole from '@src/views/roles/role/store'
import permission from '@src/views/roles/permission/store'
import guestAccess from '@src/views/guest/access/store'
import payroll from '@src/views/urs/payroll/store'
import acOpenLog from '@src/views/security/door-log/store'
import serverSettings from '@src/views/settings/server-settings/store'
import sf4c from '@src/views/shift-management/sf4c/store'
import shiftType from '@src/views/shift-management/all-shift-type/store'
import shiftExplanAdmin from '@src/views/shift-management/admin-shift-explanation/store'
import vms from '@src/views/security/event-video/store'
import showCase from '@src/views/dashboards/show-cases/store'
import acAlert from '@src/views/security/access-control-alert/store'
import viewVideo from '@src/views/Video/store'
import companyMgmt from '@src/views/organization/company-management/store'
import alertRule from '@src/views/security/alert-rule/store'
import humanAction from '@src/views/security/human-search/human-action/store'
import importUser from '@src/views/urs/import-user/store'
import shiftExplan from '@src/views/urs/shift-explanation/store'
import companyAdmin from '@src/views/organization/organization-admin/store'
import patient from '@src/views/medical/patients/store'
import medicalAccess from '@src/views/medical/access/store'
import attendant from '@src/views/faceid/attendant/store'
import humanUniform from '@src/views/security/human-search/human-uniform/store'
import bidvMeeting from '@src/views/security/bidv/meeting/store'
import bidvDashboard from '@src/views/security/bidv/dashboard/store'
import bidvDetail from '@src/views/security/bidv/history-detail/store'
import bidvMeetingDetail from '@src/views/security/bidv/meeting-detail/store'
import location from '@src/views/organization/location/store'
import liveView from '@src/views/security/live-view-video/store'
import ageEmotion from '@src/views/faceid/age-emotion/store'
import videoAnalytic from '@src/views/videos/video-analytic/store'
import videoAnaGroup from '@src/views/videos/video-analytic-group/store'
import mqtt from '@src/views/settings/mqtt-add-in/store'
import iParkingDashboards from '@src/views/security/iparking-integration/iparking-dashboard/store'
import iParkingGeneral from '@src/views/security/iparking-integration/iparking-general/store'
import iParkingVehicle from '@src/views/security/iparking-integration/iparking-vehicle/store'
import iParkingConfig from '@src/views/security/iparking-integration/iparking-config/store'
import iParkingSyncData from '@src/views/security/iparking-integration/iparking-sync-data/store'
import reId from '@src/views/faceid/reid/store'
import cronjob from '@src/views/settings/cron-job/store'
import securityReport from '@src/views/dashboards/security/store'

const rootReducer = {
    auth,
    navbar,
    layout,
    shiftAssign,
    userReport,
    timeShift,
    remoteRecognizeUser,
    remoteApprover,
    remoteAdmin,
    allOrgTable,
    telegramOttConfig,
    securityEvents,
    user,
    vip,
    blackList,
    systemLog,
    smartSearch,
    humanTracking,
    traffic,
    vehicleSearch,
    device,
    aiServices,
    deviceAdmin,
    companyView,
    iparking,
    iparkingResult,
    iparkingLicense,
    iparkingHistory,
    iparkingDashboard,
    ekycUsers,
    attTime,
    att,
    timekeepingUnknown,
    guest,
    guestHistory,
    faceResult,
    accessControl,
    deviceGroup,
    dataBackup,
    remoteResult,
    acBeta,
    acMb,
    faceIntergration,
    attByDay,
    faceHistory,
    faceReport,
    camera,
    userRole,
    permission,
    guestAccess,
    payroll,
    acOpenLog,
    serverSettings,
    sf4c,
    shiftType,
    shiftExplanAdmin,
    vms,
    showCase,
    acAlert,
    viewVideo,
    companyMgmt,
    alertRule,
    humanAction,
    importUser,
    shiftExplan,
    companyAdmin,
    patient,
    medicalAccess,
    attendant,
    humanUniform,
    bidvMeeting,
    bidvDashboard,
    bidvDetail,
    bidvMeetingDetail,
    location,
    liveView,
    ageEmotion,
    videoAnalytic,
    videoAnaGroup,
    mqtt,
    iParkingDashboards,
    iParkingGeneral,
    iParkingVehicle,
    iParkingConfig,
    iParkingSyncData,
    reId,
    cronjob,
    securityReport,
};

export default rootReducer;