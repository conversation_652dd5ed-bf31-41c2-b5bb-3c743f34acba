// ** Redux Imports
import { createSlice } from '@reduxjs/toolkit'

// ** UseJWT import to get config
import useJwt from '@src/auth/jwt/useJwt'

const config = useJwt.jwtConfig

const initialUser = () => {
    const item = window.localStorage.getItem('userData')
    //** Parse stored json or if none return initialValue
    return item ? JSON.parse(item) : {}
}

const initialSessionModal = () => {
    const item = window.localStorage.getItem('showSessionModal')
    //** Parse stored json or if none return initialValue
    return item ? JSON.parse(item) : false
}

export const authSlice = createSlice({
    name: 'authentication',
    initialState: {
        userData: initialUser(),
        sessionModal: initialSessionModal(),
    },
    reducers: {
        handleLogin: (state, action) => {
            state.userData = action.payload
            state[config.storageTokenKeyName] = action.payload[config.storageTokenKeyName]
            state[config.storageRefreshTokenKeyName] = action.payload[config.storageRefreshTokenKeyName]
            localStorage.setItem('userData', JSON.stringify(action.payload))
            localStorage.setItem(config.storageTokenKeyName, JSON.stringify(action.payload.accessToken))
            localStorage.setItem(config.storageRefreshTokenKeyName, JSON.stringify(action.payload.refreshToken))
            if (action.payload.domain) {
                localStorage.setItem('domain', action.payload.domain)
            }
            state.sessionModal = false
            localStorage.setItem('showSessionModal', JSON.stringify(false))
        },
        handleLogout: (state, action) => {
            state.userData = {}
            state[config.storageTokenKeyName] = null
            state[config.storageRefreshTokenKeyName] = null
            localStorage.removeItem('userData')
            localStorage.removeItem(config.storageTokenKeyName)
            localStorage.removeItem(config.storageRefreshTokenKeyName)
            localStorage.removeItem('domain')
            state.sessionModal = false
            localStorage.setItem('showSessionModal', JSON.stringify(false))
        },
        handleSessionModal: (state, action) => {
            state.sessionModal = action.payload
            localStorage.setItem('showSessionModal', JSON.stringify(action.payload))
        },
    }
});

export const { handleLogin, handleLogout, handleSessionModal } = authSlice.actions;

export default authSlice.reducer;